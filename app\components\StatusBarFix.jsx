import React from 'react';
import { View, StatusBar, StyleSheet, Platform } from 'react-native';
import Constants from 'expo-constants';

const STATUSBAR_HEIGHT = Platform.OS === 'ios' ? Constants.statusBarHeight : StatusBar.currentHeight;

const StatusBarFix = ({ backgroundColor, barStyle = "dark-content" }) => {
  return (
    <View style={[styles.statusBar, { backgroundColor }]}>
      <StatusBar translucent backgroundColor={backgroundColor} barStyle={barStyle} />
    </View>
  );
};

const styles = StyleSheet.create({
  statusBar: {
    height: STATUSBAR_HEIGHT,
  },
});

export default StatusBarFix;
