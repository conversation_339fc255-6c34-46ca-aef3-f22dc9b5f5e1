import { StyleSheet, Text, View } from 'react-native';

import { Slot as ExpoRouterSlot } from 'expo-router';

/**
 * Slot component that wraps the Expo Router Slot
 * This allows for content to be injected into a specific area of a layout
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render
 * @returns {React.ReactNode} - Rendered component
 */
const Slot = ({ children }) => {
  return (
    <View style={styles.container}>
      {children ? children : <ExpoRouterSlot />}
    </View>
  );
};

export default Slot;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
