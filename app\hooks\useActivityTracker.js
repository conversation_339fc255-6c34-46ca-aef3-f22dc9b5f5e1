import { useEffect } from 'react';
import { useUser } from '../context/UserContext';
import { AppState } from 'react-native';

/**
 * Custom hook to track user activity and update last interaction time
 * This helps maintain the session timeout functionality
 */
export const useActivityTracker = () => {
  const { userData, updateLastInteraction } = useUser();

  useEffect(() => {
    // Only track activity if user is logged in
    if (!userData.isLoggedIn) return;

    // Track app state changes (foreground/background)
    const handleAppStateChange = (nextAppState) => {
      if (nextAppState === 'active') {
        // User brought app to foreground - update activity
        updateLastInteraction();
      }
    };

    // Add app state listener
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Cleanup
    return () => {
      subscription?.remove();
    };
  }, [userData.isLoggedIn, updateLastInteraction]);

  // Return function to manually track activity
  const trackActivity = () => {
    if (userData.isLoggedIn) {
      updateLastInteraction();
    }
  };

  return { trackActivity };
};

export default useActivityTracker;
