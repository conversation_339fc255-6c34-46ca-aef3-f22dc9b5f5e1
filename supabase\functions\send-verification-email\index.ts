import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { SmtpClient } from "https://deno.land/x/smtp@v0.7.0/mod.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { email, verificationCode } = await req.json()

    if (!email || !verificationCode) {
      return new Response(
        JSON.stringify({ error: 'Email and verification code are required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Gmail SMTP configuration
    const client = new SmtpClient()
    
    await client.connectTLS({
      hostname: "smtp.gmail.com",
      port: 587,
      username: "<EMAIL>",
      password: "snuz bhoc nsax nnhj", // Your app password
    })

    // Email content using your template design
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #6B9142; margin: 0;">MentalEase</h1>
          <p style="color: #666; margin: 5px 0;">Your Mental Health Companion</p>
        </div>

        <h2 style="color: #333;">Welcome to MentalEase!</h2>
        <p style="color: #555; line-height: 1.6;">
          Thank you for signing up. Please verify your email address to continue.
        </p>

        <div style="background-color: #f5f5f5; padding: 20px; border-radius: 8px; text-align: center; margin: 20px 0;">
          <h3 style="margin: 0; color: #333;">Your Verification Code:</h3>
          <h1 style="color: #6B9142; font-size: 32px; letter-spacing: 4px; margin: 10px 0;">${verificationCode}</h1>
        </div>

        <p style="color: #555;">Enter this code in the MentalEase app to verify your email address.</p>
        <p style="color: #666; font-size: 14px;">This code will expire in 10 minutes for security reasons.</p>

        <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
        <p style="color: #999; font-size: 12px;">
          If you didn't sign up for MentalEase, please ignore this email.
        </p>
      </div>
    `

    await client.send({
      from: "MentalEase <<EMAIL>>",
      to: email,
      subject: "MentalEase - Email Verification Code",
      content: htmlContent,
      html: htmlContent,
    })

    await client.close()

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Verification email sent successfully' 
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error sending email:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to send email', 
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
