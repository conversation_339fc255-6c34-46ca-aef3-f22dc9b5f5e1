-- Create psychometricians table linked to users table
-- Run this in your Supabase SQL Editor

-- Step 1: Create the psychometricians table
CREATE TABLE IF NOT EXISTS psychometricians (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    specialty VARCHAR(255) DEFAULT 'Psychometrician',
    experience VARCHAR(100) DEFAULT '5+ years',
    rating DECIMAL(2,1) DEFAULT 4.8,
    image VARCHAR(10) DEFAULT '👨‍⚕️',
    available BOOLEAN DEFAULT TRUE,
    bio TEXT,
    qualifications TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one psychometrician record per user
    UNIQUE(user_id),
    UNIQUE(email)
);

-- Step 2: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_psychometricians_user_id ON psychometricians(user_id);
CREATE INDEX IF NOT EXISTS idx_psychometricians_email ON psychometricians(email);
CREATE INDEX IF NOT EXISTS idx_psychometricians_available ON psychometricians(available);

-- Step 3: Create function to auto-update updated_at timestamp
CREATE OR REPLACE FUNCTION update_psychometricians_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Step 4: Create trigger for auto-updating updated_at
DROP TRIGGER IF EXISTS update_psychometricians_updated_at ON psychometricians;
CREATE TRIGGER update_psychometricians_updated_at
    BEFORE UPDATE ON psychometricians
    FOR EACH ROW
    EXECUTE FUNCTION update_psychometricians_updated_at();

-- Step 5: Function to automatically create psychometrician record when user role is set to 'psychometrician'
CREATE OR REPLACE FUNCTION sync_psychometrician_from_user()
RETURNS TRIGGER AS $$
BEGIN
    -- If user role is changed to 'psychometrician', create psychometrician record
    IF NEW.role = 'psychometrician' AND (OLD.role IS NULL OR OLD.role != 'psychometrician') THEN
        INSERT INTO psychometricians (user_id, name, email, created_at, updated_at)
        VALUES (NEW.id, NEW.name, NEW.email, NOW(), NOW())
        ON CONFLICT (user_id) DO UPDATE SET
            name = NEW.name,
            email = NEW.email,
            updated_at = NOW();
    END IF;
    
    -- If user role is changed from 'psychometrician', optionally disable the record
    IF OLD.role = 'psychometrician' AND NEW.role != 'psychometrician' THEN
        UPDATE psychometricians 
        SET available = FALSE, updated_at = NOW()
        WHERE user_id = NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Step 6: Create trigger to sync psychometricians when user role changes
DROP TRIGGER IF EXISTS sync_psychometrician_trigger ON users;
CREATE TRIGGER sync_psychometrician_trigger
    AFTER UPDATE OF role ON users
    FOR EACH ROW
    EXECUTE FUNCTION sync_psychometrician_from_user();

-- Step 7: Insert existing psychometrician users into psychometricians table
INSERT INTO psychometricians (user_id, name, email, created_at, updated_at)
SELECT 
    id,
    COALESCE(name, email) as name,
    email,
    created_at,
    updated_at
FROM users 
WHERE role = 'psychometrician'
ON CONFLICT (user_id) DO UPDATE SET
    name = EXCLUDED.name,
    email = EXCLUDED.email,
    updated_at = NOW();

-- Step 8: Add comments for documentation
COMMENT ON TABLE psychometricians IS 'Psychometricians linked to users table for appointment booking';
COMMENT ON COLUMN psychometricians.user_id IS 'Foreign key to users table (integer ID)';
COMMENT ON COLUMN psychometricians.name IS 'Display name for the psychometrician';
COMMENT ON COLUMN psychometricians.email IS 'Email address (synced from users table)';
COMMENT ON COLUMN psychometricians.specialty IS 'Professional specialty or focus area';
COMMENT ON COLUMN psychometricians.experience IS 'Years of experience description';
COMMENT ON COLUMN psychometricians.rating IS 'Average rating (1.0 to 5.0)';
COMMENT ON COLUMN psychometricians.image IS 'Emoji or image identifier';
COMMENT ON COLUMN psychometricians.available IS 'Whether accepting new appointments';

-- Step 9: Grant necessary permissions
GRANT ALL ON psychometricians TO authenticated;
GRANT USAGE ON SEQUENCE psychometricians_id_seq TO authenticated;

-- Step 10: Verification query
SELECT 
    p.id,
    p.name,
    p.email,
    p.specialty,
    p.available,
    u.role
FROM psychometricians p
JOIN users u ON p.user_id = u.id
ORDER BY p.created_at DESC;
