-- Create mood_entries table for storing daily mood tracking data
-- Run this in your Supabase SQL Editor

CREATE TABLE IF NOT EXISTS mood_entries (
    id SERIAL PRIMARY KEY,
    user_email VARCHAR(255) NOT NULL,
    mood_value INTEGER NOT NULL CHECK (mood_value >= 0 AND mood_value <= 4),
    mood_label VARCHAR(20) NOT NULL,
    mood_emoji VARCHAR(10) NOT NULL,
    entry_date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one mood entry per user per date
    UNIQUE(user_email, entry_date)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_mood_entries_user_email ON mood_entries(user_email);
CREATE INDEX IF NOT EXISTS idx_mood_entries_date ON mood_entries(entry_date);
CREATE INDEX IF NOT EXISTS idx_mood_entries_user_date ON mood_entries(user_email, entry_date);

-- Add comments for documentation
COMMENT ON TABLE mood_entries IS 'Daily mood tracking entries for users';
COMMENT ON COLUMN mood_entries.mood_value IS 'Mood scale: 0=Sad, 1=Meh, 2=Okay, 3=Good, 4=Great';
COMMENT ON COLUMN mood_entries.mood_label IS 'Text label for mood (Sad, Meh, Okay, Good, Great)';
COMMENT ON COLUMN mood_entries.mood_emoji IS 'Emoji representation of mood';
COMMENT ON COLUMN mood_entries.entry_date IS 'Date of mood entry (YYYY-MM-DD format)';

-- Disable Row Level Security for production use with current auth system
-- Data separation is handled at the application layer using email filtering
ALTER TABLE mood_entries DISABLE ROW LEVEL SECURITY;

-- Grant necessary permissions for the application to work
GRANT ALL ON mood_entries TO authenticated;
GRANT ALL ON mood_entries TO anon;

-- Grant sequence permissions for auto-incrementing ID
GRANT USAGE, SELECT ON SEQUENCE mood_entries_id_seq TO authenticated;
GRANT USAGE, SELECT ON SEQUENCE mood_entries_id_seq TO anon;

-- Data separation is ensured by:
-- 1. Application-level filtering by user_email in all queries
-- 2. Unique constraint preventing duplicate entries per user per date
-- 3. Input validation in the mood-service.js functions
