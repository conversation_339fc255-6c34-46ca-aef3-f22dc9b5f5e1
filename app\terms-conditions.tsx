import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import StandardHeader from './components/StandardHeader';

export default function TermsConditions() {
  const router = useRouter();

  return (
    <View style={styles.container}>
      <StandardHeader 
        title="Terms & Conditions" 
        onBackPress={() => router.back()} 
      />
      
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <Text style={styles.title}>Terms and Conditions of Mentalease</Text>
          <Text style={styles.effectiveDate}>Effective Date: April 2025</Text>
          
          <Text style={styles.introText}>
            Welcome to Mentalease. By accessing or using our mobile application and services, you agree to be bound by the following Terms and Conditions. Please read them carefully.
          </Text>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>1. Acceptance of Terms</Text>
            <Text style={styles.sectionText}>
              By creating an account, accessing, or using Mentalease, you confirm that you are at least 13 years old and agree to comply with these Terms and all applicable rules and policies. If you are under 13, do not use the app.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>2. Services Provided</Text>
            <Text style={styles.sectionText}>
              Mentalease offers tools and support for mental wellness through:
            </Text>
            <Text style={styles.bulletPoint}>• AI-powered chatbots,</Text>
            <Text style={styles.bulletPoint}>• Appointment booking with licensed psychometricians,</Text>
            <Text style={styles.bulletPoint}>• Mood tracking features,</Text>
            <Text style={styles.bulletPoint}>• Journaling tools.</Text>
            <Text style={styles.sectionText}>
              These services are designed to assist users in managing emotional wellness and mental clarity but do not replace professional medical treatment.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>3. Professional Disclaimer</Text>
            <Text style={styles.sectionText}>
              All cases requiring professional intervention are handled by licensed psychometricians registered in the Philippines.
            </Text>
            <Text style={styles.sectionText}>
              Mentalease includes AI tools for guidance and suggestions. AI responses are not professional advice and should not be solely relied upon. Users are strongly advised to consult hospitals or licensed doctors for diagnosis or treatment of mental health conditions.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>4. User Accounts</Text>
            <Text style={styles.bulletPoint}>• Users must register to access services.</Text>
            <Text style={styles.bulletPoint}>• You are responsible for safeguarding your login credentials.</Text>
            <Text style={styles.sectionText}>
              Each account is strictly for use within Mentalease only and is managed and stored securely by the app administrators. Account data is not shared or used across other platforms or services.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>5. Appointments and Cancellations</Text>
            <Text style={styles.bulletPoint}>• Appointments with professionals must be booked through the app.</Text>
            <Text style={styles.bulletPoint}>• Users may cancel or reschedule an appointment at least 12 hours before the scheduled time without charges.</Text>
            <Text style={styles.bulletPoint}>• Cancellations made within 12 hours of the appointment time are non-refundable.</Text>
            <Text style={styles.bulletPoint}>• Missed appointments (no-shows) are not eligible for refund or reschedule.</Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>6. Payment and Refund Policy</Text>
            <Text style={styles.bulletPoint}>• Payments are processed securely through trusted third-party platforms.</Text>
            <Text style={styles.bulletPoint}>• All payments are final. No refunds will be issued for cancelled, missed, or rescheduled sessions.</Text>
            <Text style={styles.bulletPoint}>• Promo credits or discounts are non-refundable and non-transferable.</Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>7. Data Privacy and Account Security</Text>
            <Text style={styles.bulletPoint}>• Your personal data is handled responsibly and stored in a centralized, secure system managed solely by Mentalease administrators.</Text>
            <Text style={styles.bulletPoint}>• Data is not sold, shared, or scattered across other services.</Text>
            <Text style={styles.sectionText}>
              We follow best practices to protect user privacy, but please use the app responsibly and avoid sharing sensitive personal information unnecessarily.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>8. User Conduct</Text>
            <Text style={styles.sectionText}>You agree to:</Text>
            <Text style={styles.bulletPoint}>• Use the app for its intended purpose only.</Text>
            <Text style={styles.bulletPoint}>• Respect the privacy and safety of other users and professionals.</Text>
            <Text style={styles.bulletPoint}>• Not impersonate anyone, submit false information, or misuse the system.</Text>
            <Text style={styles.sectionText}>
              Any abuse of the system may result in account suspension or termination.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>9. Modifications</Text>
            <Text style={styles.sectionText}>
              We may update these Terms from time to time. Continued use of Mentalease after changes means you accept the updated Terms.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>10. Termination</Text>
            <Text style={styles.sectionText}>
              Mentalease reserves the right to terminate or suspend your account at any time for violating these Terms. You may delete your account at any time, but no refunds will be issued for any reason upon termination.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>11. Contact</Text>
            <Text style={styles.sectionText}>
              For questions or concerns about these Terms, please contact:
            </Text>
            <Text style={styles.contactText}>📧 <EMAIL></Text>
          </View>

          <View style={styles.bottomSpacing} />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    textAlign: 'center',
  },
  effectiveDate: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  introText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    marginBottom: 20,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  sectionText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    marginBottom: 8,
  },
  bulletPoint: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    marginBottom: 5,
    marginLeft: 10,
  },
  contactText: {
    fontSize: 16,
    color: '#6B9142',
    fontWeight: '600',
    marginTop: 5,
  },
  bottomSpacing: {
    height: 40,
  },
});
