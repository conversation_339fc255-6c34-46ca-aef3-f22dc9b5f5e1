// Mood tracking service for database operations
import { supabaseUrl, supabaseAnonKey } from './supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';

const moodOptions = [
  { emoji: '😔', label: 'Sad', value: 0 },
  { emoji: '😕', label: 'Meh', value: 1 },
  { emoji: '😐', label: 'Okay', value: 2 },
  { emoji: '🙂', label: 'Good', value: 3 },
  { emoji: '😄', label: 'Great', value: 4 }
];

/**
 * Save mood entry to database
 * @param {string} userEmail - User's email
 * @param {number} moodValue - Mood value (0-4)
 * @param {string} date - Date in YYYY-MM-DD format
 * @returns {Promise<Object>} - Response with success/error
 */
export const saveMoodEntry = async (userEmail, moodValue, date) => {
  try {
    console.log('Saving mood entry:', { userEmail, moodValue, date });

    const moodData = moodOptions[moodValue];
    if (!moodData) {
      throw new Error('Invalid mood value');
    }

    const moodEntry = {
      user_email: userEmail,
      mood_value: moodValue,
      mood_label: moodData.label,
      mood_emoji: moodData.emoji,
      entry_date: date,
      updated_at: new Date().toISOString()
    };

    // Try to insert the mood entry
    const response = await fetch(`${supabaseUrl}/rest/v1/mood_entries`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Prefer': 'return=representation'
      },
      body: JSON.stringify(moodEntry),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error saving mood entry:', errorText);

      // If it's a duplicate entry error, try to update instead
      if (response.status === 409 || errorText.includes('duplicate') || errorText.includes('unique')) {
        console.log('🔄 Duplicate entry detected, trying to update instead...');
        return await updateMoodEntry(userEmail, moodValue, date);
      }

      throw new Error(`Failed to save mood entry: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Mood entry saved to database:', data);

    // Also save to AsyncStorage as backup
    await saveMoodToAsyncStorage(userEmail, moodValue, date);

    return { success: true, data };
  } catch (error) {
    console.error('Error in saveMoodEntry:', error);
    
    // Fallback to AsyncStorage if database fails
    try {
      await saveMoodToAsyncStorage(userEmail, moodValue, date);
      console.log('✅ Mood entry saved to AsyncStorage as fallback');
      return { success: true, fallback: true };
    } catch (fallbackError) {
      console.error('Fallback save failed:', fallbackError);
      return { success: false, error: error.message };
    }
  }
};

/**
 * Update existing mood entry for a specific date
 * @param {string} userEmail - User's email
 * @param {number} moodValue - Mood value (0-4)
 * @param {string} date - Date in YYYY-MM-DD format
 * @returns {Promise<Object>} - Response with success/error
 */
const updateMoodEntry = async (userEmail, moodValue, date) => {
  try {
    console.log('Updating mood entry:', { userEmail, moodValue, date });

    const moodData = moodOptions[moodValue];
    if (!moodData) {
      throw new Error('Invalid mood value');
    }

    const updateData = {
      mood_value: moodValue,
      mood_label: moodData.label,
      mood_emoji: moodData.emoji,
      updated_at: new Date().toISOString()
    };

    const response = await fetch(
      `${supabaseUrl}/rest/v1/mood_entries?user_email=eq.${userEmail}&entry_date=eq.${date}`,
      {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
          'Authorization': `Bearer ${supabaseAnonKey}`,
          'Prefer': 'return=representation'
        },
        body: JSON.stringify(updateData),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error updating mood entry:', errorText);
      throw new Error(`Failed to update mood entry: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Mood entry updated in database:', data);

    // Also update AsyncStorage
    await saveMoodToAsyncStorage(userEmail, moodValue, date);

    return { success: true, data, updated: true };
  } catch (error) {
    console.error('Error in updateMoodEntry:', error);
    throw error;
  }
};

/**
 * Get mood entries for a user within a date range
 * @param {string} userEmail - User's email
 * @param {string} startDate - Start date (YYYY-MM-DD)
 * @param {string} endDate - End date (YYYY-MM-DD)
 * @returns {Promise<Array>} - Array of mood entries
 */
export const getMoodEntries = async (userEmail, startDate, endDate) => {
  try {
    console.log('Fetching mood entries:', { userEmail, startDate, endDate });

    const response = await fetch(
      `${supabaseUrl}/rest/v1/mood_entries?user_email=eq.${userEmail}&entry_date=gte.${startDate}&entry_date=lte.${endDate}&order=entry_date.asc`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
          'Authorization': `Bearer ${supabaseAnonKey}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch mood entries: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Mood entries fetched from database:', data.length, 'entries');

    return data;
  } catch (error) {
    console.error('Error fetching mood entries:', error);
    
    // Fallback to AsyncStorage
    try {
      const fallbackData = await getMoodFromAsyncStorage(userEmail, startDate, endDate);
      console.log('✅ Using AsyncStorage fallback data');
      return fallbackData;
    } catch (fallbackError) {
      console.error('Fallback fetch failed:', fallbackError);
      return [];
    }
  }
};

/**
 * Get all mood entries for a user
 * @param {string} userEmail - User's email
 * @returns {Promise<Array>} - Array of all mood entries
 */
export const getAllMoodEntries = async (userEmail) => {
  try {
    console.log('Fetching all mood entries for user:', userEmail);

    const response = await fetch(
      `${supabaseUrl}/rest/v1/mood_entries?user_email=eq.${userEmail}&order=entry_date.asc`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
          'Authorization': `Bearer ${supabaseAnonKey}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch mood entries: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ All mood entries fetched:', data.length, 'entries');

    return data;
  } catch (error) {
    console.error('Error fetching all mood entries:', error);
    return [];
  }
};

/**
 * Delete mood entry for a specific date
 * @param {string} userEmail - User's email
 * @param {string} date - Date in YYYY-MM-DD format
 * @returns {Promise<Object>} - Response with success/error
 */
export const deleteMoodEntry = async (userEmail, date) => {
  try {
    console.log('Deleting mood entry:', { userEmail, date });

    const response = await fetch(
      `${supabaseUrl}/rest/v1/mood_entries?user_email=eq.${userEmail}&entry_date=eq.${date}`,
      {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
          'Authorization': `Bearer ${supabaseAnonKey}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to delete mood entry: ${response.status}`);
    }

    console.log('✅ Mood entry deleted from database');

    // Also remove from AsyncStorage
    await removeMoodFromAsyncStorage(userEmail, date);

    return { success: true };
  } catch (error) {
    console.error('Error deleting mood entry:', error);
    return { success: false, error: error.message };
  }
};

// AsyncStorage fallback functions
const saveMoodToAsyncStorage = async (userEmail, moodValue, date) => {
  try {
    const key = `mood_entries_${userEmail}`;
    const existingData = await AsyncStorage.getItem(key);
    const moodEntries = existingData ? JSON.parse(existingData) : {};
    
    moodEntries[date] = moodValue;
    
    await AsyncStorage.setItem(key, JSON.stringify(moodEntries));
  } catch (error) {
    console.error('Error saving to AsyncStorage:', error);
    throw error;
  }
};

const getMoodFromAsyncStorage = async (userEmail, startDate, endDate) => {
  try {
    const key = `mood_entries_${userEmail}`;
    const existingData = await AsyncStorage.getItem(key);
    const moodEntries = existingData ? JSON.parse(existingData) : {};
    
    const result = [];
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    for (const [date, moodValue] of Object.entries(moodEntries)) {
      const entryDate = new Date(date);
      if (entryDate >= start && entryDate <= end) {
        const moodData = moodOptions[moodValue];
        result.push({
          entry_date: date,
          mood_value: moodValue,
          mood_label: moodData.label,
          mood_emoji: moodData.emoji,
          user_email: userEmail
        });
      }
    }
    
    return result.sort((a, b) => new Date(a.entry_date) - new Date(b.entry_date));
  } catch (error) {
    console.error('Error reading from AsyncStorage:', error);
    return [];
  }
};

const removeMoodFromAsyncStorage = async (userEmail, date) => {
  try {
    const key = `mood_entries_${userEmail}`;
    const existingData = await AsyncStorage.getItem(key);
    const moodEntries = existingData ? JSON.parse(existingData) : {};
    
    delete moodEntries[date];
    
    await AsyncStorage.setItem(key, JSON.stringify(moodEntries));
  } catch (error) {
    console.error('Error removing from AsyncStorage:', error);
  }
};

/**
 * Convert date to YYYY-MM-DD format (timezone-safe)
 * @param {Date} date - Date object
 * @returns {string} - Date in YYYY-MM-DD format
 */
export const formatDateForDB = (date) => {
  // Use local timezone instead of UTC to avoid date shifting
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * Get mood statistics for analytics
 * @param {Array} moodEntries - Array of mood entries
 * @returns {Object} - Statistics object
 */
export const calculateMoodStats = (moodEntries) => {
  if (!moodEntries || moodEntries.length === 0) {
    return {
      totalEntries: 0,
      averageMood: 0,
      mostCommonMood: null,
      trend: 'neutral'
    };
  }

  const moodValues = moodEntries.map(entry => entry.mood_value);
  const totalEntries = moodValues.length;
  const averageMood = moodValues.reduce((sum, mood) => sum + mood, 0) / totalEntries;

  // Find most common mood
  const moodCounts = {};
  moodValues.forEach(mood => {
    moodCounts[mood] = (moodCounts[mood] || 0) + 1;
  });
  const mostCommonMood = Object.keys(moodCounts).reduce((a, b) => 
    moodCounts[a] > moodCounts[b] ? parseInt(a) : parseInt(b)
  );

  // Calculate trend (comparing first half vs second half)
  let trend = 'neutral';
  if (totalEntries >= 4) {
    const midPoint = Math.floor(totalEntries / 2);
    const firstHalf = moodValues.slice(0, midPoint);
    const secondHalf = moodValues.slice(midPoint);
    
    const firstAvg = firstHalf.reduce((sum, mood) => sum + mood, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, mood) => sum + mood, 0) / secondHalf.length;
    
    if (secondAvg > firstAvg + 0.3) trend = 'improving';
    else if (secondAvg < firstAvg - 0.3) trend = 'declining';
  }

  return {
    totalEntries,
    averageMood: parseFloat(averageMood.toFixed(1)),
    mostCommonMood,
    trend
  };
};
