// This file is kept for compatibility
// It needs a default export to avoid warnings

// Add necessary polyfills for Node.js modules
import { <PERSON><PERSON><PERSON> } from 'buffer';
import EventEmitter from 'events';
import 'react-native-url-polyfill/auto';
import 'react-native-get-random-values';

// HMR-safe polyfill setup
if (typeof global !== 'undefined') {
  // Only set if not already defined to prevent HMR conflicts
  if (!global.Buffer) {
    global.Buffer = Buffer;
  }
  if (!global.EventEmitter) {
    global.EventEmitter = EventEmitter;
  }

  // Ensure process object exists
  global.process = global.process || {};
  global.process.env = global.process.env || {};
  if (!global.process.version) {
    global.process.version = ''; // Mock Node.js version
  }

  // Safely disable WebSocket to prevent Node module conflicts
  // This is safe because our auth-service.js uses direct fetch API calls
  if (typeof global.WebSocket === 'undefined') {
    global.WebSocket = null;
  }
}

// Empty component to satisfy the router
import React from 'react';

export default function Polyfills() {
  return null;
}





