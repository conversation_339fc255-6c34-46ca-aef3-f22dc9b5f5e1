import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, SafeAreaView } from 'react-native';
import { useUser } from './context/UserContext';
import { useRouter } from 'expo-router';

const SessionTimeoutTest = () => {
  const { userData, updateLastInteraction, logout } = useUser();
  const router = useRouter();
  const [currentTime, setCurrentTime] = useState(Date.now());

  // Update current time every second
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const timeSinceLastInteraction = Math.floor((currentTime - userData.lastInteraction) / 1000);
  const timeUntilTimeout = Math.max(0, 180 - timeSinceLastInteraction); // 3 minutes = 180 seconds

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Session Timeout Test</Text>
        
        <View style={styles.statusContainer}>
          <Text style={styles.label}>Login Status:</Text>
          <Text style={[styles.value, { color: userData.isLoggedIn ? '#4CAF50' : '#F44336' }]}>
            {userData.isLoggedIn ? 'Logged In' : 'Logged Out'}
          </Text>
        </View>

        <View style={styles.statusContainer}>
          <Text style={styles.label}>Time Since Last Interaction:</Text>
          <Text style={styles.value}>{formatTime(timeSinceLastInteraction)}</Text>
        </View>

        <View style={styles.statusContainer}>
          <Text style={styles.label}>Time Until Timeout:</Text>
          <Text style={[styles.value, { color: timeUntilTimeout < 30 ? '#F44336' : '#4CAF50' }]}>
            {formatTime(timeUntilTimeout)}
          </Text>
        </View>

        <View style={styles.statusContainer}>
          <Text style={styles.label}>Last Interaction:</Text>
          <Text style={styles.value}>
            {new Date(userData.lastInteraction).toLocaleTimeString()}
          </Text>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity 
            style={styles.button} 
            onPress={updateLastInteraction}
          >
            <Text style={styles.buttonText}>Update Activity</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.button, styles.logoutButton]} 
            onPress={logout}
          >
            <Text style={styles.buttonText}>Manual Logout</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.button, styles.backButton]} 
            onPress={() => router.back()}
          >
            <Text style={styles.buttonText}>Go Back</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.instructions}>
          • Session will timeout after 3 minutes of inactivity{'\n'}
          • Warning will appear 30 seconds before timeout{'\n'}
          • Any interaction resets the timer{'\n'}
          • Test by not interacting for 3 minutes
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
  },
  value: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  buttonContainer: {
    marginTop: 30,
    gap: 15,
  },
  button: {
    backgroundColor: '#66948a',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  logoutButton: {
    backgroundColor: '#F44336',
  },
  backButton: {
    backgroundColor: '#666',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  instructions: {
    marginTop: 30,
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    textAlign: 'center',
  },
});

export default SessionTimeoutTest;
