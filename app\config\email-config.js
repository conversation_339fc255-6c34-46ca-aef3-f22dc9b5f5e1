// Email configuration for different environments
// This file makes the app portable across different computers

import { Platform } from 'react-native';

// Function to get possible email server URLs based on environment
export const getEmailServerUrls = () => {
  const basePort = 3001;
  const endpoint = '/send-verification';
  
  // Common IP ranges for different network setups
  const commonIPs = [
    // Priority IPs - try these first (your known working IPs)
    '************',  // Your main Wi-Fi IP (known working)
    '************',  // Your Ethernet IP (known working)

    // Localhost variants
    'localhost',
    '127.0.0.1',

    // Android emulator
    '********',

    // Common home network ranges
    '***********', '***********', '***********', '***********', '***********',
    '***********', '***********', '***********', '***********', '************',
    '************', '************', '************', '************', '************',
    '************', '***********0', '************', '*************',

    // Alternative common ranges
    '***********', '***********', '***********', '***********', '***********',
    '************', '***********0', '*************',

    // Corporate/VirtualBox ranges
    '************', '************0',

    // Other common ranges
    '********', '********', '********0', '**********',
    '**********', '***********',
  ];
  
  // Build URLs array
  const urls = commonIPs.map(ip => `http://${ip}:${basePort}${endpoint}`);
  
  return urls;
};

// Function to test and find working email server
export const findWorkingEmailServer = async () => {
  const urls = getEmailServerUrls();
  
  console.log('🔍 Searching for email server across network...');
  
  for (const url of urls) {
    try {
      console.log(`🔄 Testing: ${url}`);
      
      // Test with a simple health check
      const response = await fetch(url.replace('/send-verification', '/health'), {
        method: 'GET',
        timeout: 2000, // Quick timeout for testing
      });
      
      if (response.ok) {
        console.log(`✅ Found working email server: ${url}`);
        return url;
      }
    } catch (error) {
      // Silently continue to next URL
      continue;
    }
  }
  
  console.log('❌ No email server found on network');
  return null;
};

// Email server configuration
export const EMAIL_CONFIG = {
  // Timeout for email requests
  timeout: 10000,
  
  // Retry attempts
  maxRetries: 3,
  
  // Fallback behavior
  fallbackToConsole: true,
  
  // Development mode (shows more logs)
  isDevelopment: __DEV__,
};

export default {
  getEmailServerUrls,
  findWorkingEmailServer,
  EMAIL_CONFIG,
};
