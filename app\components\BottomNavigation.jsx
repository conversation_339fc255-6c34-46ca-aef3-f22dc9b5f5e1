import React, { useEffect, useRef, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter, usePathname } from 'expo-router';

const BottomNavigation = () => {
  const router = useRouter();
  const pathname = usePathname();

  // Animation value for the active indicator
  const [activeIndicator] = React.useState(new Animated.Value(0));

  // UX Enhancement: Button press animations (Aesthetic-Usability Effect)
  const buttonScales = useRef({
    home: new Animated.Value(1),
    journal: new Animated.Value(1),
    chatbot: new Animated.Value(1),
    appointments: new Animated.Value(1),
    assessment: new Animated.Value(1),
  }).current;

  // Enhanced navigation with visual feedback (<PERSON>'s Law - familiar patterns)
  const navigateTo = (path, buttonKey) => {
    // Only navigate if we're not already on this path
    if (pathname !== path) {
      // Button press animation (<PERSON><PERSON>'s Law - visual feedback)
      Animated.sequence([
        Animated.timing(buttonScales[buttonKey], {
          toValue: 0.9,
          duration: 100,
          useNativeDriver: true
        }),
        Animated.timing(buttonScales[buttonKey], {
          toValue: 1,
          duration: 100,
          useNativeDriver: true
        }),
      ]).start();

      // Smooth navigation transition
      router.push(path);
    }
  };

  // Define route groups for better organization
  const routeGroups = {
    home: [
      '/(main)/dashboard',
      '/dashboard',
      '/(main)/profile',
      '/profile',
      '/(main)/profile/index',
      '/profile/index',
      '/(main)/profile/settings',
      '/profile/settings',
      '/(main)/profile/edit-profile',
      '/profile/edit-profile'
    ],
    journal: [
      '/(main)/mental-health/mood-journal',
      '/mental-health/mood-journal',
      '/(main)/mental-health/mood-tracker',
      '/mental-health/mood-tracker'
    ],
    chatbot: [
      '/(main)/consultation/ai-chatbot',
      '/consultation/ai-chatbot'
    ],
    appointments: [
      '/(main)/appointments',
      '/appointments',
      '/(main)/appointments/schedule',
      '/appointments/schedule',
      '/(main)/appointments/reschedule',
      '/appointments/reschedule',
      '/(main)/consultation/online',
      '/consultation/online'
    ],
    assessment: [
      '/(main)/mental-health',
      '/mental-health',
      '/(main)/mental-health/assessment',
      '/mental-health/assessment',
      '/(main)/mental-health/assessment/questions',
      '/mental-health/assessment/questions',
      '/(main)/mental-health/assessment/results',
      '/mental-health/assessment/results'
    ]
  };

  // Check which group the current path belongs to with priority
  const isInGroup = (group) => {
    // Special handling for mental-health routes to prevent conflicts
    if (pathname.includes('/mental-health/mood-journal') || pathname.includes('/mental-health/mood-tracker')) {
      return group === 'journal';
    }

    if (pathname.includes('/mental-health') && !pathname.includes('/mood-')) {
      return group === 'assessment';
    }

    // Use more specific matching for other routes
    const isActive = routeGroups[group].some(route => {
      // Remove the group prefix for comparison
      const cleanRoute = route.replace('/(main)', '');
      // Exact match or pathname starts with the route
      return pathname === route || pathname === cleanRoute ||
             (pathname.startsWith(cleanRoute) && pathname.charAt(cleanRoute.length) === '/');
    });

    return isActive;
  };

  // Get the active tab index for animation
  const getActiveTabIndex = () => {
    if (isInGroup('home')) return 0;
    if (isInGroup('journal')) return 1;
    if (isInGroup('chatbot')) return 2;
    if (isInGroup('appointments')) return 3;
    if (isInGroup('assessment')) return 4;
    return 2; // Default to center (chatbot)
  };

  // Animate the indicator when the active tab changes
  useEffect(() => {
    Animated.spring(activeIndicator, {
      toValue: getActiveTabIndex(),
      useNativeDriver: false,
      friction: 8,
      tension: 50
    }).start();
  }, [pathname]);

  return (
    <View style={styles.bottomNavContainer}>
      <View style={styles.bottomNav}>
        {/* Left side navigation items */}
        <View style={styles.sideNavContainer}>
          <Animated.View style={{ transform: [{ scale: buttonScales.home }] }}>
            <TouchableOpacity
              style={[styles.navItem, isInGroup('home') && styles.activeNavItem]}
              onPress={() => navigateTo('/(main)/dashboard', 'home')}
              activeOpacity={0.7}
            >
              <Ionicons
                name={isInGroup('home') ? "home" : "home-outline"}
                size={24}
                color={isInGroup('home') ? "#5c7e74" : "#999999"}
              />
              <Text style={[
                styles.navText,
                isInGroup('home') && styles.activeNavText
              ]}>Home</Text>
            </TouchableOpacity>
          </Animated.View>

          <Animated.View style={{ transform: [{ scale: buttonScales.journal }] }}>
            <TouchableOpacity
              style={[styles.navItem, isInGroup('journal') && styles.activeNavItem]}
              onPress={() => {
                // Button press animation
                Animated.sequence([
                  Animated.timing(buttonScales.journal, {
                    toValue: 0.9,
                    duration: 100,
                    useNativeDriver: true
                  }),
                  Animated.timing(buttonScales.journal, {
                    toValue: 1,
                    duration: 100,
                    useNativeDriver: true
                  }),
                ]).start();

                router.push({
                  pathname: '/(main)/mental-health/mood-journal',
                  params: { fromBottomNav: 'true' }
                });
              }}
              activeOpacity={0.7}
            >
              <Ionicons
                name={isInGroup('journal') ? "create" : "create-outline"}
                size={24}
                color={isInGroup('journal') ? "#5c7e74" : "#999999"}
              />
              <Text style={[
                styles.navText,
                isInGroup('journal') && styles.activeNavText
              ]}>Journal</Text>
            </TouchableOpacity>
          </Animated.View>
        </View>

        {/* Center Chat Button - Enhanced for Fitts's Law */}
        <View style={styles.centerButtonContainer}>
          <Animated.View style={{ transform: [{ scale: buttonScales.chatbot }] }}>
            <TouchableOpacity
              style={styles.centerButton}
              onPress={() => navigateTo('/(main)/consultation/ai-chatbot', 'chatbot')}
              activeOpacity={0.85}
            >
              <View style={[
                styles.centerButtonInner,
                isInGroup('chatbot') && styles.activeCenterButton
              ]}>
                <Ionicons
                  name="chatbubble"
                  size={26}
                  color="#FFFFFF"
                />
              </View>
            </TouchableOpacity>
          </Animated.View>
        </View>

        {/* Right side navigation items */}
        <View style={styles.sideNavContainer}>
          <Animated.View style={{ transform: [{ scale: buttonScales.appointments }] }}>
            <TouchableOpacity
              style={[styles.navItem, isInGroup('appointments') && styles.activeNavItem]}
              onPress={() => navigateTo('/(main)/appointments', 'appointments')}
              activeOpacity={0.7}
            >
              <Ionicons
                name={isInGroup('appointments') ? "calendar" : "calendar-outline"}
                size={24}
                color={isInGroup('appointments') ? "#5c7e74" : "#999999"}
              />
              <Text style={[
                styles.navText,
                isInGroup('appointments') && styles.activeNavText
              ]}>Appointments</Text>
            </TouchableOpacity>
          </Animated.View>

          <Animated.View style={{ transform: [{ scale: buttonScales.assessment }] }}>
            <TouchableOpacity
              style={[styles.navItem, isInGroup('assessment') && styles.activeNavItem]}
              onPress={() => navigateTo('/(main)/mental-health', 'assessment')}
              activeOpacity={0.7}
            >
              <Ionicons
                name={isInGroup('assessment') ? "clipboard" : "clipboard-outline"}
                size={24}
                color={isInGroup('assessment') ? "#5c7e74" : "#999999"}
              />
              <Text style={[
                styles.navText,
                isInGroup('assessment') && styles.activeNavText
              ]}>Assessment</Text>
            </TouchableOpacity>
          </Animated.View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  bottomNavContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  bottomNav: {
    height: 80,
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 10,
    paddingHorizontal: 8,
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    paddingBottom: Platform.OS === 'ios' ? 25 : 15,
    paddingTop: 8,
  },
  sideNavContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  centerButtonContainer: {
    flex: 0,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 12,
  },
  navItem: {
    justifyContent: 'center',
    alignItems: 'center',
    // Fitts's Law: Larger touch targets for better usability
    paddingVertical: 6,
    paddingHorizontal: 4,
    minHeight: 55,
    minWidth: 50,
    borderRadius: 12,
    flex: 1,
    maxWidth: 75,
  },
  activeNavItem: {
    // Remove all background styling - just keep the transform for subtle elevation
    transform: [{ translateY: -2 }],
  },
  navText: {
    fontSize: 8,
    color: '#999999',
    fontWeight: '500',
    marginTop: 4,
    textAlign: 'center',
    lineHeight: 10,
    numberOfLines: 2,
    adjustsFontSizeToFit: true,
    minimumFontScale: 0.7,
  },
  activeNavText: {
    color: '#5c7e74',
    fontWeight: 'bold',
  },
  centerButton: {
    // Fitts's Law: Larger touch target for primary action
    width: 70,
    height: 70,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: -35,
    zIndex: 10,
  },
  centerButtonInner: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#728a82',
    justifyContent: 'center',
    alignItems: 'center',
    // Enhanced shadow for better visual hierarchy (Aesthetic-Usability Effect)
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 10,
    borderWidth: 3,
    borderColor: '#FFFFFF',
  },
  activeCenterButton: {
    backgroundColor: '#66948a',
    transform: [{ scale: 1.05 }],
  },

});

export default BottomNavigation;
