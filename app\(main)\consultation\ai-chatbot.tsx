import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  Image,
  StatusBar,
  Alert
} from 'react-native';
import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import BottomNavigation from '../../components/BottomNavigation';
import StandardHeader from '../../components/StandardHeader';
import { getAIResponse, isCrisisMessage } from '../../services/openaiService';
import { AI_CONFIG } from '../../config/aiConfig';
import { useUser } from '../../context/UserContext';
import { loadChatHistory, saveChatHistory } from '../../services/chatStorageService';

// Type definitions
interface Message {
  id: string;
  sender: 'user' | 'bot';
  text: string;
  name?: string;
}

interface QuickReply {
  text: string;
}

const AIChatbot = () => {
  const router = useRouter();
  const { userData } = useUser();
  const [message, setMessage] = useState<string>('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState<boolean>(false);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const flatListRef = useRef<FlatList>(null);

  // Load chat history from storage
  const loadUserChatHistory = async () => {
    try {
      const userId = userData?.id || userData?.email || 'default_user';
      const savedMessages = await loadChatHistory(userId);
      setMessages(savedMessages);
    } catch (error) {
      console.error('Error loading chat history:', error);
      // Fallback to default message
      const defaultMessage: Message = {
        id: '1',
        sender: 'bot',
        text: 'How are you feeling today?',
        name: 'Aira'
      };
      setMessages([defaultMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Save chat history to storage
  const saveUserChatHistory = async (messagesToSave: Message[]) => {
    try {
      const userId = userData?.id || userData?.email || 'default_user';
      await saveChatHistory(userId, messagesToSave);
    } catch (error) {
      console.error('Error saving chat history:', error);
    }
  };

  // Get conversation history for context
  const getConversationHistory = () => {
    return messages.slice(-AI_CONFIG.maxHistoryMessages);
  };

  // Load chat history when component mounts or user changes
  useEffect(() => {
    loadUserChatHistory();
  }, [userData?.id, userData?.email]);

  // Save chat history whenever messages change (but not on initial load)
  useEffect(() => {
    if (!isLoading && messages.length > 0) {
      saveUserChatHistory(messages);
    }
  }, [messages, isLoading]);

  const handleSend = async () => {
    if (message.trim() === '') return;

    const userMessageText = message.trim();

    // Check for crisis keywords
    if (isCrisisMessage(userMessageText)) {
      Alert.alert(
        "Crisis Support",
        "I'm concerned about you. Please consider reaching out to a mental health professional or crisis helpline immediately. In the US, you can call 988 for the Suicide & Crisis Lifeline.",
        [
          { text: "OK", style: "default" },
          { text: "Call 988", onPress: () => {/* In a real app, you'd implement calling functionality */} }
        ]
      );
    }

    const newUserMessage: Message = {
      id: Date.now().toString(),
      sender: 'user',
      text: userMessageText
    };

    const updatedMessages = [...messages, newUserMessage];
    setMessages(updatedMessages);
    setMessage('');
    setIsTyping(true);

    try {
      // Get conversation history for context
      const history = getConversationHistory();

      // Get AI response
      const aiResponse = await getAIResponse(userMessageText, history);

      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        sender: 'bot',
        text: aiResponse,
        name: 'Aira'
      };

      setMessages(prevMessages => [...prevMessages, botResponse]);
    } catch (error) {
      console.error('Error getting AI response:', error);

      // Fallback response
      const fallbackResponse: Message = {
        id: (Date.now() + 1).toString(),
        sender: 'bot',
        text: "I'm having trouble connecting right now, but I'm here for you. Try taking a few deep breaths and remember that you're not alone.",
        name: 'Aira'
      };

      setMessages(prevMessages => [...prevMessages, fallbackResponse]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleQuickReply = async (reply: QuickReply) => {
    // Check for crisis keywords
    if (isCrisisMessage(reply.text)) {
      Alert.alert(
        "Crisis Support",
        "I'm concerned about you. Please consider reaching out to a mental health professional or crisis helpline immediately. In the US, you can call 988 for the Suicide & Crisis Lifeline.",
        [
          { text: "OK", style: "default" },
          { text: "Call 988", onPress: () => {/* In a real app, you'd implement calling functionality */} }
        ]
      );
    }

    const newUserMessage: Message = {
      id: Date.now().toString(),
      sender: 'user',
      text: reply.text
    };

    const updatedMessages = [...messages, newUserMessage];
    setMessages(updatedMessages);
    setIsTyping(true);

    try {
      // Get conversation history for context
      const history = getConversationHistory();

      // Get AI response
      const aiResponse = await getAIResponse(reply.text, history);

      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        sender: 'bot',
        text: aiResponse,
        name: 'Aira'
      };

      setMessages(prevMessages => [...prevMessages, botResponse]);
    } catch (error) {
      console.error('Error getting AI response:', error);

      // Fallback response
      const fallbackResponse: Message = {
        id: (Date.now() + 1).toString(),
        sender: 'bot',
        text: "I'm having trouble connecting right now, but I'm here for you. Try taking a few deep breaths and remember that you're not alone.",
        name: 'Aira'
      };

      setMessages(prevMessages => [...prevMessages, fallbackResponse]);
    } finally {
      setIsTyping(false);
    }
  };

  // Auto scroll to bottom when new messages arrive
  useEffect(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToEnd({ animated: true });
    }
  }, [messages]);

  // Keyboard event listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setIsKeyboardVisible(true);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setIsKeyboardVisible(false);
    });

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  const renderMessage = ({ item }) => {
    const isBot = item.sender === 'bot';

    return (
      <View style={[
        styles.messageBubble,
        isBot ? styles.botBubble : styles.userBubble
      ]}>
        {isBot && (
          <View style={styles.botAvatarContainer}>
            <View style={styles.botAvatar}>
              <Image
                source={require('../../../assets/aira.png')}
                style={styles.avatarImage}
              />
            </View>
          </View>
        )}
        <View style={styles.messageContainer}>
          {isBot && (
            <Text style={styles.botName}>{item.name || 'Aira'}</Text>
          )}
          <View style={[
            styles.messageContent,
            isBot ? styles.botContent : styles.userContent
          ]}>
            <Text style={[
              styles.messageText,
              isBot ? styles.botText : styles.userText
            ]}>
              {item.text}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <StandardHeader title="AIRA" subtitle="AI Mental Health Assistant" />

      <SafeAreaView style={styles.safeArea}>
        <View style={styles.chatContainer}>
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View style={styles.chatContent}>
              <FlatList
                ref={flatListRef}
                data={messages}
                renderItem={renderMessage}
                keyExtractor={item => item.id}
                contentContainerStyle={styles.messagesList}
                showsVerticalScrollIndicator={true}
                style={styles.messagesContainer}
                ListFooterComponent={() => (
                  <>
                    {isTyping && (
                      <View style={styles.typingIndicator}>
                        <Text style={styles.typingText}>AIRA is typing...</Text>
                      </View>
                    )}
                  </>
                )}
              />

              {/* Quick Reply Buttons */}
              {!isKeyboardVisible && (
                <View style={styles.quickRepliesContainer}>
                  <TouchableOpacity
                    style={styles.quickReplyButton}
                    onPress={() => handleQuickReply({ text: 'Feeling overwhelmed today' })}
                  >
                    <Text style={styles.quickReplyText}>Feeling overwhelmed today</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.quickReplyButton}
                    onPress={() => handleQuickReply({ text: 'I need someone to talk to...' })}
                  >
                    <Text style={styles.quickReplyText}>I need someone to talk to...</Text>
                  </TouchableOpacity>
                </View>
              )}

              <KeyboardAvoidingView
                behavior={Platform.OS === "ios" ? "padding" : "height"}
                keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
              >
                <View style={styles.inputContainer}>
                  <TextInput
                    style={[styles.input, { maxHeight: 80 }]}
                    placeholder="Type your message here..."
                    placeholderTextColor="#AAAAAA"
                    value={message}
                    onChangeText={setMessage}
                    multiline
                  />
                  <TouchableOpacity
                    style={styles.sendButton}
                    onPress={handleSend}
                    disabled={message.trim() === ''}
                  >
                    <View style={[
                      styles.sendButtonCircle,
                      message.trim() === '' ? styles.sendButtonDisabled : {}
                    ]}>
                      <Text style={styles.sendButtonText}>→</Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </KeyboardAvoidingView>
            </View>
          </TouchableWithoutFeedback>
        </View>

        {!isKeyboardVisible && <BottomNavigation />}
      </SafeAreaView>
    </View>
  );
};

export default AIChatbot;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  safeArea: {
    flex: 1,
  },
  chatContainer: {
    flex: 1,
  },
  chatContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  messagesContainer: {
    flex: 1,
    marginBottom: 10,
  },
  messagesList: {
    padding: 20,
    paddingBottom: 10,
  },
  messageBubble: {
    flexDirection: 'row',
    marginBottom: 15,
    maxWidth: '85%',
  },
  botBubble: {
    alignSelf: 'flex-start',
  },
  userBubble: {
    alignSelf: 'flex-end',
    flexDirection: 'row-reverse',
  },
  botAvatarContainer: {
    marginRight: 10,
    alignSelf: 'flex-start',
    marginTop: 5,
  },
  botAvatar: {
    width: 35,
    height: 35,
    borderRadius: 17.5,
    backgroundColor: '#5A9B8E',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  avatarImage: {
    width: 28,
    height: 28,
    borderRadius: 14,
  },
  messageContainer: {
    flex: 1,
  },
  botName: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 4,
    marginLeft: 2,
  },
  messageContent: {
    borderRadius: 15,
    paddingHorizontal: 14,
    paddingVertical: 10,
    maxWidth: '100%',
  },
  botContent: {
    backgroundColor: '#FFFFFF',
    borderBottomLeftRadius: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 2,
  },
  userContent: {
    backgroundColor: '#D1D5DB',
    borderBottomRightRadius: 4,
  },
  messageText: {
    fontSize: 14,
    lineHeight: 20,
  },
  botText: {
    color: '#333333',
  },
  userText: {
    color: '#333333',
  },
  typingIndicator: {
    padding: 8,
    alignItems: 'flex-start',
    marginLeft: 15,
  },
  typingText: {
    color: '#999999',
    fontSize: 11,
    fontStyle: 'italic',
  },
  quickRepliesContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 8,
    backgroundColor: '#F5F5F5',
    justifyContent: 'space-between',
  },
  quickReplyButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 18,
    paddingHorizontal: 14,
    paddingVertical: 8,
    flex: 0.48,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 2,
  },
  quickReplyText: {
    color: '#666666',
    fontSize: 13,
    textAlign: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: '#F5F5F5',
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
    marginBottom: 75, // Space for bottom navigation
  },
  input: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    maxHeight: 80,
    fontSize: 14,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sendButton: {
    marginLeft: 10,
  },
  sendButtonCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#5A9B8E',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    elevation: 3,
  },
  sendButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  sendButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
