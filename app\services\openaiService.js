import OpenAI from 'openai';
import { AI_CONFIG, SYSTEM_PROMPT } from '../config/aiConfig';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.EXPO_PUBLIC_OPENAI_API_KEY,
  organization: process.env.EXPO_PUBLIC_OPENAI_ORGANIZATION,
  dangerouslyAllowBrowser: true // Required for React Native/Expo
});

// Function to get AI response
export const getAIResponse = async (userMessage, conversationHistory = []) => {
  try {
    // Prepare messages array with system prompt and conversation history
    const messages = [
      { role: 'system', content: SYSTEM_PROMPT },
      ...conversationHistory.map(msg => ({
        role: msg.sender === 'user' ? 'user' : 'assistant',
        content: msg.text
      })),
      { role: 'user', content: userMessage }
    ];

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: AI_CONFIG.model,
      messages: messages,
      max_tokens: AI_CONFIG.maxTokens,
      temperature: AI_CONFIG.temperature,
      presence_penalty: AI_CONFIG.presencePenalty,
      frequency_penalty: AI_CONFIG.frequencyPenalty
    });

    return completion.choices[0].message.content.trim();
  } catch (error) {
    console.error('OpenAI API Error:', error);

    // Fallback responses for different error types
    if (error.code === 'insufficient_quota') {
      return AI_CONFIG.fallbackResponses.quotaExceeded;
    } else if (error.code === 'rate_limit_exceeded') {
      return AI_CONFIG.fallbackResponses.rateLimit;
    } else {
      return getFallbackResponse(userMessage);
    }
  }
};

// Fallback responses when OpenAI is unavailable
const getFallbackResponse = (userMessage) => {
  const lowerCaseMessage = userMessage.toLowerCase();

  if (lowerCaseMessage.includes('overwhelmed') || lowerCaseMessage.includes('stressed')) {
    return "I understand feeling overwhelmed can be difficult. Try taking a few deep breaths. Would you like me to guide you through a quick breathing exercise?";
  } else if (lowerCaseMessage.includes('anxious') || lowerCaseMessage.includes('anxiety')) {
    return "Anxiety can be challenging. Remember that your feelings are valid. Try the 5-4-3-2-1 grounding technique: name 5 things you see, 4 you can touch, 3 you hear, 2 you smell, and 1 you taste.";
  } else if (lowerCaseMessage.includes('sad') || lowerCaseMessage.includes('depressed')) {
    return "I'm sorry you're feeling down. Remember that it's okay to not be okay sometimes. Would you like to talk more about what's bothering you, or would you prefer some self-care suggestions?";
  } else if (lowerCaseMessage.includes('help') || lowerCaseMessage.includes('support')) {
    return "I'm here to support you. Would you like some self-care suggestions, coping strategies, or would you prefer to connect with a mental health professional?";
  } else if (lowerCaseMessage.includes('meditation') || lowerCaseMessage.includes('relax')) {
    return "Meditation can be very helpful. Try focusing on your breath for a few minutes. Breathe in for 4 counts, hold for 4, and exhale for 6. Would you like more relaxation techniques?";
  } else if (lowerCaseMessage.includes('talk') || lowerCaseMessage.includes('listen')) {
    return "I'm here to listen. Feel free to share what's on your mind, and we can work through it together.";
  } else if (lowerCaseMessage.includes('suicide') || lowerCaseMessage.includes('kill myself') || lowerCaseMessage.includes('end it all')) {
    return "I'm very concerned about you. Please reach out to a mental health professional immediately or contact a crisis helpline. In the US, you can call 988 for the Suicide & Crisis Lifeline. Your life has value and there are people who want to help.";
  } else {
    return "Thank you for sharing. I'm here to support you. How else can I help you today? Would you like to talk about what's on your mind or explore some coping strategies?";
  }
};

// Function to check if message indicates crisis
export const isCrisisMessage = (message) => {
  const lowerMessage = message.toLowerCase();
  return AI_CONFIG.crisisKeywords.some(keyword => lowerMessage.includes(keyword));
};

export default { getAIResponse, isCrisisMessage };
