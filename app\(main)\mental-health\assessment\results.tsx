import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  StatusBar,
  ActivityIndicator,
  Share,
  Alert
} from 'react-native';
import { useState, useEffect, useCallback } from 'react';
import { useRouter, useLocalSearchParams } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import StandardHeader from '../../../components/StandardHeader';
import { useUser } from '../../../context/UserContext';
import { supabaseUrl, supabaseAnonKey } from '../../../lib/supabase';

const AssessmentResults = () => {
  const router = useRouter();
  const { id, results: resultsParam, refresh: refreshParam } = useLocalSearchParams();
  const { userData } = useUser();
  const [results, setResults] = useState(null);
  const [assessmentTitle, setAssessmentTitle] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaved, setIsSaved] = useState(false);
  const [savedDate, setSavedDate] = useState(null);
  const [attemptNumber, setAttemptNumber] = useState(1);
  
  // Share results function
  const shareResults = useCallback(async () => {
    try {
      if (!results) return;
      // Format results for sharing
      let message = `My ${assessmentTitle} Results:\n\n`;
      Object.entries(results).forEach(([category, data]: [string, any]) => {
        message += `${category.charAt(0).toUpperCase() + category.slice(1)}: ${data.score} - ${data.level}\n`;
      });
      message += "\nTaken via MentalEase App";
      await Share.share({
        message,
        title: `${assessmentTitle} Results`,
      });
    } catch (error) {
      console.error('Error sharing results:', error);
    }
  }, [results, assessmentTitle]);

  // Check if this specific assessment result is already saved
  const checkIfSaved = useCallback(async () => {
    try {
      if (!userData.email || !results) return;

      // Check if this exact result set is already saved in database
      const response = await fetch(
        `${supabaseUrl}/rest/v1/assessment_results?user_email=eq.${userData.email}&assessment_type=eq.${id}&order=completed_at.desc&limit=5`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabaseAnonKey,
            'Authorization': `Bearer ${supabaseAnonKey}`
          }
        }
      );

      if (response.ok) {
        const existingResults = await response.json();
        if (existingResults && existingResults.length > 0) {
          // Check if current results match any saved results
          const currentResultsString = JSON.stringify(results);
          const isAlreadySaved = existingResults.some(savedResult =>
            savedResult.results === currentResultsString
          );

          if (isAlreadySaved) {
            setIsSaved(true);
            const matchingResult = existingResults.find(savedResult =>
              savedResult.results === currentResultsString
            );
            setSavedDate(new Date(matchingResult.completed_at));
            console.log('✅ This exact result set is already saved in database');
          } else {
            setIsSaved(false);
            console.log('🆕 This is a new result set, not yet saved');
          }

          // Count total attempts for this assessment
          const countResponse = await fetch(
            `${supabaseUrl}/rest/v1/assessment_results?user_email=eq.${userData.email}&assessment_type=eq.${id}&select=id`,
            {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'apikey': supabaseAnonKey,
                'Authorization': `Bearer ${supabaseAnonKey}`
              }
            }
          );

          if (countResponse.ok) {
            const allResults = await countResponse.json();
            setAttemptNumber(allResults.length + 1); // Next attempt number
          }
        } else {
          // No results in database yet
          setIsSaved(false);
          setAttemptNumber(1);
          console.log('🆕 No previous results found, this will be attempt #1');
        }
      }
    } catch (error) {
      console.error('Error checking saved status:', error);
      // Default to not saved on error
      setIsSaved(false);
    }
  }, [id, userData.email, results]);

  // Save assessment results to database
  const saveAssessment = useCallback(async () => {
    try {
      if (!results || !userData.email) return;

      const timestamp = new Date().toISOString();

      // Prepare assessment data for database
      const assessmentData = {
        user_email: userData.email,
        assessment_type: id,
        results: JSON.stringify(results),
        completed_at: timestamp,
        scores: JSON.stringify({
          // Extract main scores for easy querying
          depression: results.depression?.score || null,
          anxiety: results.anxiety?.score || null,
          stress: results.stress?.score || null,
          total_score: results.stress?.score || results.total_score || null
        })
      };

      console.log('Saving assessment to database:', assessmentData);

      // Save to database
      const response = await fetch(`${supabaseUrl}/rest/v1/assessment_results`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
          'Authorization': `Bearer ${supabaseAnonKey}`,
          'Prefer': 'return=representation'
        },
        body: JSON.stringify(assessmentData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Database save error:', errorText);

        // If it's a security policy error, fall back to AsyncStorage only
        if (errorText.includes('row-level security policy')) {
          console.log('🔄 Database security issue, saving to AsyncStorage only');

          // Save to AsyncStorage as fallback
          await AsyncStorage.setItem(`${id}_saved_results`, JSON.stringify(results));
          await AsyncStorage.setItem(`${id}_result_timestamp`, timestamp);
          setSavedDate(new Date(timestamp));

          // Mark current assessment as complete
          await AsyncStorage.removeItem(`${id}_answers`);
          await AsyncStorage.removeItem(`${id}_currentQuestion`);

          setIsSaved(true);
          Alert.alert(
            "Success",
            "Your assessment results have been saved locally!",
            [{
              text: "OK",
              onPress: () => {
                // Navigate back to the main assessment selection page
                router.push('/(main)/mental-health');
              }
            }]
          );
          return; // Exit early, don't throw error
        }

        throw new Error(`Failed to save to database: ${response.status}`);
      }

      const savedData = await response.json();
      console.log('✅ Assessment saved to database:', savedData);

      // Also save to AsyncStorage as backup
      await AsyncStorage.setItem(`${id}_saved_results`, JSON.stringify(results));
      await AsyncStorage.setItem(`${id}_result_timestamp`, timestamp);
      setSavedDate(new Date(timestamp));

      // Mark current assessment as complete
      await AsyncStorage.removeItem(`${id}_answers`);
      await AsyncStorage.removeItem(`${id}_currentQuestion`);

      setIsSaved(true);
      Alert.alert(
        "Success",
        "Your assessment results have been saved successfully!",
        [{
          text: "OK",
          onPress: () => {
            // Navigate back to the main assessment selection page
            router.push('/(main)/mental-health');
          }
        }]
      );
    } catch (error) {
      console.error('Error saving assessment:', error);
      Alert.alert(
        "Error",
        "Failed to save your assessment results. Please try again.",
        [{ text: "OK" }]
      );
    }
  }, [results, id, userData.email]);

  // Load results - prioritize passed results for fresh assessments, then database, then AsyncStorage
  const loadLatestResults = async () => {
    setIsLoading(true);

    let latestResults = null;

    // FIRST: Use passed results if available (for fresh assessments)
    if (resultsParam) {
      try {
        const resultsString = Array.isArray(resultsParam) ? resultsParam[0] : resultsParam;
        latestResults = JSON.parse(resultsString);
        console.log('✅ Using fresh assessment results (passed)');
      } catch (error) {
        console.error('Error parsing passed results:', error);
      }
    }

    // SECOND: Try database if no passed results
    if (!latestResults && userData.email) {
      try {
        console.log('🔍 Checking database for latest results...');
        const response = await fetch(
          `${supabaseUrl}/rest/v1/assessment_results?user_email=eq.${userData.email}&assessment_type=eq.${id}&order=completed_at.desc&limit=1`,
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'apikey': supabaseAnonKey,
              'Authorization': `Bearer ${supabaseAnonKey}`
            }
          }
        );

        if (response.ok) {
          const dbResults = await response.json();
          if (dbResults && dbResults.length > 0) {
            latestResults = JSON.parse(dbResults[0].results);
            console.log('✅ Loaded latest results from database');
          }
        }
      } catch (dbError) {
        console.error('Database error:', dbError);
      }
    }

    // FINAL: Fallback to AsyncStorage
    if (!latestResults) {
      try {
        const savedResults = await AsyncStorage.getItem(`${id}_saved_results`);
        if (savedResults) {
          latestResults = JSON.parse(savedResults);
          console.log('✅ Using AsyncStorage results as final fallback');
        }
      } catch (error) {
        console.error('Error loading from AsyncStorage:', error);
      }
    }

    if (latestResults) {
      setResults(latestResults);
    } else {
      console.error('❌ No results found anywhere');
    }

    setIsLoading(false);
  };

  useEffect(() => {
    // Set assessment title based on ID
    if (id === 'dass') {
      setAssessmentTitle('DASS (Depression Anxiety Stress Scales)');
    } else if (id === 'pss') {
      setAssessmentTitle('PSS (Perceived Stress Scale)');
    } else if (id === 'gad7') {
      setAssessmentTitle('GAD-7 (Generalized Anxiety Disorder)');
    } else if (id === 'phq9') {
      setAssessmentTitle('PHQ-9 (Patient Health Questionnaire)');
    }

    // Load the latest results
    loadLatestResults();
  }, [id, refreshParam]); // Also depend on refresh parameter to reload when retaking

  // Separate effect for checking if saved (runs after results are loaded)
  useEffect(() => {
    if (results) {
      checkIfSaved();
    }
  }, [results, userData.email, id]); // Only run when results change

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Normal':
      case 'Low Stress':
        return '#6B9142'; // Green
      case 'Mild':
      case 'Moderate Stress':
        return '#A3C677'; // Light green
      case 'Moderate':
        return '#FFC107'; // Yellow
      case 'Severe':
      case 'High Stress':
        return '#FF9800'; // Orange
      case 'Extremely Severe':
        return '#F44336'; // Red
      default:
        return '#6B9142';
    }
  };

  const getRecommendations = (category: string, level: string) => {
    const recommendations = {
      depression: {
        Normal: [
          'Continue with your current healthy habits',
          'Maintain regular exercise and social connections',
          'Practice gratitude daily'
        ],
        Mild: [
          'Consider adding more physical activity to your routine',
          'Establish a regular sleep schedule',
          'Connect with friends and family regularly'
        ],
        Moderate: [
          'Consider speaking with a mental health professional',
          'Practice mindfulness meditation daily',
          'Establish a routine that includes enjoyable activities'
        ],
        Severe: [
          'Consult with a mental health professional',
          'Consider joining a support group',
          'Establish a self-care routine and stick to it'
        ],
        Extreme: [
          'Seek professional help immediately',
          'Contact a crisis helpline if needed',
          'Reach out to trusted friends or family for support'
        ]
      },
      anxiety: {
        Normal: [
          'Continue with your current healthy habits',
          'Practice regular relaxation techniques',
          'Maintain a balanced lifestyle'
        ],
        Mild: [
          'Try deep breathing exercises when feeling anxious',
          'Limit caffeine and alcohol consumption',
          'Practice progressive muscle relaxation'
        ],
        Moderate: [
          'Consider speaking with a mental health professional',
          'Practice daily mindfulness meditation',
          'Identify and challenge anxious thoughts'
        ],
        Severe: [
          'Consult with a mental health professional',
          'Learn and practice grounding techniques',
          'Establish a regular exercise routine'
        ],
        Extreme: [
          'Seek professional help immediately',
          'Contact a crisis helpline if needed',
          'Practice self-compassion and acceptance'
        ]
      },
      stress: {
        Normal: [
          'Continue with your current healthy habits',
          'Practice regular relaxation techniques',
          'Maintain work-life balance'
        ],
        'Low Stress': [
          'Excellent stress management! Keep up the good work',
          'Continue with your current coping strategies',
          'Maintain regular exercise and healthy lifestyle'
        ],
        Mild: [
          'Incorporate stress-reduction activities into your routine',
          'Practice time management techniques',
          'Ensure adequate sleep and nutrition'
        ],
        'Moderate Stress': [
          'Consider learning new stress management techniques',
          'Practice mindfulness or meditation daily',
          'Evaluate your current stressors and coping strategies'
        ],
        Moderate: [
          'Consider speaking with a mental health professional',
          'Practice daily stress management techniques',
          'Identify and address sources of stress'
        ],
        'High Stress': [
          'Consider speaking with a mental health professional',
          'Prioritize stress reduction activities',
          'Identify major stressors and develop coping strategies'
        ],
        Severe: [
          'Consult with a mental health professional',
          'Prioritize self-care and stress reduction',
          'Consider lifestyle changes to reduce stress'
        ],
        'Extremely Severe': [
          'Seek professional help immediately',
          'Take immediate steps to reduce stressors',
          'Consider crisis intervention if needed'
        ]
      }
    };
    return recommendations[category]?.[level] || [];
  };

  const formatDate = (date: Date | null) => {
    if (!date) return '';
    return date.toLocaleDateString() + ' at ' +
           date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (isLoading || !results) {
    return (
      <View style={styles.container}>
        <StandardHeader
          title="Assessment Results"
          subtitle="Loading..."
          showBackButton={false}
        />
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#6B9142" />
            <Text style={styles.loadingText}>Loading your results...</Text>
          </View>
        </SafeAreaView>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StandardHeader
        title="Assessment Results"
        subtitle="Mental Health Evaluation"
        onBackPress={() => router.push('/(main)/dashboard')}
        rightComponent={
          <TouchableOpacity onPress={shareResults} style={styles.shareButton}>
            <Text style={styles.shareButtonText}>Share</Text>
          </TouchableOpacity>
        }
      />

      <SafeAreaView style={styles.safeArea}>
        <ScrollView contentContainerStyle={styles.content}>
        <Text style={styles.assessmentTitle}>{assessmentTitle}</Text>
        <Text style={styles.subtitle}>
          Answer each question honestly based on how you've been feeling over the past week
        </Text>
        
        {/* Display saved status and date if saved */}
        {isSaved && (
          <View style={styles.savedBadge}>
            <Text style={styles.savedText}>✓ Saved</Text>
            {savedDate && (
              <Text style={styles.savedDateText}>
                {formatDate(savedDate)}
              </Text>
            )}
          </View>
        )}
        
        {Object.entries(results).map(([category, data]: [string, any]) => (
          <View key={category} style={styles.resultCard}>
            <View style={styles.resultHeader}>
              <Text style={styles.categoryTitle}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </Text>
              <View style={styles.scoreContainer}>
                <Text style={styles.scoreValue}>{data.score}</Text>
                <Text
                  style={[
                    styles.levelText,
                    { color: getLevelColor(data.level) }
                  ]}
                >
                  {data.level}
                </Text>
              </View>
            </View>
            {getRecommendations(category, data.level).length > 0 && (
              <View style={styles.recommendationsContainer}>
                <Text style={styles.recommendationsTitle}>Recommendations:</Text>
                {getRecommendations(category, data.level).map((rec: string, index: number) => (
                  <Text key={index} style={styles.recommendationItem}>• {rec}</Text>
                ))}
              </View>
            )}
          </View>
        ))}
        
        <View style={styles.disclaimerContainer}>
          <Text style={styles.disclaimerText}>
            Disclaimer: This assessment is for self-evaluation purposes only and should not be considered a professional diagnosis. Please consult with a qualified healthcare professional for proper evaluation and treatment.
          </Text>
        </View>
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.saveButton, isSaved && styles.disabledButton]}
            onPress={saveAssessment}
            activeOpacity={0.8}
            disabled={isSaved}
          >
            <Text style={styles.saveButtonText}>
              {isSaved ? 'Assessment Saved' : 'Save Assessment'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.shareResultsButton}
            onPress={shareResults}
            activeOpacity={0.8}
          >
            <Text style={styles.shareResultsButtonText}>Share Results</Text>
          </TouchableOpacity>
        </View>
        
        {/* Add a button to take new assessment if already saved */}
        {isSaved && (
          <TouchableOpacity
            style={styles.newAssessmentButton}
            onPress={() => router.push({
              pathname: '/(main)/mental-health/assessment/questions',
              params: { id: id }
            })}
            activeOpacity={0.8}
          >
            <Text style={styles.newAssessmentButtonText}>Take New Assessment</Text>
          </TouchableOpacity>
        )}
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default AssessmentResults;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },

  safeArea: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 15,
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  shareButton: {
    padding: 8,
  },
  shareButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    padding: 20,
    paddingBottom: 80, // Space for bottom navigation
  },
  assessmentTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 8,
    textAlign: 'center',
    paddingHorizontal: 10,
  },
  subtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 25,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 15,
  },
  savedBadge: {
    alignSelf: 'center',
    backgroundColor: '#E8F2D9',
    paddingVertical: 5,
    paddingHorizontal: 12,
    borderRadius: 15,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#6B9142',
  },
  savedText: {
    color: '#6B9142',
    fontWeight: 'bold',
    fontSize: 14,
    textAlign: 'center',
  },
  savedDateText: {
    color: '#6B9142',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 2,
  },
  resultCard: {
    backgroundColor: '#F5F9EE',
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6B9142',
  },
  scoreContainer: {
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 10,
    minWidth: 70,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  scoreText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B9142',
  },
  interpretationText: {
    fontSize: 14,
    color: '#6B9142',      
  },
  shareResultsButton: {
    backgroundColor: '#6B9142',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 20,
  },
  shareResultsText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },  
  instructionText: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'center',
    padding: 20,
    fontStyle: 'italic',
    marginBottom: 60, // Space for bottom navigation
  },
  // Missing styles
  scoreValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 5,
  },
  levelText: {
    fontSize: 14,
    fontWeight: '600',
  },
  recommendationsContainer: {
    marginTop: 15,
    padding: 15,
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  recommendationsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 10,
  },
  recommendationItem: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 5,
    lineHeight: 20,
  },
  disclaimerContainer: {
    backgroundColor: '#FFF9E6',
    borderRadius: 10,
    padding: 15,
    marginTop: 20,
    borderWidth: 1,
    borderColor: '#FFE082',
  },
  disclaimerText: {
    fontSize: 12,
    color: '#F57C00',
    textAlign: 'center',
    lineHeight: 18,
    fontStyle: 'italic',
  },
  buttonContainer: {
    marginTop: 30,
    gap: 15,
  },
  saveButton: {
    backgroundColor: '#6B9142',
    borderRadius: 25,
    paddingVertical: 15,
    paddingHorizontal: 30,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
    shadowOpacity: 0,
    elevation: 0,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  shareResultsButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  newAssessmentButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    paddingVertical: 15,
    paddingHorizontal: 30,
    alignItems: 'center',
    marginTop: 15,
    borderWidth: 2,
    borderColor: '#6B9142',
  },
  newAssessmentButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: 'bold',
  },
});