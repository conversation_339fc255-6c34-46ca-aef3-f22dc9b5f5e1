import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  TextInput,
  Modal,
  Platform,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
  StatusBar
} from 'react-native';
import { useState, useEffect } from 'react';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useAppointment } from '../../context/AppointmentContext';
import StandardHeader from '../../components/StandardHeader';

const RescheduleAppointment = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const appointmentId = params.id;

  const {
    appointments,
    rescheduleAppointment,
    isTimeSlotAvailable,
    getPsychometricianById,
    getDates,
    getTimeSlots,
    formatDate,
    formatDateString
  } = useAppointment();

  // Find the appointment to reschedule
  const [appointment, setAppointment] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedTime, setSelectedTime] = useState(null);
  const [availableTimeSlots, setAvailableTimeSlots] = useState([]);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  // Load the appointment details (only on initial load)
  useEffect(() => {
    if (appointmentId && appointments.length > 0 && !appointment) {
      const foundAppointment = appointments.find(a => a.id == appointmentId);
      if (foundAppointment) {
        setAppointment(foundAppointment);
        // Don't set initial date/time - let user choose new ones
        // setSelectedDate(foundAppointment.date);
        // setSelectedTime(timeSlot);
      } else {
        // Handle appointment not found
        alert('Appointment not found');
        router.push('/(main)/appointments');
      }
    } else if (appointmentId && appointments.length > 0 && !appointments.find(a => a.id == appointmentId)) {
      // No appointment ID provided
      alert('No appointment specified');
      router.push('/(main)/appointments');
    }
  }, [appointmentId, appointments, appointment]);

  // Update available time slots when appointment or date changes
  useEffect(() => {
    if (appointment && selectedDate) {
      const slots = getTimeSlots(appointment.psychometrician_id, selectedDate);
      setAvailableTimeSlots(slots);

      // Only reset time slot if the currently selected one is not available in the new slots
      if (selectedTime) {
        const isCurrentSlotAvailable = slots.some(slot =>
          slot.start_time === selectedTime.start_time &&
          slot.end_time === selectedTime.end_time
        );
        if (!isCurrentSlotAvailable) {
          setSelectedTime(null);
        }
      }
    } else {
      setAvailableTimeSlots([]);
      // Don't automatically reset selectedTime here - let user keep their selection
    }
  }, [appointment, selectedDate, getTimeSlots]);

  const handleRescheduleAppointment = () => {
    console.log('🎯 HANDLE RESCHEDULE BUTTON CLICKED!');
    console.log('📅 Selected Date:', selectedDate);
    console.log('⏰ Selected Time:', selectedTime);
    console.log('📋 Current Appointment:', appointment);

    if (selectedDate && selectedTime) {
      console.log('✅ Date and time selected, proceeding with validation...');

      // Check if the date or time has actually changed
      const originalTimeSlot = {
        start_time: appointment.start_time,
        end_time: appointment.end_time
      };

      console.log('🔍 Original time slot:', originalTimeSlot);
      console.log('🔍 Selected time slot:', selectedTime);

      if (selectedDate === appointment.date &&
          selectedTime.start_time === originalTimeSlot.start_time &&
          selectedTime.end_time === originalTimeSlot.end_time) {
        console.log('❌ Same date/time selected');
        alert('Please select a different date or time to reschedule');
        return;
      }

      // Check if the selected time slot is available
      const isAvailable = isTimeSlotAvailable(selectedDate, selectedTime, appointment.psychometrician_id, appointmentId);
      console.log('🔍 Time slot availability:', isAvailable);

      if (!isAvailable) {
        console.log('❌ Time slot not available');
        alert('This time slot is not available');
        return;
      }

      console.log('✅ All validations passed, calling confirmReschedule directly');
      // Skip modal for testing - call confirmReschedule directly
      confirmReschedule();
    } else {
      console.log('❌ Date or time not selected');
      console.log('Missing - Date:', !selectedDate, 'Time:', !selectedTime);
    }
  };

  const confirmReschedule = async () => {
    console.log('🎯 CONFIRM RESCHEDULE CLICKED!');
    console.log('📋 Parameters being sent:', {
      appointmentId,
      selectedDate,
      selectedTime
    });

    try {
      console.log('🚀 Calling rescheduleAppointment function...');
      console.log('🔍 Before reschedule - appointment data:', appointment);

      // Call the reschedule function from the controller
      const result = await rescheduleAppointment(appointmentId, selectedDate, selectedTime);

      console.log('✅ Reschedule function returned:', result);
      console.log('✅ Reschedule successful, showing success modal');
      setShowConfirmModal(false);
      setShowSuccessModal(true);
    } catch (error) {
      console.error('❌ Error rescheduling appointment:', error);
      console.error('❌ Error details:', error.message);
      console.error('❌ Full error object:', error);
      alert(`Failed to reschedule appointment: ${error.message}`);
    }
  };

  const closeSuccessModal = () => {
    setShowSuccessModal(false);
    router.push('/(main)/appointments');
  };

  // If appointment is not loaded yet, show loading state
  if (!appointment) {
    return (
      <View style={styles.container}>
        <StandardHeader title="Reschedule Appointment" />
        <View style={styles.loadingContainer}>
          <View style={styles.loadingIndicator}>
            <Text style={styles.loadingEmoji}>⏳</Text>
          </View>
          <Text style={styles.loadingText}>Loading appointment details...</Text>
        </View>
      </View>
    );
  }

  // Get psychometrician details
  const psychometrician = getPsychometricianById(appointment.psychometrician_id);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={styles.container}>
          <StandardHeader title="Reschedule Appointment" />

          <ScrollView contentContainerStyle={styles.content}>
            <View style={styles.appointmentCard}>
              <Text style={styles.currentAppointmentTitle}>Current Appointment</Text>
              <View style={styles.therapistInfo}>
                <Text style={styles.therapistImage}>{psychometrician?.image || '👨‍⚕️'}</Text>
                <View style={styles.therapistDetails}>
                  <Text style={styles.therapistName}>{psychometrician?.name || 'Psychometrician'}</Text>
                  <Text style={styles.therapistSpecialty}>{psychometrician?.specialty || 'Mental Health Professional'}</Text>
                  <Text style={styles.currentDateTime}>
                    {new Date(appointment.date).toLocaleDateString('en-US', {
                      weekday: 'short',
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric'
                    })} at {appointment.start_time} - {appointment.end_time}
                  </Text>
                </View>
              </View>
            </View>

            <Text style={styles.sectionTitle}>Select a New Date</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.datesContainer}
            >
              {getDates().map((date, index) => {
                const formattedDate = formatDate(date);
                const dateString = formatDateString(date);
                return (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.dateCard,
                      selectedDate === dateString && styles.selectedDateCard
                    ]}
                    onPress={() => setSelectedDate(dateString)}
                  >
                    <Text style={[
                      styles.dateDay,
                      selectedDate === dateString && { color: '#FFFFFF' }
                    ]}>{formattedDate.day}</Text>
                    <Text style={[
                      styles.dateNumber,
                      selectedDate === dateString && { color: '#FFFFFF' }
                    ]}>{formattedDate.date}</Text>
                    <Text style={[
                      styles.dateMonth,
                      selectedDate === dateString && { color: '#FFFFFF' }
                    ]}>{formattedDate.month}</Text>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>

            <Text style={styles.sectionTitle}>Select a New Time</Text>
            <View style={styles.timeContainer}>
              {availableTimeSlots.map((timeSlot, index) => {
                // Safety check to prevent undefined timeSlot errors
                if (!timeSlot || !timeSlot.start_time || !timeSlot.end_time) {
                  return null;
                }

                const available = isTimeSlotAvailable(selectedDate, timeSlot, appointment.psychometrician_id, appointmentId);
                const isSelected = selectedTime &&
                  selectedTime.start_time === timeSlot.start_time &&
                  selectedTime.end_time === timeSlot.end_time;

                return (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.timeCard,
                      isSelected && styles.selectedTimeCard,
                      !available && styles.unavailableTimeCard
                    ]}
                    onPress={() => available && setSelectedTime(timeSlot)}
                    disabled={!available}
                  >
                    <Text
                      style={[
                        styles.timeText,
                        isSelected && styles.selectedTimeText,
                        !available && styles.unavailableTimeText
                      ]}
                    >
                      {timeSlot.display}
                    </Text>
                    {!available && (
                      <Text style={styles.bookedText}>Booked</Text>
                    )}
                  </TouchableOpacity>
                );
              })}
              {availableTimeSlots.length === 0 && appointment && selectedDate && (
                <Text style={styles.noTimeSlotsText}>
                  No available time slots for this date.
                </Text>
              )}
            </View>

            <TouchableOpacity
              style={[
                styles.rescheduleButton,
                (!selectedDate || !selectedTime) && styles.disabledButton
              ]}
              onPress={handleRescheduleAppointment}
              disabled={!selectedDate || !selectedTime}
            >
              <Text style={styles.rescheduleButtonText}>Reschedule Appointment</Text>
            </TouchableOpacity>
          </ScrollView>

          {/* Confirmation Modal */}
          <Modal
            visible={showConfirmModal}
            transparent={true}
            animationType="fade"
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Confirm Reschedule</Text>
                <Text style={styles.modalText}>
                  Are you sure you want to reschedule your appointment with {psychometrician?.name || 'Psychometrician'} to {selectedDate} at {selectedTime?.display || 'selected time'}?
                </Text>
                <View style={styles.modalButtons}>
                  <TouchableOpacity
                    style={styles.modalCancelButton}
                    onPress={() => {
                      console.log('🚫 CANCEL BUTTON PRESSED!');
                      setShowConfirmModal(false);
                    }}
                  >
                    <Text style={styles.modalCancelButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.modalConfirmButton}
                    onPress={() => {
                      console.log('🔥 CONFIRM BUTTON PRESSED!');
                      confirmReschedule();
                    }}
                  >
                    <Text style={styles.modalConfirmButtonText}>Confirm</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
        </Modal>

          {/* Success Modal */}
          <Modal
            visible={showSuccessModal}
            transparent={true}
            animationType="fade"
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.successIcon}>✅</Text>
                <Text style={styles.modalTitle}>Appointment Rescheduled</Text>
                <Text style={styles.modalText}>
                  Your appointment has been successfully rescheduled to {selectedDate} at {selectedTime?.display || 'selected time'}.
                </Text>
                <TouchableOpacity
                  style={styles.modalDoneButton}
                  onPress={closeSuccessModal}
                >
                  <Text style={styles.modalDoneButtonText}>Done</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Modal>
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default RescheduleAppointment;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 50,
  },
  loadingIndicator: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F5F9EE',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#6B9142',
  },
  loadingEmoji: {
    fontSize: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B9142',
    fontWeight: '600',
  },

  content: {
    padding: 16,
    paddingBottom: 40,
  },
  appointmentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  currentAppointmentTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 16,
  },
  therapistInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  therapistImage: {
    fontSize: 40,
    marginRight: 16,
  },
  therapistDetails: {
    flex: 1,
  },
  therapistName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 4,
  },
  therapistSpecialty: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  currentDateTime: {
    fontSize: 14,
    color: '#6B9142',
    fontWeight: '500',
  },
  currentAppointmentText: {
    fontSize: 14,
    color: '#666666',
    fontStyle: 'italic',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
  },
  datesContainer: {
    paddingBottom: 10,
  },
  dateCard: {
    width: 80,
    height: 100,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedDateCard: {
    backgroundColor: '#6B9142',
    borderColor: '#6B9142',
    borderWidth: 2,
  },
  dateDay: {
    fontSize: 12,
    color: '#666666',
    textTransform: 'uppercase',
  },
  dateNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    marginVertical: 4,
  },
  dateMonth: {
    fontSize: 12,
    color: '#666666',
    textTransform: 'uppercase',
  },
  timeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  timeCard: {
    width: '30%',
    height: 50,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  selectedTimeCard: {
    backgroundColor: '#6B9142',
    borderColor: '#6B9142',
    borderWidth: 2,
  },
  unavailableTimeCard: {
    backgroundColor: '#F5F5F5',
    opacity: 0.6,
    borderColor: '#E0E0E0',
  },
  timeText: {
    fontSize: 14,
    color: '#333333',
    fontWeight: '500',
  },
  selectedTimeText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  unavailableTimeText: {
    color: '#999999',
  },
  bookedText: {
    fontSize: 10,
    color: '#999999',
    marginTop: 2,
  },
  noTimeSlotsText: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 20,
    padding: 20,
  },
  rescheduleButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginTop: 30,
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  rescheduleButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
    shadowOpacity: 0,
    elevation: 0,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 340,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 12,
    textAlign: 'center',
  },
  modalText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 12,
  },
  modalCancelButton: {
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    padding: 16,
    flex: 1,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  modalCancelButtonText: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '500',
  },
  modalConfirmButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    padding: 16,
    flex: 1,
    alignItems: 'center',
    shadowColor: '#6B9142',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  modalConfirmButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  successIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  modalDoneButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    padding: 16,
    width: '100%',
    alignItems: 'center',
    shadowColor: '#6B9142',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  modalDoneButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
});
