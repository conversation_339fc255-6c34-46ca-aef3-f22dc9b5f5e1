// Gmail email service using EmailJS (React Native compatible)

// Send verification email using EmailJS with Gmail
export const sendVerificationEmail = async (toEmail, verificationCode) => {
  try {
    console.log('Sending verification email via EmailJS to:', toEmail);

    // Using EmailJS service - works in React Native
    const response = await fetch('https://api.emailjs.com/api/v1.0/email/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        service_id: process.env.EMAILJS_SERVICE_ID || 'service_gmail',
        template_id: process.env.EMAILJS_TEMPLATE_ID || 'template_verification',
        user_id: process.env.EMAILJS_PUBLIC_KEY || 'YOUR_EMAILJS_PUBLIC_KEY',
        template_params: {
          to_email: toEmail,
          verification_code: verificationCode,
          subject: 'Email Verification Code',
          message: `Your verification code is: ${verificationCode}`,
          from_name: 'Your App Name'
        }
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`EmailJS error: ${errorData}`);
    }

    console.log('Verification email sent successfully via EmailJS');
    return { success: true, error: null };
  } catch (error) {
    console.error('EmailJS sending error:', error);
    return { success: false, error: error.message };
  }
};

export default { sendVerificationEmail };
