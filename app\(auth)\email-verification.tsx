import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator,
  Image,
  Animated,
  StatusBar
} from 'react-native';
import { useRouter } from 'expo-router';
import { useUser } from '../context/UserContext';
import { supabaseUrl, supabaseAnonKey } from '../lib/supabase';
import { getEmailServerUrls, EMAIL_CONFIG } from '../config/email-config';

const EmailVerification = () => {
  const router = useRouter();
  const { userData, updateUserData } = useUser();
  const [code, setCode] = useState('');
  const [timer, setTimer] = useState(57);
  const [canResend, setCanResend] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  // Get email from user context
  const email = userData.email || "<EMAIL>";

  // Handle back button press - simplified to avoid BackHandler errors
  useEffect(() => {
    // We've removed the BackHandler code to fix the error

    // Instead, we'll just add a simple back button in the UI
    // that navigates to the create account screen
  }, [router]);

  // Check if user is already verified
  useEffect(() => {
    console.log("Email verification check - User data:", JSON.stringify(userData));

    // First check: if we have an existing user with emailVerified and hasCompletedProfile
    if (userData.emailVerified && userData.hasCompletedProfile) {
      console.log("User is verified and has completed profile, redirecting to dashboard");
      router.replace('/(main)/dashboard');
      return;
    }

    // Second check: if user is verified but needs to complete profile
    if (userData.emailVerified && userData.firstName) {
      console.log("User is verified and has personal info, redirecting to dashboard");
      router.replace('/(main)/dashboard');
      return;
    }

    // Third check: if user is verified but has no personal info
    if (userData.emailVerified && !userData.firstName) {
      console.log("User is verified but needs to complete personal info");
      router.replace('/(auth)/personal-information');
      return;
    }

    // If we get here, user is not verified, show verification screen
    console.log("User is not verified, showing verification screen");
    setIsLoading(false);

    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, [userData, router]);

  useEffect(() => {
    let interval;
    if (timer > 0) {
      interval = setInterval(() => {
        setTimer(prevTimer => prevTimer - 1);
      }, 1000);
    } else {
      setCanResend(true);
    }

    return () => clearInterval(interval);
  }, [timer]);

  const handleResendCode = async () => {
    if (canResend) {
      try {
        console.log('🔄 Resending verification code...');

        // Generate new custom verification code
        console.log('🔄 Generating new custom verification code...');

        // Fallback: Generate custom code
        const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
        const expirationTime = Date.now() + (10 * 60 * 1000);

        // Store verification code
        try {
          const updateCodeResponse = await fetch(`${supabaseUrl}/rest/v1/users?email=eq.${email}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'apikey': supabaseAnonKey,
              'Prefer': 'return=representation'
            },
            body: JSON.stringify({
              activationcode: verificationCode,                    // Web uses 'activationcode'
              activation_code_expires: new Date(expirationTime).toISOString()
            }),
          });

          if (updateCodeResponse.ok) {
            console.log('✅ Custom verification code stored successfully');
          } else {
            console.log('❌ Failed to store verification code in DB');
          }
        } catch (dbError) {
          console.log('❌ DB error storing verification code:', dbError);
        }

        console.log('');
        console.log('🎯 ================================');
        console.log('🔢 CUSTOM VERIFICATION CODE:', verificationCode);
        console.log('📧 For email:', email);
        console.log('⏰ Valid for 10 minutes');
        console.log('🎯 ================================');
        console.log('');

        // Send email with the custom verification code
        try {
          const possibleUrls = getEmailServerUrls();

          let emailSent = false;

          for (const url of possibleUrls) {
            try {
              const emailResponse = await fetch(url, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  email: email,
                  verificationCode: verificationCode
                }),
              });

              if (emailResponse.ok) {
                console.log('✅ Custom verification email sent successfully via:', url);
                emailSent = true;
                break;
              }
            } catch (urlError) {
              continue;
            }
          }

          if (emailSent) {
            Alert.alert(
              "Code Resent Successfully! 📧",
              `A new verification code has been sent to ${email}. Please check your email and enter the 6-digit code below.`,
              [{ text: 'OK', style: 'default' }]
            );
          } else {
            console.log('❌ All email server connections failed');
            Alert.alert(
              "Code Generated 📱",
              `New verification code: ${verificationCode}\n\nEmail server is temporarily unavailable, but you can use this code to verify your account.`,
              [{ text: 'Use Code', style: 'default' }]
            );
          }
        } catch (emailError) {
          console.log('❌ Email sending error:', emailError);
          Alert.alert(
            "Code Generated 📱",
            `New verification code: ${verificationCode}\n\nEmail sending failed, but you can use this code to verify your account.`,
            [{ text: 'Use Code', style: 'default' }]
          );
        }

        setTimer(60);
        setCanResend(false);

      } catch (error) {
        console.error('Resend error:', error);
        Alert.alert(
          "Resend Failed ❌",
          "Failed to resend verification code. Please check your internet connection and try again.",
          [
            { text: 'Try Again', style: 'default' },
            {
              text: 'Need Help?',
              style: 'cancel',
              onPress: () => {
                Alert.alert(
                  'Need Help? 🤝',
                  'If you continue to have issues:\n\n• Check your internet connection\n• Look in your spam/junk folder\n• Contact <NAME_EMAIL>',
                  [{ text: 'OK', style: 'default' }]
                );
              }
            }
          ]
        );
      }
    }
  };

  const handleVerify = async () => {
    if (code.length === 6) {
      setIsVerifying(true);

      try {
        // Try Supabase OTP verification first
        console.log('🔄 Attempting Supabase OTP verification...');

        const supabaseOtpResponse = await fetch(`${supabaseUrl}/auth/v1/verify`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabaseAnonKey,
          },
          body: JSON.stringify({
            type: 'email',
            email: email,
            token: code
          }),
        });

        if (supabaseOtpResponse.ok) {
          console.log('✅ Supabase OTP verification successful');
        } else {
          console.log('❌ Supabase OTP verification failed, trying custom code...');

          // Fallback: Try custom verification code system
          const userResponse = await fetch(`${supabaseUrl}/rest/v1/users?email=eq.${email}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'apikey': supabaseAnonKey,
            },
          });

          const userData = await userResponse.json();

          if (!userResponse.ok || !userData || userData.length === 0) {
            Alert.alert(
              "Account Not Found ❌",
              "We couldn't find your account. Please try signing up again or contact support if you believe this is an error.",
              [
                { text: 'Sign Up Again', onPress: () => router.push('/(auth)/create-account') },
                { text: 'Contact Support', style: 'cancel', onPress: () => {
                  Alert.alert(
                    'Contact Support 📞',
                    'Please contact our support <NAME_EMAIL> for assistance.',
                    [{ text: 'OK', style: 'default' }]
                  );
                }}
              ]
            );
            setIsVerifying(false);
            return;
          }

          const user = userData[0];
          const storedCode = user.activationcode;                    // Web uses 'activationcode'
          const codeExpires = user.activation_code_expires;

          // Check if code matches
          if (storedCode !== code) {
            Alert.alert("Invalid Code", "The verification code you entered is incorrect. Please try again.");
            setIsVerifying(false);
            return;
          }

          // Check if code has expired
          if (codeExpires && new Date(codeExpires) < new Date()) {
            Alert.alert("Code Expired", "The verification code has expired. Please request a new one.");
            setIsVerifying(false);
            return;
          }

          console.log('✅ Custom verification code verified successfully');
        }

        // Update our users table - mark as verified and clear verification code
        const updateResponse = await fetch(`${supabaseUrl}/rest/v1/users?email=eq.${email}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabaseAnonKey,
            'Prefer': 'return=representation'
          },
          body: JSON.stringify({
            status: true,                        // Web uses 'status' instead of 'email_verified'
            activationcode: null,                // Web uses 'activationcode'
            activation_code_expires: null
          }),
        });

        // Clear any fallback storage (React Native doesn't use localStorage)
        console.log('✅ Verification successful, user marked as verified');

        if (!updateResponse.ok) {
          throw new Error('Failed to update verification status');
        }

        // Update user context to mark email as verified
        updateUserData({
          email: email,
          emailVerified: true
        });

        // Show success alert and navigate
        console.log('✅ Email verified successfully, navigating to personal information...');
        setIsVerifying(false);

        Alert.alert(
          'Email Verified Successfully! ✅',
          'Great! Your email has been verified. Now let\'s complete your profile to get started with MentalEase.',
          [
            {
              text: 'Continue',
              style: 'default',
              onPress: () => router.push('/(auth)/personal-information')
            }
          ],
          { cancelable: false }
        );
      } catch (error) {
        console.error('Verification error:', error);
        Alert.alert(
          "Verification Failed ❌",
          "There was an error verifying your code. Please check your internet connection and try again.",
          [
            { text: 'Try Again', style: 'default' },
            {
              text: 'Resend Code',
              style: 'cancel',
              onPress: () => {
                if (canResend) {
                  handleResendCode();
                } else {
                  Alert.alert(
                    'Please Wait ⏰',
                    `You can request a new code in ${timer} seconds.`,
                    [{ text: 'OK', style: 'default' }]
                  );
                }
              }
            }
          ]
        );
        setIsVerifying(false);
      }
    } else {
      Alert.alert(
        "Invalid Code Format ❌",
        "Please enter a valid 6-digit verification code. Check your email for the correct code.",
        [{ text: 'OK', style: 'default' }]
      );
    }
  };

  // Show loading indicator while checking verification status
  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, styles.loadingContainer]}>
        <StatusBar backgroundColor="#66948a" barStyle="light-content" translucent />
        <ActivityIndicator size="large" color="#6B9142" />
        <Text style={styles.loadingText}>Checking verification status...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#66948a" barStyle="light-content" translucent />
      {/* Back Button */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Logo */}
            <View style={styles.logoContainer}>
              <Image
                source={require('../../assets/mainlogo.png')}
                style={styles.logo}
                resizeMode="contain"
              />
              <Text style={styles.logoText}>mentalease</Text>
            </View>

            {/* Title */}
            <Text style={styles.title}>Verify Your Email</Text>

            {/* Description */}
            <Text style={styles.description}>
              We've sent a verification code to you{'\n'}
              {email}
            </Text>

            {/* Input Label */}
            <Text style={styles.inputLabel}>Enter verification code</Text>

            {/* Code Input */}
            <TextInput
              style={styles.input}
              placeholder="Enter 6-digit code"
              placeholderTextColor="#999999"
              value={code}
              onChangeText={setCode}
              keyboardType="number-pad"
              maxLength={6}
              returnKeyType="done"
              onSubmitEditing={Keyboard.dismiss}
            />

            {/* Resend Section */}
            <View style={styles.resendSection}>
              <Text style={styles.resendText}>
                Didn't receive the code?
              </Text>
              <TouchableOpacity
                onPress={handleResendCode}
                disabled={!canResend}
              >
                <Text style={[styles.resendTimer, canResend && styles.resendActive]}>
                  {canResend ? 'Resend code' : `Resend code in ${timer} seconds`}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Verify Button */}
            <TouchableOpacity
              style={[
                styles.verifyButton,
                (code.length !== 6 || isVerifying) && styles.verifyButtonDisabled
              ]}
              onPress={() => {
                Keyboard.dismiss();
                handleVerify();
              }}
              disabled={code.length !== 6 || isVerifying}
            >
              <Text style={styles.verifyButtonText}>
                {isVerifying ? "Verifying..." : "Verify Email"}
              </Text>
            </TouchableOpacity>

            {/* Partnership Text */}
            <Text style={styles.partnershipText}>
              in Partnership with Sanda Diagnostic Center
            </Text>
          </Animated.View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default EmailVerification;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8E8E8',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#2D5016',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  backIcon: {
    fontSize: 20,
    color: '#2D5016',
    fontWeight: 'bold',
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 12,
  },
  logoText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2D5016',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2D5016',
    textAlign: 'center',
    marginBottom: 20,
  },
  description: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 40,
  },
  inputLabel: {
    fontSize: 16,
    color: '#2D5016',
    alignSelf: 'flex-start',
    marginBottom: 12,
    width: '100%',
  },
  input: {
    backgroundColor: '#C5C5C5',
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 16,
    marginBottom: 20,
    fontSize: 16,
    width: '100%',
    textAlign: 'center',
    letterSpacing: 2,
    color: '#333333',
  },
  resendSection: {
    alignItems: 'center',
    marginBottom: 40,
  },
  resendText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  resendTimer: {
    fontSize: 14,
    color: '#999999',
  },
  resendActive: {
    color: '#2D5016',
    fontWeight: '600',
  },
  verifyButton: {
    backgroundColor: '#2D5016',
    borderRadius: 25,
    paddingVertical: 16,
    alignItems: 'center',
    width: '100%',
    marginBottom: 40,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  verifyButtonDisabled: {
    backgroundColor: '#C5C5C5',
  },
  verifyButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  partnershipText: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
    marginTop: 20,
  },
});
