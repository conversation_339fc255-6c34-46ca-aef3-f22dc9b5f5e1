import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
  Modal,
  ActivityIndicator,
  StatusBar,
  Keyboard
} from 'react-native';
import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'expo-router';
import { useUser } from '../context/UserContext';
import { supabaseUrl, supabaseAnonKey } from '../lib/supabase';
import KeyboardAwareScrollView from '../components/KeyboardAwareScrollView';

// Type definitions
interface FormData {
  firstName: string;
  middleName: string;
  lastName: string;
  age: string;
  gender: string;
  civilStatus: string;
  phone: string;
  address: string;
  birthdate: string;
  birthplace: string;
  religion: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  age?: string;
  gender?: string;
  civilStatus?: string;
  phone?: string;
  address?: string;
  birthdate?: string;
  birthplace?: string;
}

const PersonalInformation = () => {
  const router = useRouter();
  const { userData, updateUserData } = useUser();
  const [isLoading, setIsLoading] = useState(true);

  // Simplified back button handling to avoid BackHandler errors
  const handleBackNavigation = useCallback(() => {
    router.push('/(auth)/email-verification');
  }, [router]);

  // We've removed the BackHandler code to fix the error
  useEffect(() => {
    // Instead, we'll just use the UI back button
  }, []);

  // Check user profile status
  useEffect(() => {
    const checkProfileStatus = () => {
      // If user has completed profile, go to dashboard
      if (userData.hasCompletedProfile || (userData.emailVerified && userData.firstName)) {
        router.replace('/(main)/dashboard');
        return;
      }

      // If user is verified but doesn't have personal info, show form
      if (userData.emailVerified && !userData.firstName) {
        setIsLoading(false);
        return;
      }

      // If user is not verified, go to email verification
      if (!userData.emailVerified) {
        router.replace('/(auth)/email-verification');
        return;
      }

      setIsLoading(false);
    };

    checkProfileStatus();
  }, [userData, router]);

  // Options for dropdowns
  const genderOptions = ['Male', 'Female', 'Non-binary', 'Prefer not to say', 'Other'];
  const civilStatusOptions = ['Single', 'Married', 'Divorced', 'Widowed', 'Separated', 'Other'];

  // Form state management
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    middleName: '',
    lastName: '',
    age: '',
    gender: '',
    civilStatus: '',
    phone: '',
    birthdate: '',
    birthplace: '',
    religion: '',
    address: ''
  });

  // UI state management
  const [errors, setErrors] = useState<FormErrors>({});
  const [showGenderModal, setShowGenderModal] = useState<boolean>(false);
  const [showCivilStatusModal, setShowCivilStatusModal] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  // Form field update handler
  const updateField = useCallback((field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field when user types
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  }, [errors]);

  // Form validation
  const validateForm = useCallback(() => {
    const requiredFields: Array<{ key: keyof FormData; label: string }> = [
      { key: 'firstName', label: 'First name' },
      { key: 'lastName', label: 'Last name' },
      { key: 'age', label: 'Age' },
      { key: 'gender', label: 'Gender' },
      { key: 'civilStatus', label: 'Civil status' },
      { key: 'phone', label: 'Phone number' },
      { key: 'birthdate', label: 'Birthdate' },
      { key: 'birthplace', label: 'Birthplace' },
      { key: 'address', label: 'Address' }
    ];

    const newErrors: FormErrors = {};
    let isValid = true;

    // Check required fields
    requiredFields.forEach(field => {
      if (!formData[field.key] || !formData[field.key].trim()) {
        newErrors[field.key] = `${field.label} is required`;
        isValid = false;
      }
    });

    // Additional validation for age
    if (formData.age && (isNaN(Number(formData.age)) || parseInt(formData.age) <= 0)) {
      newErrors.age = 'Please enter a valid age';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  }, [formData]);

  // Form submission handler
  const handleSubmit = useCallback(async () => {
    if (validateForm()) {
      setIsSubmitting(true);

      try {
        // Always generate a completely unique control number
        // Use timestamp + random + user email hash for maximum uniqueness
        const timestamp = Date.now().toString(); // Full timestamp
        const random = Math.floor(10000 + Math.random() * 90000); // 5-digit random
        const emailHash = userData.email.split('@')[0].slice(-3); // Last 3 chars of email username
        const controlNumber = `MH-${new Date().getFullYear()}-${timestamp.slice(-8)}${random}${emailHash}`;

        console.log('🔢 Generated completely unique control number:', controlNumber);

        // Prepare user data for database (using web column names)
        const dbUserData = {
          name: formData.firstName.trim(),                // Web uses 'name' for first name
          middle_name: formData.middleName.trim(),
          last_name: formData.lastName.trim(),
          age: parseInt(formData.age),
          gender: formData.gender.trim(),
          civil_status: formData.civilStatus.trim(),
          contactnumber: formData.phone.trim(),           // Web uses 'contactnumber'
          birthdate: formData.birthdate.trim(),
          birthplace: formData.birthplace.trim(),
          religion: formData.religion.trim() || 'Not specified',
          address: formData.address.trim(),
          control_number: controlNumber,
          has_completed_profile: true
        };

        // Add personal information to user record in database
        console.log('📝 Adding personal information to user record...');
        console.log('📧 User email:', userData.email);
        console.log('📊 Data to add:', dbUserData);

        const updateResponse = await fetch(`${supabaseUrl}/rest/v1/users?email=eq.${userData.email}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabaseAnonKey,
            'Prefer': 'return=representation'
          },
          body: JSON.stringify(dbUserData),
        });

        console.log('📊 Update response status:', updateResponse.status);

        if (!updateResponse.ok) {
          const errorText = await updateResponse.text();
          console.log('❌ Update failed with error:', errorText);
          throw new Error(`Failed to add personal information to database: ${errorText}`);
        }

        console.log('✅ User profile updated in database');

        // Prepare user data for context (using camelCase)
        const contextUserData = {
          firstName: formData.firstName.trim(),
          middleName: formData.middleName.trim(),
          lastName: formData.lastName.trim(),
          age: parseInt(formData.age),
          gender: formData.gender.trim(),
          civilStatus: formData.civilStatus.trim(),
          phone: formData.phone.trim(),
          birthdate: formData.birthdate.trim(),
          birthplace: formData.birthplace.trim(),
          religion: formData.religion.trim() || 'Not specified',
          address: formData.address.trim(),
          controlNumber: controlNumber,
          emailVerified: true,
          hasCompletedProfile: true
        };

        // Update user data in context
        updateUserData(contextUserData, () => {
          setIsSubmitting(false);
          router.replace('/(main)/dashboard');
        });

      } catch (error) {
        console.error('Error updating user profile:', error);
        Alert.alert(
          "Update Failed",
          "There was an error updating your profile. Please try again.",
          [{ text: "OK" }]
        );
        setIsSubmitting(false);
      }
    } else {
      Alert.alert(
        "Incomplete Information",
        "Please fill in all required fields correctly.",
        [{ text: "OK" }]
      );
    }
  }, [formData, validateForm, updateUserData, router, userData.email]);

  // Show loading indicator while checking user data
  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, styles.loadingContainer]}>
        <ActivityIndicator size="large" color="#6B9142" />
        <Text style={styles.loadingText}>Checking account information...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        translucent={true}
        backgroundColor="#66948a"
        barStyle="light-content"
      />
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBackNavigation}
        >
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Personal Information</Text>
        {/* Empty View to balance the header */}
        <View style={styles.headerRight} />
      </View>

      <KeyboardAwareScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        extraScrollHeight={50}
        enableOnAndroid={true}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View>
            <Text style={styles.subtitle}>
              Please provide your personal information to continue.
              Fields marked with * are required.
            </Text>

            <View style={styles.formSection}>
              <Text style={styles.sectionTitle}>Basic Information</Text>

              <Text style={styles.inputLabel}>First Name *</Text>
              <TextInput
                style={[styles.input, errors.firstName && styles.inputError]}
                placeholder="Enter your first name"
                value={formData.firstName}
                onChangeText={(text) => updateField('firstName', text)}
                autoCapitalize="words"
              />
              {errors.firstName && <Text style={styles.errorText}>{errors.firstName}</Text>}

              <Text style={styles.inputLabel}>Middle Name</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your middle name (optional)"
                value={formData.middleName}
                onChangeText={(text) => updateField('middleName', text)}
                autoCapitalize="words"
              />

              <Text style={styles.inputLabel}>Last Name *</Text>
              <TextInput
                style={[styles.input, errors.lastName && styles.inputError]}
                placeholder="Enter your last name"
                value={formData.lastName}
                onChangeText={(text) => updateField('lastName', text)}
                autoCapitalize="words"
              />
              {errors.lastName && <Text style={styles.errorText}>{errors.lastName}</Text>}

              <Text style={styles.inputLabel}>Age *</Text>
              <TextInput
                style={[styles.input, errors.age && styles.inputError]}
                placeholder="Enter your age"
                value={formData.age}
                onChangeText={(text) => updateField('age', text)}
                keyboardType="numeric"
                maxLength={3}
              />
              {errors.age && <Text style={styles.errorText}>{errors.age}</Text>}

              <Text style={styles.inputLabel}>Gender *</Text>
              <TouchableOpacity
                style={[styles.input, styles.dropdownInput, errors.gender && styles.inputError]}
                onPress={() => setShowGenderModal(true)}
                activeOpacity={0.7}
              >
                <Text style={formData.gender ? styles.dropdownText : styles.placeholderText}>
                  {formData.gender || "Select your gender"}
                </Text>
                <Text style={styles.dropdownIcon}>▼</Text>
              </TouchableOpacity>
              {errors.gender && <Text style={styles.errorText}>{errors.gender}</Text>}

              <Text style={styles.inputLabel}>Civil Status *</Text>
              <TouchableOpacity
                style={[styles.input, styles.dropdownInput, errors.civilStatus && styles.inputError]}
                onPress={() => setShowCivilStatusModal(true)}
                activeOpacity={0.7}
              >
                <Text style={formData.civilStatus ? styles.dropdownText : styles.placeholderText}>
                  {formData.civilStatus || "Select your civil status"}
                </Text>
                <Text style={styles.dropdownIcon}>▼</Text>
              </TouchableOpacity>
              {errors.civilStatus && <Text style={styles.errorText}>{errors.civilStatus}</Text>}
            </View>

            <View style={styles.formSection}>
              <Text style={styles.sectionTitle}>Contact Information</Text>

              <Text style={styles.inputLabel}>Phone Number *</Text>
              <TextInput
                style={[styles.input, errors.phone && styles.inputError]}
                placeholder="Enter your phone number"
                value={formData.phone}
                onChangeText={(text) => updateField('phone', text)}
                keyboardType="phone-pad"
              />
              {errors.phone && <Text style={styles.errorText}>{errors.phone}</Text>}

              <Text style={styles.inputLabel}>Address *</Text>
              <TextInput
                style={[styles.input, styles.multilineInput, errors.address && styles.inputError]}
                placeholder="Enter your complete address"
                value={formData.address}
                onChangeText={(text) => updateField('address', text)}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
              {errors.address && <Text style={styles.errorText}>{errors.address}</Text>}
            </View>

            <View style={styles.formSection}>
              <Text style={styles.sectionTitle}>Background Information</Text>

              <Text style={styles.inputLabel}>Birthdate *</Text>
              <TextInput
                style={[styles.input, errors.birthdate && styles.inputError]}
                placeholder="YYYY-MM-DD"
                value={formData.birthdate}
                onChangeText={(text) => updateField('birthdate', text)}
                keyboardType={Platform.OS === 'ios' ? 'default' : 'numeric'}
              />
              {errors.birthdate && <Text style={styles.errorText}>{errors.birthdate}</Text>}

              <Text style={styles.inputLabel}>Birthplace *</Text>
              <TextInput
                style={[styles.input, errors.birthplace && styles.inputError]}
                placeholder="Enter your birthplace"
                value={formData.birthplace}
                onChangeText={(text) => updateField('birthplace', text)}
                autoCapitalize="words"
              />
              {errors.birthplace && <Text style={styles.errorText}>{errors.birthplace}</Text>}

              <Text style={styles.inputLabel}>Religion</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your religion (optional)"
                value={formData.religion}
                onChangeText={(text) => updateField('religion', text)}
                autoCapitalize="words"
              />
            </View>

            <TouchableOpacity
              style={[styles.submitButton, isSubmitting && styles.submitButtonDisabled]}
              onPress={handleSubmit}
              disabled={isSubmitting}
              activeOpacity={0.8}
            >
              {isSubmitting ? (
                <ActivityIndicator color="#FFFFFF" size="small" />
              ) : (
                <Text style={styles.submitButtonText}>Submit and Continue</Text>
              )}
            </TouchableOpacity>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAwareScrollView>

      {/* Gender Selection Modal */}
        <Modal
          visible={showGenderModal}
          transparent={true}
          animationType="fade"
          statusBarTranslucent={true}
        >
          <TouchableWithoutFeedback onPress={() => setShowGenderModal(false)}>
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Select Gender</Text>

                <ScrollView style={styles.optionsList}>
                  {genderOptions.map((option, index) => (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.optionItem,
                        formData.gender === option && styles.selectedOption
                      ]}
                      onPress={() => {
                        updateField('gender', option);
                        setShowGenderModal(false);
                      }}
                      activeOpacity={0.7}
                    >
                      <Text style={[
                        styles.optionText,
                        formData.gender === option && styles.selectedOptionText
                      ]}>
                        {option}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>

                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => setShowGenderModal(false)}
                  activeOpacity={0.7}
                >
                  <Text style={styles.closeButtonText}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>

        {/* Civil Status Selection Modal */}
        <Modal
          visible={showCivilStatusModal}
          transparent={true}
          animationType="fade"
          statusBarTranslucent={true}
        >
          <TouchableWithoutFeedback onPress={() => setShowCivilStatusModal(false)}>
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Select Civil Status</Text>

                <ScrollView style={styles.optionsList}>
                  {civilStatusOptions.map((option, index) => (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.optionItem,
                        formData.civilStatus === option && styles.selectedOption
                      ]}
                      onPress={() => {
                        updateField('civilStatus', option);
                        setShowCivilStatusModal(false);
                      }}
                      activeOpacity={0.7}
                    >
                      <Text style={[
                        styles.optionText,
                        formData.civilStatus === option && styles.selectedOptionText
                      ]}>
                        {option}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>

                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => setShowCivilStatusModal(false)}
                  activeOpacity={0.7}
                >
                  <Text style={styles.closeButtonText}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
    </SafeAreaView>
  );
};

export default PersonalInformation;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8E8E8',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  loadingText: {
    marginTop: 15,
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight + 10 : 15,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    width: 60, // Fixed width to balance the header
  },
  backButtonText: {
    color: '#2D5016',
    fontSize: 16,
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2D5016',
    textAlign: 'center',
  },
  headerRight: {
    width: 60, // Same width as backButton for balance
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 40, // Extra padding at bottom for better scrolling
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: '#666666',
    marginTop: 15,
    marginBottom: 25,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 10,
  },
  formSection: {
    marginBottom: 20,
    backgroundColor: '#F5F9EE',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    paddingBottom: 8,
  },
  inputLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 6,
    fontWeight: '500',
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 12,
    marginBottom: 15,
    fontSize: 15,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    color: '#333333',
  },
  multilineInput: {
    height: 100,
    textAlignVertical: 'top',
    paddingTop: 12,
  },
  inputError: {
    borderColor: '#FF6B6B',
    borderWidth: 1.5,
  },
  errorText: {
    color: '#FF6B6B',
    fontSize: 12,
    marginTop: -10,
    marginBottom: 10,
    paddingLeft: 5,
  },
  submitButton: {
    backgroundColor: '#2D5016',
    borderRadius: 30,
    paddingVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    marginTop: 15,
    marginBottom: 30,
    shadowColor: '#2D5016',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 4,
    height: 50,
  },
  submitButtonDisabled: {
    backgroundColor: '#A5BF85',
    shadowOpacity: 0.1,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  dropdownInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownText: {
    color: '#333333',
    fontSize: 15,
  },
  placeholderText: {
    color: '#999999',
    fontSize: 15,
  },
  dropdownIcon: {
    fontSize: 12,
    color: '#6B9142',
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '85%',
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 15,
    textAlign: 'center',
  },
  optionsList: {
    maxHeight: 300,
  },
  optionItem: {
    paddingVertical: 14,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    borderRadius: 8,
  },
  selectedOption: {
    backgroundColor: '#F5F9EE',
  },
  optionText: {
    fontSize: 16,
    color: '#333333',
  },
  selectedOptionText: {
    color: '#6B9142',
    fontWeight: 'bold',
  },
  closeButton: {
    marginTop: 15,
    paddingVertical: 12,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  closeButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
});

