# 🔄 Database Migration Guide: `user` → `users`

This guide helps you migrate from your mobile `user` table to the unified `users` table that works with both mobile and web applications.

## 📊 **Schema Changes Summary**

### **Column Mapping:**
| **Mobile (`user`)** | **Web (`users`)** | **Action** |
|-------------------|------------------|------------|
| `first_name` | `name` | 🔄 **Rename** |
| `email_verified` | `status` | 🔄 **Rename** |
| `verification_code` | `activationcode` | 🔄 **Rename** |
| `phone` | `contactnumber` | 🔄 **Rename** |
| `middle_name` | `middle_name` | ✅ **Keep** |
| `last_name` | `last_name` | ✅ **Keep** |
| `has_completed_profile` | `has_completed_profile` | ✅ **Keep** |
| ❌ Missing | `username` | ➕ **Add** |
| ❌ Missing | `role` | ➕ **Add** |
| ❌ Missing | `resetcode` | ➕ **Add** |
| ❌ Missing | `disable` | ➕ **Add** |
| ❌ Missing | `created_at` | ➕ **Add** |
| ❌ Missing | `updated_at` | ➕ **Add** |

## 🚀 **Migration Steps**

### **Step 1: Backup Your Data**
```sql
-- Create backup of current user table
CREATE TABLE user_backup AS SELECT * FROM "user";
```

### **Step 2: Run Migration Script**
```bash
# In Supabase SQL Editor, run:
# migrate_user_to_users_table.sql
```

### **Step 3: Update Mobile App Code**

#### **Replace auth-service.js:**
```javascript
// OLD: app/lib/auth-service.js
import { signUp, signIn } from './auth-service';

// NEW: app/lib/auth-service-users.js  
import { signUp, signIn } from './auth-service-users';
```

#### **Update all database references:**
```javascript
// OLD
fetch(`${supabaseUrl}/rest/v1/user?email=eq.${email}`)

// NEW  
fetch(`${supabaseUrl}/rest/v1/users?email=eq.${email}`)
```

### **Step 4: Update Column References**

#### **Authentication:**
```javascript
// OLD
user.email_verified
user.verification_code

// NEW
user.status              // email_verified → status
user.activationcode      // verification_code → activationcode
```

#### **Personal Info:**
```javascript
// OLD
user.first_name
user.phone

// NEW
user.name               // first_name → name
user.contactnumber      // phone → contactnumber
```

### **Step 5: Test Migration**

#### **Verification Queries:**
```sql
-- Check data migration
SELECT COUNT(*) as old_count FROM user_backup;
SELECT COUNT(*) as new_count FROM "users";

-- Verify column mapping
SELECT 
  name,                    -- was first_name
  email,
  status,                  -- was email_verified  
  activationcode,          -- was verification_code
  contactnumber,           -- was phone
  role,                    -- new column
  control_number           -- mobile-specific
FROM "users" 
LIMIT 5;
```

## 🔧 **Code Updates Required**

### **1. Auth Service Updates**
```javascript
// File: app/lib/auth-service.js
// Replace with: app/lib/auth-service-users.js

// Key changes:
- Table: 'user' → 'users'
- Column: 'email_verified' → 'status'
- Column: 'verification_code' → 'activationcode'
- Column: 'phone' → 'contactnumber'
- Add: 'role' field (default: 'patient')
```

### **2. Email Verification Updates**
```javascript
// File: app/(auth)/email-verification.tsx

// OLD
fetch(`${supabaseUrl}/rest/v1/user?email=eq.${email}`)

// NEW
fetch(`${supabaseUrl}/rest/v1/users?email=eq.${email}`)

// Column updates:
verification_code → activationcode
email_verified → status
```

### **3. Personal Information Updates**
```javascript
// File: app/(auth)/personal-information.tsx

// Database update payload:
{
  name: formData.firstName,           // first_name → name
  contactnumber: formData.phone,      // phone → contactnumber
  // ... other fields remain same
}
```

### **4. Sign-in Updates**
```javascript
// File: app/(auth)/sign-in.tsx

// User context mapping:
firstName: user.name,                 // name → firstName
emailVerified: user.status,           // status → emailVerified
phone: user.contactnumber,            // contactnumber → phone
```

## 🎯 **Using the Migration Helper**

### **Import the mapping utility:**
```javascript
import { mapWebToMobile, mapMobileToWeb } from '../lib/user-mapping';

// Convert web data for mobile app
const mobileUser = mapWebToMobile(webUserData);

// Convert mobile data for web database
const webUser = mapMobileToWeb(mobileUserData);
```

## ✅ **Verification Checklist**

- [ ] **Migration script executed successfully**
- [ ] **Data count matches** (old user table = new users table)
- [ ] **Auth service updated** to use `users` table
- [ ] **Email verification** works with new column names
- [ ] **Personal information** saves correctly
- [ ] **Sign-in process** works with new schema
- [ ] **User context** maps data correctly
- [ ] **Mobile app** can create new accounts
- [ ] **Web app** can access same user data

## 🔄 **Rollback Plan (If Needed)**

```sql
-- If migration fails, restore from backup:
DROP TABLE IF EXISTS "users";
ALTER TABLE user_backup RENAME TO "user";

-- Then revert mobile app code changes
```

## 🎉 **Benefits After Migration**

✅ **Unified Database** - Web and mobile share same user data  
✅ **Role-Based Access** - Support for different user types  
✅ **Better Timestamps** - Automatic created_at/updated_at tracking  
✅ **Web Compatibility** - Mobile users can access web features  
✅ **Future-Proof** - Easier to add new features across platforms  

## 📞 **Support**

If you encounter issues:
1. Check the verification queries
2. Review console logs for errors
3. Use the user-mapping helper for data conversion
4. Test with a single user first before full migration
