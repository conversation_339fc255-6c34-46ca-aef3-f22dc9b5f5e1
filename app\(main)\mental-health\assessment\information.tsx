import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Animated,
  Dimensions,
  Platform
} from 'react-native';
import { useState, useEffect, useRef } from 'react';
import { useRouter, useLocalSearchParams } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { LinearGradient } from 'expo-linear-gradient';
import StandardHeader from '../../../components/StandardHeader';

const { width, height } = Dimensions.get('window');

const AssessmentInformation = () => {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const assessmentId = Array.isArray(id) ? id[0] : id;

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const buttonOpacity = useRef(new Animated.Value(0)).current;
  const buttonScale = useRef(new Animated.Value(0.8)).current;

  // State for scroll-triggered button
  const [showBeginButton, setShowBeginButton] = useState(false);
  const [scrollY, setScrollY] = useState(0);

  // Assessment information data
  const assessmentInfo = {
    dass: {
      title: 'DASS-21',
      fullName: 'Depression, Anxiety and Stress Scale - 21 Items',
      description: 'A scientifically validated psychological assessment tool designed to measure the emotional states of depression, anxiety, and stress.',
      purpose: 'To help identify and understand your current emotional well-being across three key psychological dimensions.',
      duration: '5-10 minutes',
      questions: 21,
      scales: [
        { name: 'Depression', description: 'Measures feelings of sadness, hopelessness, and lack of interest' },
        { name: 'Anxiety', description: 'Assesses physical arousal, panic attacks, and fearfulness' },
        { name: 'Stress', description: 'Evaluates tension, irritability, and difficulty relaxing' }
      ],
      instructions: [
        'Read each statement carefully',
        'Consider how much each statement applied to you over the past week',
        'Select the response that best describes your experience',
        'Answer honestly - there are no right or wrong answers',
        'Complete all questions for accurate results'
      ],
      confidentiality: 'Your responses are completely confidential and stored securely on your device.',
      disclaimer: 'This assessment is for self-evaluation purposes only and should not replace professional medical advice.'
    },
    pss: {
      title: 'PSS-10',
      fullName: 'Perceived Stress Scale - 10 Items',
      description: 'A widely used psychological instrument that measures your perception of stress and how unpredictable, uncontrollable, and overloaded you find your life.',
      purpose: 'To assess your subjective experience of stress and your ability to cope with life\'s challenges.',
      duration: '3-5 minutes',
      questions: 10,
      scales: [
        { name: 'Perceived Stress', description: 'Measures how often you feel stressed, overwhelmed, or unable to cope' }
      ],
      instructions: [
        'Think about your feelings and thoughts during the last month',
        'For each question, indicate how often you felt or thought a certain way',
        'Choose the response that best reflects your experience',
        'Be honest about your feelings - this helps ensure accurate results',
        'Complete all questions for a comprehensive assessment'
      ],
      confidentiality: 'Your responses are completely confidential and stored securely on your device.',
      disclaimer: 'This assessment is for self-evaluation purposes only and should not replace professional medical advice.'
    }
  };

  const currentAssessment = assessmentInfo[assessmentId as keyof typeof assessmentInfo];

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  // Handle scroll to show/hide begin button
  const handleScroll = (event) => {
    const currentScrollY = event.nativeEvent.contentOffset.y;
    setScrollY(currentScrollY);

    // Show button when user has scrolled down significantly (about 60% of content)
    const shouldShow = currentScrollY > 400;

    if (shouldShow && !showBeginButton) {
      setShowBeginButton(true);
      // Animate button appearance
      Animated.parallel([
        Animated.timing(buttonOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.spring(buttonScale, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        })
      ]).start();
    } else if (!shouldShow && showBeginButton) {
      setShowBeginButton(false);
      Animated.parallel([
        Animated.timing(buttonOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(buttonScale, {
          toValue: 0.8,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    }
  };

  const handleStartAssessment = () => {
    // Add haptic feedback and animation
    Animated.sequence([
      Animated.timing(buttonScale, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(buttonScale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      })
    ]).start(() => {
      router.push({
        pathname: '/(main)/mental-health/assessment/questions',
        params: {
          id: assessmentId,
          fresh: 'true' // Indicate this is a fresh start
        }
      });
    });
  };

  const handleGoBack = () => {
    router.back();
  };

  if (!currentAssessment) {
    return (
      <View style={styles.container}>
        <StandardHeader title="Assessment Information" onBackPress={handleGoBack} />

        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Assessment information not found</Text>
          <TouchableOpacity style={styles.errorBackButton} onPress={handleGoBack}>
            <Text style={styles.errorBackButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StandardHeader title="Assessment Information" onBackPress={handleGoBack} />

      <Animated.View 
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }]
          }
        ]}
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
          onScroll={handleScroll}
          scrollEventThrottle={16}
        >
          {/* Assessment Title Card */}
          <View style={styles.titleCard}>
            <View style={styles.assessmentBadge}>
              <Text style={styles.badgeText}>{currentAssessment.title}</Text>
            </View>
            <Text style={styles.fullName}>{currentAssessment.fullName}</Text>
            <Text style={styles.description}>{currentAssessment.description}</Text>
          </View>

          {/* Quick Facts */}
          <View style={styles.factsCard}>
            <Text style={styles.sectionTitle}>Quick Facts</Text>
            <View style={styles.factRow}>
              <View style={styles.factItem}>
                <Text style={styles.factNumber}>{currentAssessment.questions}</Text>
                <Text style={styles.factLabel}>Questions</Text>
              </View>
              <View style={styles.factItem}>
                <Text style={styles.factNumber}>{currentAssessment.duration.split(' ')[0]}</Text>
                <Text style={styles.factLabel}>{currentAssessment.duration.split(' ')[1]}</Text>
                <Text style={styles.factSubLabel}>Duration</Text>
              </View>
              <View style={styles.factItem}>
                <Text style={styles.factNumber}>{currentAssessment.scales.length}</Text>
                <Text style={styles.factLabel}>Scale{currentAssessment.scales.length > 1 ? 's' : ''}</Text>
              </View>
            </View>
          </View>

          {/* What This Measures */}
          <View style={styles.measuresCard}>
            <Text style={styles.sectionTitle}>What This Assessment Measures</Text>
            <Text style={styles.purposeText}>{currentAssessment.purpose}</Text>
            
            <View style={styles.scalesContainer}>
              {currentAssessment.scales.map((scale, index) => (
                <View key={index} style={styles.scaleItem}>
                  <View style={styles.scaleIndicator} />
                  <View style={styles.scaleContent}>
                    <Text style={styles.scaleName}>{scale.name}</Text>
                    <Text style={styles.scaleDescription}>{scale.description}</Text>
                  </View>
                </View>
              ))}
            </View>
          </View>

          {/* Instructions */}
          <View style={styles.instructionsCard}>
            <Text style={styles.sectionTitle}>How to Complete This Assessment</Text>
            {currentAssessment.instructions.map((instruction, index) => (
              <View key={index} style={styles.instructionItem}>
                <View style={styles.instructionNumber}>
                  <Text style={styles.instructionNumberText}>{index + 1}</Text>
                </View>
                <Text style={styles.instructionText}>{instruction}</Text>
              </View>
            ))}
          </View>

          {/* Privacy & Disclaimer */}
          <View style={styles.privacyCard}>
            <Text style={styles.sectionTitle}>Privacy & Important Information</Text>
            
            <View style={styles.privacyItem}>
              <Text style={styles.privacyIcon}>🔒</Text>
              <View style={styles.privacyContent}>
                <Text style={styles.privacyTitle}>Confidentiality</Text>
                <Text style={styles.privacyText}>{currentAssessment.confidentiality}</Text>
              </View>
            </View>

            <View style={styles.privacyItem}>
              <Text style={styles.privacyIcon}>⚠️</Text>
              <View style={styles.privacyContent}>
                <Text style={styles.privacyTitle}>Medical Disclaimer</Text>
                <Text style={styles.privacyText}>{currentAssessment.disclaimer}</Text>
              </View>
            </View>
          </View>

          {/* Spacer for scroll content */}
          <View style={styles.scrollSpacer} />
        </ScrollView>
      </Animated.View>

      {/* Floating Begin Assessment Button */}
      {showBeginButton && (
        <Animated.View
          style={[
            styles.floatingButtonContainer,
            {
              opacity: buttonOpacity,
              transform: [{ scale: buttonScale }]
            }
          ]}
        >
          <TouchableOpacity
            style={styles.floatingButton}
            onPress={handleStartAssessment}
            activeOpacity={0.9}
          >
            <Text style={styles.floatingButtonText}>Begin Assessment</Text>
            <Text style={styles.floatingButtonSubtext}>Ready to start?</Text>
          </TouchableOpacity>
        </Animated.View>
      )}

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },

  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120, // Space for floating button
  },
  scrollSpacer: {
    height: 100, // Extra space to ensure user can scroll enough to trigger button
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#666666',
    marginBottom: 20,
    textAlign: 'center',
  },
  errorBackButton: {
    backgroundColor: '#66948a',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
  },
  errorBackButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  // Title Card Styles
  titleCard: {
    backgroundColor: '#FFFFFF',
    margin: 20,
    marginBottom: 15,
    borderRadius: 20,
    padding: 25,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
  },
  assessmentBadge: {
    backgroundColor: '#66948a',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 15,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  fullName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2D5016',
    textAlign: 'center',
    marginBottom: 15,
    lineHeight: 24,
  },
  description: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
  },
  // Facts Card Styles
  factsCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginBottom: 15,
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2D5016',
    marginBottom: 15,
  },
  factRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  factItem: {
    alignItems: 'center',
    flex: 1,
  },
  factNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#66948a',
    marginBottom: 5,
  },
  factLabel: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
    textAlign: 'center',
  },
  factSubLabel: {
    fontSize: 12,
    color: '#999999',
    fontWeight: '400',
    textAlign: 'center',
    marginTop: 2,
  },
  // Measures Card Styles
  measuresCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginBottom: 15,
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 4,
  },
  purposeText: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 22,
    marginBottom: 20,
  },
  scalesContainer: {
    marginTop: 10,
  },
  scaleItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  scaleIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#66948a',
    marginTop: 6,
    marginRight: 15,
  },
  scaleContent: {
    flex: 1,
  },
  scaleName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2D5016',
    marginBottom: 4,
  },
  scaleDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  // Instructions Card Styles
  instructionsCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginBottom: 15,
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 4,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  instructionNumber: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#F5F9EE',
    borderWidth: 2,
    borderColor: '#66948a',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
    marginTop: 2,
  },
  instructionNumberText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#66948a',
  },
  instructionText: {
    flex: 1,
    fontSize: 15,
    color: '#444444',
    lineHeight: 21,
  },
  // Privacy Card Styles
  privacyCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginBottom: 15,
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 4,
  },
  privacyItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  privacyIcon: {
    fontSize: 24,
    marginRight: 15,
    marginTop: 2,
  },
  privacyContent: {
    flex: 1,
  },
  privacyTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2D5016',
    marginBottom: 6,
  },
  privacyText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  // Floating Button Styles
  floatingButtonContainer: {
    position: 'absolute',
    bottom: 30,
    left: 20,
    right: 20,
    zIndex: 1000,
  },
  floatingButton: {
    backgroundColor: '#2D5016',
    borderRadius: 25,
    paddingVertical: 20,
    paddingHorizontal: 30,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 12,
    borderWidth: 3,
    borderColor: '#FFFFFF',
  },
  floatingButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  floatingButtonSubtext: {
    color: '#FFFFFF',
    fontSize: 14,
    opacity: 0.9,
    fontWeight: '500',
  },

});

export default AssessmentInformation;
