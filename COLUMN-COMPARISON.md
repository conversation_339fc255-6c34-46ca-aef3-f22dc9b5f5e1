# 📊 Column Comparison: Web vs Mobile

## **Current Web `users` Table:**
Based on your friend's database screenshot:

| **Column** | **Type** | **Status** |
|------------|----------|------------|
| `id` | int8 | ✅ **Exists** |
| `created_at` | timestamp | ✅ **Exists** |
| `updated_at` | timestamp | ✅ **Exists** |
| `name` | varchar | ✅ **Exists** |
| `email` | varchar | ✅ **Exists** |
| `username` | varchar | ✅ **Exists** |
| `password` | varchar | ✅ **Exists** |
| `status` | bool | ✅ **Exists** |
| `role` | varchar | ✅ **Exists** |
| `activationcode` | varchar | ✅ **Exists** |
| `resetcode` | varchar | ✅ **Exists** |
| `contactnumber` | varchar | ✅ **Exists** |
| `disable` | bool | ✅ **Exists** |

## **Missing Mobile Columns:**
These columns need to be **ADDED** to the web `users` table:

| **Column** | **Type** | **Purpose** | **Default** |
|------------|----------|-------------|-------------|
| `middle_name` | VARCHAR | User middle name | NULL |
| `last_name` | VARCHAR | User last name | NULL |
| `age` | INTEGER | User age | NULL |
| `gender` | VARCHAR | User gender | NULL |
| `civil_status` | VARCHAR | Marital status | NULL |
| `birthdate` | VARCHAR | Birth date | NULL |
| `birthplace` | VARCHAR | Birth place | NULL |
| `religion` | VARCHAR | User religion | NULL |
| `address` | VARCHAR | User address | NULL |
| `control_number` | VARCHAR | Mobile app ID (MH-2023-45678) | Auto-generated |
| `has_completed_profile` | BOOLEAN | Profile completion flag | FALSE |
| `verification_code_expires` | TIMESTAMPTZ | Code expiration time | NULL |

## **Column Mapping for Mobile App:**

### **Web → Mobile Mapping:**
```javascript
// When reading from database
const mobileUser = {
  firstName: webUser.name,                    // name → firstName
  middleName: webUser.middle_name,            // direct mapping
  lastName: webUser.last_name,                // direct mapping
  email: webUser.email,                       // direct mapping
  emailVerified: webUser.status,              // status → emailVerified
  phone: webUser.contactnumber,               // contactnumber → phone
  controlNumber: webUser.control_number,      // direct mapping
  hasCompletedProfile: webUser.has_completed_profile, // direct mapping
  // ... other mobile fields
};
```

### **Mobile → Web Mapping:**
```javascript
// When writing to database
const webUser = {
  name: mobileUser.firstName,                 // firstName → name
  middle_name: mobileUser.middleName,         // direct mapping
  last_name: mobileUser.lastName,             // direct mapping
  email: mobileUser.email,                    // direct mapping
  status: mobileUser.emailVerified,           // emailVerified → status
  contactnumber: mobileUser.phone,            // phone → contactnumber
  control_number: mobileUser.controlNumber,   // direct mapping
  has_completed_profile: mobileUser.hasCompletedProfile, // direct mapping
  // ... other web fields
};
```

## **SQL Script to Add Missing Columns:**

Run `add_mobile_columns_to_users.sql` to add all missing mobile columns to your existing `users` table.

## **After Adding Columns:**

### **Complete `users` Table Schema:**
```sql
CREATE TABLE "users" (
  -- Web columns (existing)
  id BIGSERIAL PRIMARY KEY,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  name VARCHAR,                    -- Web: first name
  email VARCHAR UNIQUE NOT NULL,
  username VARCHAR,
  password VARCHAR NOT NULL,
  status BOOLEAN DEFAULT FALSE,    -- Web: email verified
  role VARCHAR DEFAULT 'patient',
  activationcode VARCHAR,          -- Web: verification code
  resetcode VARCHAR,
  contactnumber VARCHAR,           -- Web: phone number
  disable BOOLEAN DEFAULT FALSE,
  
  -- Mobile columns (to be added)
  middle_name VARCHAR,
  last_name VARCHAR,
  age INTEGER,
  gender VARCHAR,
  civil_status VARCHAR,
  birthdate VARCHAR,
  birthplace VARCHAR,
  religion VARCHAR,
  address VARCHAR,
  control_number VARCHAR UNIQUE,
  has_completed_profile BOOLEAN DEFAULT FALSE,
  verification_code_expires TIMESTAMPTZ
);
```

## **Benefits After Adding Columns:**

✅ **Full Compatibility** - Mobile app can store all user data  
✅ **Web Access** - Web app can access mobile user profiles  
✅ **No Data Loss** - All existing mobile data preserved  
✅ **Future Proof** - Ready for cross-platform features  

## **Next Steps:**

1. **Run SQL script** to add missing columns
2. **Update mobile app** to use `users` table instead of `user`
3. **Test data sync** between web and mobile
4. **Verify all features** work with new schema
