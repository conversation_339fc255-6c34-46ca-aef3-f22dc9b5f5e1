-- Create appointments table for booking appointments with psychometricians
-- Run this in your Supabase SQL Editor

-- Step 1: Create the appointments table
CREATE TABLE IF NOT EXISTS appointments (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    psychometrician_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'confirmed', 'completed', 'cancelled', 'rescheduled')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure no double booking for the same psychometrician at the same time
    UNIQUE(psychometrician_id, date, start_time),
    
    -- Ensure end_time is after start_time
    CHECK (end_time > start_time)
);

-- Step 2: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_appointments_user_id ON appointments(user_id);
CREATE INDEX IF NOT EXISTS idx_appointments_psychometrician_id ON appointments(psychometrician_id);
CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(date);
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);
CREATE INDEX IF NOT EXISTS idx_appointments_psychometrician_date ON appointments(psychometrician_id, date);

-- Step 3: Create function to auto-update updated_at timestamp
CREATE OR REPLACE FUNCTION update_appointments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Step 4: Create trigger for auto-updating updated_at
DROP TRIGGER IF EXISTS update_appointments_updated_at ON appointments;
CREATE TRIGGER update_appointments_updated_at
    BEFORE UPDATE ON appointments
    FOR EACH ROW
    EXECUTE FUNCTION update_appointments_updated_at();

-- Step 5: Add comments for documentation
COMMENT ON TABLE appointments IS 'Appointments between patients and psychometricians';
COMMENT ON COLUMN appointments.user_id IS 'Patient user ID (references users.id)';
COMMENT ON COLUMN appointments.psychometrician_id IS 'Psychometrician user ID (references users.id where role=psychometrician)';
COMMENT ON COLUMN appointments.date IS 'Appointment date (YYYY-MM-DD)';
COMMENT ON COLUMN appointments.start_time IS 'Appointment start time (HH:MM:SS)';
COMMENT ON COLUMN appointments.end_time IS 'Appointment end time (HH:MM:SS)';
COMMENT ON COLUMN appointments.status IS 'Appointment status (scheduled, confirmed, completed, cancelled, rescheduled)';
COMMENT ON COLUMN appointments.notes IS 'Additional notes or comments';

-- Step 6: Grant necessary permissions
GRANT ALL ON appointments TO authenticated;
GRANT USAGE ON SEQUENCE appointments_id_seq TO authenticated;

-- Step 7: Create a view for easy appointment queries with user names
CREATE OR REPLACE VIEW appointment_details AS
SELECT 
    a.id,
    a.date,
    a.start_time,
    a.end_time,
    a.status,
    a.notes,
    a.created_at,
    a.updated_at,
    
    -- Patient information
    p.id as patient_id,
    p.name as patient_name,
    p.email as patient_email,
    
    -- Psychometrician information
    ps.id as psychometrician_id,
    ps.name as psychometrician_name,
    ps.email as psychometrician_email
    
FROM appointments a
JOIN users p ON a.user_id = p.id
JOIN users ps ON a.psychometrician_id = ps.id
WHERE ps.role = 'psychometrician';

-- Grant permissions on the view
GRANT SELECT ON appointment_details TO authenticated;

-- Step 8: Verification queries (uncomment to test)
/*
-- Check if table was created successfully
SELECT table_name, column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'appointments'
ORDER BY ordinal_position;

-- Check available psychometricians
SELECT id, name, email, role, status
FROM users 
WHERE role = 'psychometrician' AND status = true
ORDER BY name;

-- Test appointment creation (replace with actual user IDs)
-- INSERT INTO appointments (user_id, psychometrician_id, date, start_time, end_time, notes)
-- VALUES (1, 2, '2025-01-20', '10:00:00', '11:00:00', 'Initial consultation');
*/
