{"expo": {"scheme": "capstone", "name": "capstone", "slug": "capstone", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/mainlogo.png", "userInterfaceStyle": "light", "newArchEnabled": true, "androidStatusBar": {"barStyle": "light-content", "backgroundColor": "#66948a", "translucent": true}, "splash": {"image": "./assets/mainlogo.png", "resizeMode": "contain", "backgroundColor": "#F5F9EE"}, "ios": {"supportsTablet": true, "infoPlist": {"UIViewControllerBasedStatusBarAppearance": true, "UIStatusBarStyle": "UIStatusBarStyleLightContent", "NSCameraUsageDescription": "This app needs access to camera for video consultations with mental health professionals.", "NSMicrophoneUsageDescription": "This app needs access to microphone for video consultations with mental health professionals."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/mainlogo.png", "backgroundColor": "#F5F9EE"}, "edgeToEdgeEnabled": true, "package": "com.bansity1.capstone", "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.MODIFY_AUDIO_SETTINGS", "android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE"]}, "web": {"favicon": "./assets/mainlogo.png"}, "plugins": ["expo-router", "expo-dev-client"]}}