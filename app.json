{"expo": {"scheme": "capstone", "name": "capstone", "slug": "capstone", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/mainlogo.png", "userInterfaceStyle": "light", "newArchEnabled": true, "androidStatusBar": {"barStyle": "light-content", "backgroundColor": "#66948a", "translucent": true}, "splash": {"image": "./assets/mainlogo.png", "resizeMode": "contain", "backgroundColor": "#F5F9EE"}, "ios": {"supportsTablet": true, "infoPlist": {"UIViewControllerBasedStatusBarAppearance": true, "UIStatusBarStyle": "UIStatusBarStyleLightContent"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/mainlogo.png", "backgroundColor": "#F5F9EE"}, "edgeToEdgeEnabled": true, "package": "com.bansity1.capstone"}, "web": {"favicon": "./assets/mainlogo.png"}, "plugins": ["expo-router"]}}