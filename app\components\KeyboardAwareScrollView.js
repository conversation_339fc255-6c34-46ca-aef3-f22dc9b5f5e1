import React, { useEffect, useRef, useState } from 'react';
import {
  KeyboardAvoidingView,
  ScrollView,
  Platform,
  Keyboard,
  Dimensions,
  View
} from 'react-native';

const { height: screenHeight } = Dimensions.get('window');

const KeyboardAwareScrollView = ({
  children,
  style,
  contentContainerStyle,
  keyboardVerticalOffset = 0,
  enableOnAndroid = true,
  extraScrollHeight = 20,
  ...props
}) => {
  const scrollViewRef = useRef(null);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      (e) => {
        setKeyboardHeight(e.endCoordinates.height);
        setIsKeyboardVisible(true);
      }
    );

    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardHeight(0);
        setIsKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  const scrollToInput = (reactNode) => {
    if (scrollViewRef.current && reactNode) {
      reactNode.measureInWindow((x, y, width, height) => {
        const keyboardY = screenHeight - keyboardHeight;
        const inputY = y + height;
        
        if (inputY > keyboardY) {
          const scrollOffset = inputY - keyboardY + extraScrollHeight;
          scrollViewRef.current.scrollTo({
            y: scrollOffset,
            animated: true,
          });
        }
      });
    }
  };

  const behavior = Platform.OS === 'ios' ? 'padding' : enableOnAndroid ? 'height' : undefined;

  return (
    <KeyboardAvoidingView
      style={[{ flex: 1 }, style]}
      behavior={behavior}
      keyboardVerticalOffset={keyboardVerticalOffset}
    >
      <ScrollView
        ref={scrollViewRef}
        style={{ flex: 1 }}
        contentContainerStyle={[
          {
            flexGrow: 1,
            paddingBottom: isKeyboardVisible ? keyboardHeight / 2 : 0,
          },
          contentContainerStyle,
        ]}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        {...props}
      >
        {children}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default KeyboardAwareScrollView;
