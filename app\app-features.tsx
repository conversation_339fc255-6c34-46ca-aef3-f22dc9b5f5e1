import React, { useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Animated,
  Dimensions,
  StatusBar,
} from 'react-native';
import { useRouter } from 'expo-router';

const { width } = Dimensions.get('window');

const AppFeatures = () => {
  const router = useRouter();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();

    // Auto-transition to welcome screen after 4.5 seconds
    const timer = setTimeout(() => {
      router.push('/(auth)/welcome');
    }, 4500);

    return () => clearTimeout(timer);
  }, [router]);

  const features = [
    {
      icon: require('../assets/Mood tracker.png'),
      title: 'Mood tracker',
      color: '#6B9142',
      description: 'Track your daily emotions and mood patterns'
    },
    {
      icon: require('../assets/AI chatbot.png'),
      title: 'AI chatbot',
      color: '#5A7C32',
      description: 'Chat with Aira, your AI mental health companion'
    },
    {
      icon: require('../assets/Stress Management.png'),
      title: 'Stress Management',
      color: '#7BA055',
      description: 'Learn techniques to manage stress effectively'
    },
    {
      icon: require('../assets/Mental Health Assessment.png'),
      title: 'Mental Health\nAssessment',
      color: '#4A6B2A',
      description: 'Take comprehensive mental health evaluations'
    },
    {
      icon: require('../assets/Calendar.png'),
      title: 'Appointment',
      color: '#8FB069',
      description: 'Schedule and manage your therapy sessions'
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#66948a" barStyle="light-content" translucent />
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        {/* Logo */}
        <View style={styles.logoContainer}>
          <Image
            source={require('../assets/mainlogo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.logoText}>mentalease</Text>
        </View>

        {/* App Features Title */}
        <Text style={styles.title}>App features</Text>

        {/* Features Grid */}
        <View style={styles.featuresContainer}>
          {features.map((feature, index) => (
            <Animated.View
              key={index}
              style={[
                styles.featureCard,
                {
                  opacity: fadeAnim,
                  transform: [
                    {
                      translateY: Animated.add(
                        slideAnim,
                        new Animated.Value(index * 15)
                      ),
                    },
                  ],
                },
              ]}
            >
              <View style={styles.featureCardContent}>
                <View style={[styles.featureIconContainer, { backgroundColor: feature.color }]}>
                  <Image
                    source={feature.icon}
                    style={styles.featureIconImage}
                    resizeMode="contain"
                  />
                </View>
                <View style={styles.featureTextContainer}>
                  <Text style={styles.featureTitle}>{feature.title}</Text>
                  <Text style={styles.featureDescription}>{feature.description}</Text>
                </View>
              </View>
            </Animated.View>
          ))}
        </View>



        {/* Partnership Text */}
        <View style={styles.partnershipContainer}>
          <Text style={styles.partnershipText}>
            in Partnership with Sanda Diagnostic Center
          </Text>
        </View>
      </Animated.View>
    </SafeAreaView>
  );
};

export default AppFeatures;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 12,
  },
  logo: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginBottom: 4,
  },
  logoText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2D5016',
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    color: '#2D5016',
    marginBottom: 16,
    textAlign: 'center',
  },
  featuresContainer: {
    flex: 1,
    width: '100%',
    paddingHorizontal: 8,
  },
  featureCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.06,
    shadowRadius: 3,
    elevation: 2,
  },
  featureCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  featureIconContainer: {
    width: 42,
    height: 42,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 1,
  },
  featureIconImage: {
    width: 22,
    height: 22,
    tintColor: '#FFFFFF',
  },
  featureTextContainer: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2D5016',
    marginBottom: 1,
    lineHeight: 16,
  },
  featureDescription: {
    fontSize: 11,
    color: '#666666',
    lineHeight: 14,
  },

  partnershipContainer: {
    marginTop: 8,
    marginBottom: 16,
  },
  partnershipText: {
    fontSize: 10,
    color: '#666666',
    textAlign: 'center',
  },
});
