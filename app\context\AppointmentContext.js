// AppointmentContext.js - Context for appointment data
import { createContext, useContext, useState, useEffect } from 'react';
import { supabase, supabaseUrl, supabaseAnonKey } from '../lib/supabase';
import { useUser } from './UserContext';

// Create the context
const AppointmentContext = createContext();

// Helper functions
const getDates = () => {
  const dates = [];
  const today = new Date();

  for (let i = 0; i < 14; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);
    dates.push(date);
  }

  return dates;
};

const formatDate = (date) => {
  const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  return {
    day: days[date.getDay()],
    date: date.getDate(),
    month: months[date.getMonth()],
    year: date.getFullYear(),
    fullDate: `${months[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`
  };
};

const formatDateString = (date) => {
  // Use local date formatting to avoid timezone issues
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Create a provider component
export const AppointmentProvider = ({ children }) => {
  const { userData } = useUser(); // Get user data from UserContext
  const [appointments, setAppointments] = useState([]);
  const [allAppointments, setAllAppointments] = useState([]); // All appointments for availability checking
  const [upcomingAppointments, setUpcomingAppointments] = useState([]);
  const [pastAppointments, setPastAppointments] = useState([]);
  const [psychometricians, setPsychometricians] = useState([]);
  const [schedules, setSchedules] = useState([]);

  // Fetch psychometricians directly from users table
  const fetchPsychometricians = async () => {
    try {
      console.log('🔍 Fetching psychometricians from users table...');

      // Use direct fetch API since the custom supabase client has limited functionality
      const response = await fetch(`${supabaseUrl}/rest/v1/users?role=eq.psychometrician&status=eq.true&select=id,name,email,role,status,created_at,updated_at&order=name.asc`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
          'Authorization': `Bearer ${supabaseAnonKey}`
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ HTTP error:', response.status, errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Raw response from users table:', data);
      console.log('Data type:', typeof data);
      console.log('Is array:', Array.isArray(data));

      if (data && Array.isArray(data) && data.length > 0) {
        console.log(`✅ Found ${data.length} psychometricians in users table`);

        // Transform user data to psychometrician format
        const psychometricians = data.map(user => ({
          id: user.id,
          user_id: user.id,  // Same as id since we're using users directly
          name: user.name || user.email.split('@')[0],
          email: user.email,
          specialty: 'Psychometrician',
          experience: '5+ years',
          rating: 4.8,
          image: '👨‍⚕️',
          available: true,
          created_at: user.created_at,
          updated_at: user.updated_at
        }));

        setPsychometricians(psychometricians);
        console.log('✅ Psychometricians loaded:', psychometricians.map(p => p.name));
      } else {
        console.log('⚠️ No psychometricians found in users table, using fallback data');
        // Fallback to mock data if no psychometricians in database
        setPsychometricians([
          {
            id: 1,
            user_id: 1,
            name: 'Dr. Sarah Johnson',
            email: '<EMAIL>',
            specialty: 'Anxiety & Depression',
            experience: '10 years',
            rating: 4.9,
            image: '👩‍⚕️',
            available: true
          },
          {
            id: 2,
            user_id: 2,
            name: 'Dr. Michael Chen',
            email: '<EMAIL>',
            specialty: 'Stress Management',
            experience: '8 years',
            rating: 4.7,
            image: '👨‍⚕️',
            available: true
          }
        ]);
      }
    } catch (error) {
      console.error('❌ Error fetching psychometricians:', error);
      // Fallback to mock data if database fetch fails
      setPsychometricians([
        {
          id: 1,
          user_id: 1,
          name: 'Dr. Sarah Johnson',
          email: '<EMAIL>',
          specialty: 'Anxiety & Depression',
          experience: '10 years',
          rating: 4.9,
          image: '👩‍⚕️',
          available: true
        },
        {
          id: 2,
          user_id: 2,
          name: 'Dr. Michael Chen',
          email: '<EMAIL>',
          specialty: 'Stress Management',
          experience: '8 years',
          rating: 4.7,
          image: '👨‍⚕️',
          available: true
        }
      ]);
    }
  };

  // Fetch schedules from database
  const fetchSchedules = async () => {
    try {
      console.log('🔄 Fetching schedules from database...');
      console.log('🔄 Supabase URL:', supabaseUrl);
      console.log('🔄 API Key (first 20 chars):', supabaseAnonKey.substring(0, 20) + '...');

      const url = `${supabaseUrl}/rest/v1/schedules?select=*&order=psychometrician_id.asc,date.asc`;
      console.log('🔄 Request URL:', url);

      // Use direct fetch API like psychometricians
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
          'Authorization': `Bearer ${supabaseAnonKey}`
        }
      });

      console.log('🔄 Response status:', response.status);
      console.log('🔄 Response ok:', response.ok);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Response error text:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Schedules fetched:', data);
      console.log('✅ Schedules count:', data?.length || 0);
      console.log('✅ First schedule:', data?.[0]);
      console.log('✅ Schedule columns:', data?.[0] ? Object.keys(data[0]) : 'No data');
      setSchedules(data || []);
    } catch (error) {
      console.error('❌ Error fetching schedules:', error);
      console.error('❌ Error details:', error.message);
    }
  };

  useEffect(() => {
    fetchPsychometricians();
    fetchSchedules();
    fetchAllAppointments(); // Fetch all appointments for availability checking
  }, []);

  useEffect(() => {
    if (userData) {
      fetchAppointments();
    }
  }, [userData]);

  // Update upcoming and past appointments whenever appointments change
  useEffect(() => {
    if (appointments.length > 0) {
      const now = new Date();

      const upcoming = appointments.filter(appointment => {
        if (appointment.status !== 'scheduled') return false;

        // Create a date object from the appointment date and start time
        const appointmentDateTime = new Date(`${appointment.date}T${appointment.start_time}`);
        return appointmentDateTime > now;
      });

      const past = appointments.filter(appointment => {
        // Create a date object from the appointment date and start time
        const appointmentDateTime = new Date(`${appointment.date}T${appointment.start_time}`);
        return appointmentDateTime <= now;
      }).map(appointment => {
        // Auto-update status to 'completed' for past appointments if they're still 'scheduled'
        if (appointment.status === 'scheduled') {
          return { ...appointment, status: 'completed' };
        }
        return appointment;
      });

      console.log('📊 Filtering appointments:', {
        total: appointments.length,
        upcoming: upcoming.length,
        past: past.length,
        currentTime: now.toISOString()
      });

      if (appointments.length > 0) {
        console.log('📋 All appointments:', appointments.map(apt => ({
          id: apt.id,
          date: apt.date,
          start_time: apt.start_time,
          status: apt.status,
          isUpcoming: new Date(`${apt.date}T${apt.start_time}`) > now
        })));
      }

      setUpcomingAppointments(upcoming);
      setPastAppointments(past);
    } else {
      setUpcomingAppointments([]);
      setPastAppointments([]);
    }
  }, [appointments]);

  const fetchAppointments = async () => {
    if (!userData?.id) {
      console.log('No user data available for fetching appointments');
      return;
    }

    try {
      const { data, error } = await supabase
        .from('appointment')
        .select('*')
        .eq('user_id', userData.id)
        .order('date', { ascending: true });

      if (error) throw error;

      console.log('📥 Fetched appointments from database:', data);
      console.log('👤 Current user ID:', userData.id);

      setAppointments(data || []);
    } catch (error) {
      console.error('Error fetching appointments:', error);
    }
  };

  // Fetch all appointments for availability checking (not user-specific)
  const fetchAllAppointments = async () => {
    try {
      // Use direct fetch API
      const response = await fetch(`${supabaseUrl}/rest/v1/appointment?select=*&order=date.asc,start_time.asc`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
          'Authorization': `Bearer ${supabaseAnonKey}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('All appointments fetched for availability checking:', data?.length || 0);
      console.log('All appointments data:', data);
      setAllAppointments(data || []);
    } catch (error) {
      console.error('Error fetching all appointments:', error);
    }
  };

  const addAppointment = async (newAppointment) => {
    if (!userData?.id) {
      throw new Error('User not authenticated');
    }

    try {
      // First, check if the time slot is still available
      const timeSlot = {
        start_time: newAppointment.start_time,
        end_time: newAppointment.end_time
      };

      const isAvailable = isTimeSlotAvailable(
        newAppointment.date,
        timeSlot,
        parseInt(newAppointment.psychometricianId)
      );

      if (!isAvailable) {
        throw new Error('This time slot is no longer available. Please select a different time.');
      }

      const appointmentData = {
        user_id: parseInt(userData.id), // Convert to integer to match users table
        psychometrician_id: parseInt(newAppointment.psychometricianId), // This is now the user ID of the psychometrician
        date: newAppointment.date,
        start_time: newAppointment.start_time,
        end_time: newAppointment.end_time,
        status: 'scheduled',
        notes: newAppointment.notes || '',
        consultation_fee: newAppointment.consultation_fee || 500.00, // Default consultation fee
        payment_method: newAppointment.payment_method || 'pending'
      };

      console.log('Appointment data to insert:', appointmentData);
      console.log('Data types:', {
        user_id_type: typeof appointmentData.user_id,
        user_id_value: appointmentData.user_id,
        psychometrician_id_type: typeof appointmentData.psychometrician_id,
        psychometrician_id_value: appointmentData.psychometrician_id
      });

      const { data, error } = await supabase
        .from('appointment')
        .insert([appointmentData])
        .select();

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      console.log('Appointment created successfully:', data);

      // Immediately update both appointments and allAppointments state
      const createdAppointment = data[0];
      setAppointments([...appointments, createdAppointment]);
      setAllAppointments([...allAppointments, createdAppointment]);

      // Refresh appointments from database to ensure we have the latest data
      await fetchAppointments();
      await fetchAllAppointments(); // Also refresh all appointments for availability checking

      return createdAppointment;
    } catch (error) {
      console.error('Error adding appointment:', error);
      throw error;
    }
  };

  const updateAppointment = async (id, updatedData) => {
    try {
      const { error } = await supabase
        .from('appointment')
        .update({
          psychometrician_id: updatedData.psychometricianId,
          date: updatedData.date,
          start_time: updatedData.start_time,
          end_time: updatedData.end_time,
          status: updatedData.status,
          notes: updatedData.notes
        })
        .eq('id', id)
        .eq('user_id', userData.id);

      if (error) throw error;

      setAppointments(
        appointments.map(appointment =>
          appointment.id === id ? { ...appointment, ...updatedData } : appointment
        )
      );
    } catch (error) {
      console.error('Error updating appointment:', error);
      throw error;
    }
  };

  // Cancel an appointment
  const cancelAppointment = (appointmentId) => {
    const updatedAppointments = appointments.map(appointment =>
      appointment.id === appointmentId
        ? { ...appointment, status: 'cancelled' }
        : appointment
    );

    setAppointments(updatedAppointments);

    // Update upcoming appointments
    setUpcomingAppointments(
      updatedAppointments.filter(
        appointment =>
          appointment.status === 'scheduled' &&
          new Date(`${appointment.date}T${appointment.time}`) > new Date()
      )
    );

    return updatedAppointments.find(a => a.id === appointmentId);
  };

  // Reschedule an appointment
  const rescheduleAppointment = async (appointmentId, newDate, newTimeSlot) => {
    console.log('🚀 RESCHEDULE FUNCTION CALLED!');
    console.log('📋 Raw parameters received:', { appointmentId, newDate, newTimeSlot });
    console.log('👤 User data:', userData);

    if (!userData?.id) {
      console.error('❌ User not authenticated');
      throw new Error('User not authenticated');
    }

    try {
      console.log('✅ Starting reschedule process...');
      console.log('Reschedule params:', { appointmentId, newDate, newTimeSlot, userData: userData.id });
      console.log('Current appointments:', appointments.map(a => ({ id: a.id, user_id: a.user_id })));

      // First, check if the new time slot is available (excluding the current appointment)
      const isAvailable = isTimeSlotAvailable(
        newDate,
        newTimeSlot,
        null, // Check for all psychometricians since we'll get it from existing appointment
        appointmentId
      );

      if (!isAvailable) {
        throw new Error('This time slot is no longer available. Please select a different time.');
      }

      const updatedData = {
        date: newDate,
        start_time: newTimeSlot.start_time,
        end_time: newTimeSlot.end_time
      };

      // Ensure appointmentId is a valid number
      const appointmentIdNum = parseInt(appointmentId);
      if (isNaN(appointmentIdNum)) {
        throw new Error('Invalid appointment ID');
      }

      console.log('Updating appointment with ID:', appointmentIdNum, 'Data:', updatedData);

      // Update in database with proper user verification
      const userIdNum = parseInt(userData.id);
      console.log('User ID for verification:', userIdNum, 'type:', typeof userIdNum);

      // Verify the appointment exists and belongs to the user using local state
      console.log('Verifying appointment with ID:', appointmentIdNum);
      console.log('Available appointments:', appointments.map(a => ({ id: a.id, user_id: a.user_id })));

      const existingAppointment = appointments.find(appointment => appointment.id == appointmentIdNum);

      if (!existingAppointment) {
        console.error('No appointment found with ID:', appointmentIdNum);
        throw new Error('Appointment not found');
      }

      console.log('Found appointment:', existingAppointment);

      // Convert both to numbers for comparison to avoid string vs number issues
      const appointmentUserId = parseInt(existingAppointment.user_id);
      console.log('User ID comparison:', {
        appointmentUserId,
        userIdNum,
        appointmentUserIdType: typeof appointmentUserId,
        userIdNumType: typeof userIdNum
      });

      if (appointmentUserId !== userIdNum) {
        console.error('User ID mismatch:', { found: appointmentUserId, expected: userIdNum });
        throw new Error('Access denied - appointment belongs to another user');
      }

      // Now update the appointment
      const { data, error } = await supabase
        .from('appointment')
        .update(updatedData)
        .eq('id', appointmentIdNum)
        .select();

      if (error) {
        console.error('Supabase error:', error);
        throw new Error(`Failed to update appointment: ${error.message}`);
      }

      console.log('Update successful, data returned:', data);

      // Verify the update actually happened
      if (!data || data.length === 0) {
        console.error('No data returned from update - appointment may not exist or update failed');
        throw new Error('Update failed - no data returned');
      }

      console.log('Database update result:', data);

      // Update local state
      const updatedAppointments = appointments.map(appointment =>
        appointment.id == appointmentIdNum
          ? { ...appointment, ...updatedData }
          : appointment
      );

      setAppointments(updatedAppointments);

      // Also update allAppointments to keep availability checking accurate
      const updatedAllAppointments = allAppointments.map(appointment =>
        appointment.id == appointmentIdNum
          ? { ...appointment, ...updatedData }
          : appointment
      );
      setAllAppointments(updatedAllAppointments);

      console.log('Appointment rescheduled successfully');
      return updatedAppointments.find(a => a.id == appointmentIdNum);
    } catch (error) {
      console.error('Error rescheduling appointment:', error);
      throw error;
    }
  };

  // Get available time slots for a psychometrician on a specific date
  const getTimeSlots = (psychometricianId = null, selectedDate = null) => {
    if (!psychometricianId || !selectedDate) {
      // Return empty array if no specific psychometrician or date
      return [];
    }

    // Check if schedules are loaded
    if (!schedules || schedules.length === 0) {
      console.log('⏳ Schedules not loaded yet');
      return [];
    }

    // Format the selected date to match database format (YYYY-MM-DD)
    const selectedDateStr = selectedDate instanceof Date ? formatDateString(selectedDate) : selectedDate;

    // Find schedules for this psychometrician on the exact selected date
    const psychometricianSchedules = schedules.filter(schedule => {
      // Safely check if schedule has required properties
      if (!schedule || typeof schedule !== 'object') {
        return false;
      }

      const matchesPsychometrician = schedule.psychometrician_id === psychometricianId;
      const matchesDate = schedule.date === selectedDateStr;

      return matchesPsychometrician && matchesDate;
    });

    if (psychometricianSchedules.length === 0) {
      return []; // No schedules available for this day
    }

    // Generate time slots based on schedules
    const timeSlots = [];
    psychometricianSchedules.forEach(schedule => {
      const startTime = schedule.start_time;
      const endTime = schedule.end_time;

      // Convert time strings to minutes for easier calculation
      const startMinutes = timeToMinutes(startTime);
      const endMinutes = timeToMinutes(endTime);

      // Generate 1-hour slots
      for (let minutes = startMinutes; minutes < endMinutes; minutes += 60) {
        const slotStart = minutesToTime(minutes);
        const slotEnd = minutesToTime(minutes + 60);

        timeSlots.push({
          start_time: slotStart,
          end_time: slotEnd,
          display: formatTimeSlot(slotStart, slotEnd)
        });
      }
    });

    return timeSlots;
  };

  // Helper function to convert time string to minutes
  const timeToMinutes = (timeStr) => {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  };

  // Helper function to convert minutes to time string
  const minutesToTime = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  };

  // Helper function to format time slot for display
  const formatTimeSlot = (startTime, endTime) => {
    const formatTime = (time) => {
      const [hours, minutes] = time.split(':').map(Number);
      const period = hours >= 12 ? 'PM' : 'AM';
      const displayHours = hours > 12 ? hours - 12 : hours === 0 ? 12 : hours;
      return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
    };

    return `${formatTime(startTime)} - ${formatTime(endTime)}`;
  };

  // Book a new appointment
  const bookAppointment = (psychometricianId, date, timeSlot, notes = '') => {
    // Validate inputs
    if (!psychometricianId || !date || !timeSlot) {
      throw new Error('Missing required appointment information');
    }

    // Validate timeSlot object
    if (!timeSlot.start_time || !timeSlot.end_time) {
      console.error('Invalid timeSlot object:', timeSlot);
      throw new Error('Invalid time slot: missing start_time or end_time');
    }

    // Format date if it's a Date object
    const formattedDate = date instanceof Date ? formatDateString(date) : date;

    // Create and add the appointment
    const appointment = addAppointment({
      psychometricianId,
      date: formattedDate,
      start_time: timeSlot.start_time,
      end_time: timeSlot.end_time,
      notes
    });

    return appointment;
  };

  // Get psychometrician details by ID
  const getPsychometricianById = (psychometricianId) => {
    return psychometricians.find(psychometrician => psychometrician.id === psychometricianId);
  };

  // Get upcoming appointments with psychometrician details
  const getUpcomingAppointments = () => {
    console.log('🔍 getUpcomingAppointments called');
    console.log('📋 Raw upcoming appointments:', upcomingAppointments);
    console.log('👥 Available psychometricians:', psychometricians);

    const result = upcomingAppointments.map(appointment => {
      const psychometrician = getPsychometricianById(appointment.psychometrician_id);
      console.log(`🔗 Appointment ${appointment.id} -> Psychometrician ${appointment.psychometrician_id}:`, psychometrician);

      return {
        ...appointment,
        psychometrician: psychometrician
      };
    });

    console.log('✅ Final upcoming appointments with psychometrician data:', result);
    return result;
  };

  // Get past appointments with psychometrician details
  const getPastAppointments = () => {
    return pastAppointments.map(appointment => ({
      ...appointment,
      psychometrician: getPsychometricianById(appointment.psychometrician_id)
    }));
  };

  // Check if a specific time slot is available
  const isTimeSlotAvailable = (date, timeSlot, psychometricianId = null, excludeAppointmentId = null) => {
    // Validate inputs
    if (!date || !timeSlot) {
      console.error('isTimeSlotAvailable: Missing required parameters', { date, timeSlot });
      return false;
    }

    // Validate timeSlot object
    if (!timeSlot.start_time || !timeSlot.end_time) {
      console.error('isTimeSlotAvailable: Invalid timeSlot object', timeSlot);
      return false;
    }

    // Ensure allAppointments is available
    if (!allAppointments || !Array.isArray(allAppointments)) {
      console.warn('isTimeSlotAvailable: allAppointments not available, assuming slot is available');
      return true; // If we can't check, assume it's available
    }

    // Format date if it's a Date object
    const formattedDate = date instanceof Date ? formatDateString(date) : date;

    // Convert excludeAppointmentId to number for comparison
    const excludeId = excludeAppointmentId ? parseInt(excludeAppointmentId) : null;

    // Check for ANY overlapping appointments using ALL appointments (not just user's appointments)
    const hasConflict = allAppointments.some(appointment => {
      // Validate appointment object - be more thorough
      if (!appointment) {
        console.warn('isTimeSlotAvailable: Null appointment object');
        return false;
      }

      if (typeof appointment !== 'object') {
        console.warn('isTimeSlotAvailable: Appointment is not an object', appointment);
        return false;
      }

      if (!appointment.start_time || !appointment.end_time) {
        console.warn('isTimeSlotAvailable: Invalid appointment object missing time properties', appointment);
        return false;
      }

      // Skip if this is the appointment being excluded (for rescheduling)
      if (appointment.id === excludeId) return false;

      // Only check scheduled appointments
      if (appointment.status !== 'scheduled' && appointment.status !== 'rescheduled') return false;

      // Check if it's the same date
      if (appointment.date !== formattedDate) return false;

      // Check if it's the same psychometrician (if specified)
      if (psychometricianId && appointment.psychometrician_id !== psychometricianId) return false;

      // Normalize time formats to ensure consistent comparison
      // Database times might have seconds (HH:MM:SS) while UI times don't (HH:MM)
      const normalizeTime = (time) => {
        if (!time) return '';
        return time.substring(0, 5); // "HH:MM:SS" -> "HH:MM" or "HH:MM" -> "HH:MM"
      };

      const appointmentStart = normalizeTime(appointment.start_time);
      const appointmentEnd = normalizeTime(appointment.end_time);
      const slotStart = normalizeTime(timeSlot.start_time);
      const slotEnd = normalizeTime(timeSlot.end_time);

      // Two time ranges overlap if: start1 < end2 AND start2 < end1
      // Adjacent slots (e.g., 10:00-11:00 and 11:00-12:00) should NOT overlap
      const hasTimeOverlap = slotStart < appointmentEnd && appointmentStart < slotEnd;

      return hasTimeOverlap;
    });

    return !hasConflict;
  };

  // Context value with all appointment functions and data
  const contextValue = {
    bookAppointment,
    updateAppointment,
    cancelAppointment,
    rescheduleAppointment,
    getPsychometricianById,
    getUpcomingAppointments,
    getPastAppointments,
    isTimeSlotAvailable,
    psychometricians,
    schedules,
    appointments,
    allAppointments,
    upcomingAppointments,
    pastAppointments,
    getDates,
    getTimeSlots,
    formatDate,
    formatDateString,
    fetchPsychometricians,
    fetchSchedules,
    fetchAppointments,
    fetchAllAppointments
  };

  return (
    <AppointmentContext.Provider value={contextValue}>
      {children}
    </AppointmentContext.Provider>
  );
};

// Custom hook to use the context
export const useAppointment = () => {
  const context = useContext(AppointmentContext);
  if (!context) {
    throw new Error('useAppointment must be used within an AppointmentProvider');
  }
  return context;
};

export default AppointmentContext;


