import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Modal,
  FlatList,
  Alert,
  StatusBar
} from 'react-native';
import { useState } from 'react';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import BottomNavigation from '../../components/BottomNavigation';
import StandardHeader from '../../components/StandardHeader';

const MoodTracker = () => {
  const router = useRouter();
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDay, setSelectedDay] = useState(null);
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  // Removed unused state variables

  // Mock data for mood entries with more details
  const moodEntries = {
    '2025-01-12': {
      mood: 'Good',
      emoji: '😊',
      emotions: ['Happy', 'Relaxed'],
      journal: 'Had a great day today. Went for a walk and enjoyed the sunshine.'
    },
    '2025-01-17': {
      mood: 'Great',
      emoji: '😁',
      emotions: ['Excited', 'Energetic'],
      journal: 'Accomplished a lot at work today. Feeling productive and satisfied.'
    },
    '2025-01-04': {
      mood: 'Okay',
      emoji: '😐',
      emotions: ['Neutral', 'Calm'],
      journal: 'Nothing special today. Just a regular day.'
    },
    '2025-01-25': {
      mood: 'Sad',
      emoji: '😔',
      emotions: ['Disappointed', 'Tired'],
      journal: 'Feeling a bit down today. Need to rest and recharge.'
    },
  };

  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };

  const formatDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const renderCalendar = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayOfMonth = getFirstDayOfMonth(year, month);

    const days = [];
    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    // Add day names
    dayNames.forEach(day => {
      days.push(
        <View key={`header-${day}`} style={styles.dayNameCell}>
          <Text style={styles.dayNameText}>{day}</Text>
        </View>
      );
    });

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(<View key={`empty-${i}`} style={styles.emptyCell} />);
    }

    // Add cells for each day of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const dateString = formatDate(date);
      const hasMood = moodEntries[dateString];

      days.push(
        <TouchableOpacity
          key={`day-${day}`}
          style={[
            styles.dayCell,
            hasMood && styles.dayWithMood,
            selectedDay === dateString && styles.selectedDay
          ]}
          onPress={() => {
            setSelectedDay(dateString);
            router.push({
              pathname: '/(main)/mental-health/mood-journal',
              params: {
                date: dateString,
                fromTracker: 'true'
              }
            });
          }}
        >
          <Text style={styles.dayText}>{day}</Text>
          {hasMood && (
            <Text style={styles.moodEmoji}>{hasMood.emoji}</Text>
          )}
        </TouchableOpacity>
      );
    }

    return <View style={styles.calendarGrid}>{days}</View>;
  };

  const prevMonth = () => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(newMonth.getMonth() - 1);
    setCurrentMonth(newMonth);
  };

  const nextMonth = () => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(newMonth.getMonth() + 1);
    setCurrentMonth(newMonth);
  };

  const handleViewHistory = () => {
    // Get all entries and sort by date (newest first)
    const entriesArray = Object.entries(moodEntries).map(([date, entry]) => ({
      date,
      ...entry
    })).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    if (entriesArray.length === 0) {
      Alert.alert('No Entries', 'You have not recorded any mood entries yet.');
      return;
    }

    setShowHistoryModal(true);
  };

  const handleEditEntry = (entry: any) => {
    setShowHistoryModal(false);
    router.push({
      pathname: '/(main)/mental-health/mood-journal',
      params: {
        date: entry.date,
        edit: 'true',
        mood: entry.mood,
        journal: entry.journal,
        emotions: JSON.stringify(entry.emotions),
        fromTracker: 'true'
      }
    });
  };

  const handleDeleteEntry = (date: string) => {
    Alert.alert(
      'Delete Entry',
      'Are you sure you want to delete this entry?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Delete',
          onPress: () => {
            // In a real app, you would delete from database
            // For now, we'll just close the modal
            console.log('Deleting entry for date:', date);
            setShowHistoryModal(false);
            Alert.alert('Entry Deleted', 'Your entry has been deleted successfully.');
          },
          style: 'destructive'
        }
      ]
    );
  };

  const renderHistoryItem = ({ item }) => {
    const date = new Date(item.date);
    const formattedDate = date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });

    return (
      <View style={styles.historyItem}>
        <View style={styles.historyItemHeader}>
          <Text style={styles.historyItemDate}>{formattedDate}</Text>
          <Text style={styles.historyItemMood}>{item.emoji} {item.mood}</Text>
        </View>

        {item.emotions.length > 0 && (
          <View style={styles.historyItemEmotions}>
            {item.emotions.map((emotion: string, index: number) => (
              <View key={index} style={styles.emotionChip}>
                <Text style={styles.emotionChipText}>{emotion}</Text>
              </View>
            ))}
          </View>
        )}

        {item.journal && (
          <Text style={styles.historyItemJournal} numberOfLines={2}>
            {item.journal}
          </Text>
        )}

        <View style={styles.historyItemActions}>
          <TouchableOpacity
            style={styles.historyItemButton}
            onPress={() => handleEditEntry(item)}
          >
            <Text style={styles.historyItemButtonText}>Edit</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.historyItemButton, styles.deleteButton]}
            onPress={() => handleDeleteEntry(item.date)}
          >
            <Text style={[styles.historyItemButtonText, styles.deleteButtonText]}>Delete</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  // Removed unused Supabase functions that referenced userData

  return (
    <View style={styles.container}>
      <StandardHeader title="Mood Journal & Tracker" />

      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.viewHistoryButton}
            onPress={handleViewHistory}
          >
            <Text style={styles.viewHistoryButtonText}>View History</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.analyticsButton}
            onPress={() => router.push('/(main)/mental-health/mood-analytics')}
          >
            <Text style={styles.analyticsButtonText}>📊 Analytics</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.monthSelector}>
          <TouchableOpacity onPress={prevMonth} style={styles.monthButton}>
            <Text style={styles.monthButtonText}>←</Text>
          </TouchableOpacity>
          <Text style={styles.monthText}>
            {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
          </Text>
          <TouchableOpacity onPress={nextMonth} style={styles.monthButton}>
            <Text style={styles.monthButtonText}>→</Text>
          </TouchableOpacity>
        </View>

        {renderCalendar()}
      </ScrollView>

      {/* History Modal */}
      <Modal
        visible={showHistoryModal}
        transparent={true}
        animationType="none"
        onRequestClose={() => setShowHistoryModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Mood History</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowHistoryModal(false)}
              >
                <Text style={styles.closeButtonText}>×</Text>
              </TouchableOpacity>
            </View>

            <FlatList
              data={Object.entries(moodEntries).map(([date, entry]) => ({
                date,
                ...entry
              })).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())}
              renderItem={renderHistoryItem}
              keyExtractor={item => item.date}
              contentContainerStyle={styles.historyList}
            />
          </View>
        </View>
      </Modal>

      <BottomNavigation />
    </View>
  );
};

export default MoodTracker;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },

  content: {
    padding: 20,
    backgroundColor: '#F5F9EE',
    paddingBottom: 80, // Space for bottom navigation
  },
  actionButtons: {
    marginBottom: 15,
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
  },
  viewHistoryButton: {
    backgroundColor: '#6B9142',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 0.45,
  },
  viewHistoryButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 14,
  },
  analyticsButton: {
    backgroundColor: '#66948a',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 0.45,
  },
  analyticsButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 14,
  },
  monthSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  monthButton: {
    padding: 10,
  },
  monthButtonText: {
    color: '#6B9142',
    fontSize: 18,
    fontWeight: 'bold',
  },
  monthText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: 'bold',
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  dayNameCell: {
    width: '14%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 5,
  },
  dayNameText: {
    color: '#6B9142',
    fontWeight: 'bold',
  },
  emptyCell: {
    width: '14%',
    aspectRatio: 1,
  },
  dayCell: {
    width: '14%',
    aspectRatio: 1,
    backgroundColor: '#E8F3D8',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 5,
  },
  dayWithMood: {
    backgroundColor: '#D1E7B1',
  },
  selectedDay: {
    backgroundColor: '#6B9142',
  },
  dayText: {
    color: '#666666',
    fontWeight: '500',
  },
  moodEmoji: {
    fontSize: 12,
    marginTop: 2,
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6B9142',
  },
  closeButton: {
    padding: 5,
  },
  closeButtonText: {
    fontSize: 24,
    color: '#666666',
    fontWeight: 'bold',
  },
  historyList: {
    padding: 15,
  },
  historyItem: {
    backgroundColor: '#F5F9EE',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  historyItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  historyItemDate: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666666',
  },
  historyItemMood: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#6B9142',
  },
  historyItemEmotions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 10,
  },
  emotionChip: {
    backgroundColor: '#E8F3D8',
    borderRadius: 15,
    paddingVertical: 4,
    paddingHorizontal: 8,
    marginRight: 5,
    marginBottom: 5,
  },
  emotionChipText: {
    fontSize: 12,
    color: '#6B9142',
  },
  historyItemJournal: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 10,
  },
  historyItemActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  historyItemButton: {
    backgroundColor: '#6B9142',
    borderRadius: 15,
    paddingVertical: 5,
    paddingHorizontal: 15,
    marginLeft: 10,
  },
  historyItemButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  deleteButton: {
    backgroundColor: '#FF6B6B',
  },
  deleteButtonText: {
    color: '#FFFFFF',
  },
});


