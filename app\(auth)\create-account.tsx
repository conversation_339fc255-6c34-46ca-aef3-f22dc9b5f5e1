import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Image,
  Animated,
  StatusBar
} from 'react-native';
import { useRouter } from 'expo-router';
import { useUser } from '../context/UserContext';
import { signUp } from '../lib/auth-service';
import AsyncStorage from '@react-native-async-storage/async-storage';

const CreateAccount = () => {
  const router = useRouter();
  const { updateUserData } = useUser();
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Function to clear user-specific AsyncStorage data to prevent data leakage between accounts
  const clearUserSpecificAsyncStorage = async () => {
    try {
      console.log('🧹 Clearing AsyncStorage to prevent data leakage between accounts...');

      // Assessment-related keys
      const assessmentKeys = [
        // DASS assessment
        'dass_saved_results',
        'dass_result_timestamp',
        'dass_answers',
        'dass_currentQuestion',
        'dass_progress',

        // PSS assessment
        'pss_saved_results',
        'pss_result_timestamp',
        'pss_answers',
        'pss_currentQuestion',
        'pss_progress',

        // Other potential assessments
        'gad7_saved_results',
        'gad7_result_timestamp',
        'gad7_answers',
        'gad7_currentQuestion',
        'gad7_progress',

        'phq9_saved_results',
        'phq9_result_timestamp',
        'phq9_answers',
        'phq9_currentQuestion',
        'phq9_progress',

        // Mood tracking
        'savedMoods',

        // Chat history (user-specific)
        'chatHistory'
      ];

      // Clear all assessment-related data
      await Promise.all(assessmentKeys.map(key => AsyncStorage.removeItem(key)));

      console.log('✅ AsyncStorage cleared successfully');
    } catch (error) {
      console.error('❌ Error clearing AsyncStorage:', error);
    }
  };

  const handleCreateAccount = async () => {
    // Input validation with specific error messages
    if (!email && !username && !password) {
      Alert.alert('Input Required', 'Please fill in all required fields to create your account.');
      return;
    }

    if (!email) {
      Alert.alert('Email Required', 'Please enter your email address to continue.');
      return;
    }

    if (!username) {
      Alert.alert('Username Required', 'Please choose a username for your account.');
      return;
    }

    if (!password) {
      Alert.alert('Password Required', 'Please create a password for your account.');
      return;
    }

    if (!confirmPassword) {
      Alert.alert('Confirm Password', 'Please confirm your password to continue.');
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      Alert.alert('Invalid Email', 'Please enter a valid email address.');
      return;
    }

    // Username validation
    if (username.length < 3) {
      Alert.alert('Invalid Username', 'Username must be at least 3 characters long.');
      return;
    }

    if (username.length > 20) {
      Alert.alert('Invalid Username', 'Username must be less than 20 characters long.');
      return;
    }

    // Password validation
    if (password.length < 6) {
      Alert.alert('Weak Password', 'Password must be at least 6 characters long.');
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert('Password Mismatch', 'The passwords you entered do not match. Please try again.');
      return;
    }

    setIsLoading(true);

    try {
      console.log('Attempting to sign up with Supabase...');

      // Use our Supabase auth service
      const { data, error } = await signUp(email, username, password);

      if (error) {
        throw error;
      }

      console.log('Sign up successful:', data);

      // 🚨 CRITICAL: Clear AsyncStorage to prevent data leakage between accounts
      await clearUserSpecificAsyncStorage();

      // Update user data in context
      updateUserData({
        id: data.user?.id || data.id || 'user_' + Math.random().toString(36).substring(2),
        email: email,
        emailVerified: false,
        hasCompletedProfile: false
      });

      // Show success message
      Alert.alert(
        "Account Created Successfully! 🎉",
        `Welcome to MentalEase, ${username}! Your account has been created successfully. We've sent a verification code to ${email}. Please check your email to verify your account.`,
        [
          {
            text: "Continue to Verification",
            style: "default",
            onPress: () => router.push('/(auth)/email-verification')
          }
        ],
        { cancelable: false }
      );
    } catch (error) {
      console.error('Error creating account:', error.message);

      // Provide specific error messages based on error type
      let alertTitle = "Account Creation Failed ❌";
      let alertMessage = "";

      if (error.message.includes('User with this email already exists')) {
        alertTitle = "Email Already Registered ❌";
        alertMessage = "An account with this email address already exists. Please use a different email or try signing in instead.";
      } else if (error.message.includes('Username is already taken')) {
        alertTitle = "Username Unavailable ❌";
        alertMessage = "This username is already taken. Please choose a different username.";
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        alertTitle = "Connection Error ❌";
        alertMessage = "Unable to connect to the server. Please check your internet connection and try again.";
      } else if (error.message.includes('timeout')) {
        alertTitle = "Request Timeout ❌";
        alertMessage = "The request took too long to complete. Please try again.";
      } else {
        alertTitle = "Registration Error ❌";
        alertMessage = error.message || "An unexpected error occurred while creating your account. Please try again later.";
      }

      Alert.alert(
        alertTitle,
        alertMessage,
        [
          {
            text: 'Try Again',
            style: 'default'
          },
          {
            text: 'Need Help?',
            style: 'cancel',
            onPress: () => {
              Alert.alert(
                'Need Help? 🤝',
                'If you continue to experience issues:\n\n• Try using a different email or username\n• Check your internet connection\n• Contact <NAME_EMAIL>',
                [{ text: 'OK', style: 'default' }]
              );
            }
          }
        ]
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#66948a" barStyle="light-content" translucent />
      {/* Back Button */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Logo */}
            <View style={styles.logoContainer}>
              <Image
                source={require('../../assets/mainlogo.png')}
                style={styles.logo}
                resizeMode="contain"
              />
              <Text style={styles.logoText}>mentalease</Text>
            </View>

            {/* Title */}
            <Text style={styles.title}>Create Account</Text>

            {/* Form */}
            <View style={styles.formContainer}>
              <TextInput
                style={[styles.input, { marginBottom: 16 }]}
                placeholder="Email Address"
                placeholderTextColor="#999999"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                returnKeyType="next"
                autoCorrect={false}
                textContentType="emailAddress"
              />

              <TextInput
                style={[styles.input, { marginBottom: 16 }]}
                placeholder="Username"
                placeholderTextColor="#999999"
                value={username}
                onChangeText={setUsername}
                autoCapitalize="none"
                returnKeyType="next"
                autoCorrect={false}
                textContentType="username"
              />

              <TextInput
                style={styles.input}
                placeholder="Password"
                placeholderTextColor="#999999"
                value={password}
                onChangeText={setPassword}
                secureTextEntry
                returnKeyType="next"
                autoCorrect={false}
                textContentType="newPassword"
              />

              <TextInput
                style={styles.input}
                placeholder="Confirm Password"
                placeholderTextColor="#999999"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry
                returnKeyType="done"
                onSubmitEditing={Keyboard.dismiss}
                autoCorrect={false}
                textContentType="newPassword"
              />

              <TouchableOpacity
                style={[styles.createButton, isLoading && styles.createButtonDisabled]}
                onPress={() => {
                  Keyboard.dismiss();
                  handleCreateAccount();
                }}
                disabled={isLoading}
              >
                <Text style={styles.createButtonText}>
                  {isLoading ? "Creating Account..." : "Create Account"}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Partnership Text */}
            <Text style={styles.partnershipText}>
              in Partnership with Sanda Diagnostic Center
            </Text>
          </Animated.View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default CreateAccount;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8E8E8',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  backIcon: {
    fontSize: 20,
    color: '#2D5016',
    fontWeight: 'bold',
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 12,
  },
  logoText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2D5016',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2D5016',
    textAlign: 'center',
    marginBottom: 30,
  },
  formContainer: {
    width: '100%',
    maxWidth: 350,
    marginBottom: 30,
  },
  input: {
    backgroundColor: '#C5C5C5',
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 16,
    marginBottom: 16,
    fontSize: 16,
    color: '#333333',
  },
  createButton: {
    backgroundColor: '#2D5016',
    borderRadius: 25,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  createButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  createButtonDisabled: {
    backgroundColor: '#A9A9A9',
    opacity: 0.7,
  },
  partnershipText: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
    marginTop: 20,
  },
});






