// Chat Storage Service for AIRA AI Assistant
// Manages persistent chat history across user sessions

import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage key prefix for chat history
const CHAT_STORAGE_PREFIX = 'aira_chat_history_';

// Default welcome message
const DEFAULT_WELCOME_MESSAGE = {
  id: '1',
  sender: 'bot',
  text: 'How are you feeling today?',
  name: '<PERSON><PERSON>'
};

/**
 * Get storage key for user-specific chat history
 * @param {string} userId - User identifier (ID or email)
 * @returns {string} Storage key
 */
export const getChatStorageKey = (userId) => {
  const userIdentifier = userId || 'default_user';
  return `${CHAT_STORAGE_PREFIX}${userIdentifier}`;
};

/**
 * Load chat history from storage for a specific user
 * @param {string} userId - User identifier
 * @returns {Promise<Array>} Array of messages
 */
export const loadChatHistory = async (userId) => {
  try {
    const storageKey = getChatStorageKey(userId);
    const savedMessages = await AsyncStorage.getItem(storageKey);
    
    if (savedMessages) {
      return JSON.parse(savedMessages);
    } else {
      // Return default welcome message if no history exists
      return [DEFAULT_WELCOME_MESSAGE];
    }
  } catch (error) {
    console.error('Error loading chat history:', error);
    // Return default message on error
    return [DEFAULT_WELCOME_MESSAGE];
  }
};

/**
 * Save chat history to storage for a specific user
 * @param {string} userId - User identifier
 * @param {Array} messages - Array of messages to save
 * @returns {Promise<void>}
 */
export const saveChatHistory = async (userId, messages) => {
  try {
    const storageKey = getChatStorageKey(userId);
    await AsyncStorage.setItem(storageKey, JSON.stringify(messages));
  } catch (error) {
    console.error('Error saving chat history:', error);
  }
};

/**
 * Clear chat history for a specific user
 * @param {string} userId - User identifier
 * @returns {Promise<void>}
 */
export const clearChatHistory = async (userId) => {
  try {
    const storageKey = getChatStorageKey(userId);
    await AsyncStorage.removeItem(storageKey);
  } catch (error) {
    console.error('Error clearing chat history:', error);
  }
};

/**
 * Clear all chat histories (for complete app reset)
 * @returns {Promise<void>}
 */
export const clearAllChatHistories = async () => {
  try {
    const allKeys = await AsyncStorage.getAllKeys();
    const chatKeys = allKeys.filter(key => key.startsWith(CHAT_STORAGE_PREFIX));
    
    if (chatKeys.length > 0) {
      await AsyncStorage.multiRemove(chatKeys);
    }
  } catch (error) {
    console.error('Error clearing all chat histories:', error);
  }
};

/**
 * Get default welcome message
 * @returns {Object} Default message object
 */
export const getDefaultWelcomeMessage = () => {
  return { ...DEFAULT_WELCOME_MESSAGE };
};

export default {
  getChatStorageKey,
  loadChatHistory,
  saveChatHistory,
  clearChatHistory,
  clearAllChatHistories,
  getDefaultWelcomeMessage
};
