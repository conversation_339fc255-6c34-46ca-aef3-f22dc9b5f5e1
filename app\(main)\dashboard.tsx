import React, { useRef, useEffect, useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Animated,
  Platform,
  StatusBar,
  Image,
  Easing,
  Modal,
  BackHandler
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import BottomNavigation from '../components/BottomNavigation';
import { useUser } from '../context/UserContext';
import { useAppointment } from '../context/AppointmentContext';
import { saveMoodEntry, getMoodEntries, deleteMoodEntry, formatDateForDB } from '../lib/mood-service';

// Month View Component
const MonthView = ({ currentDate, onDateChange, savedMoods, onDateClick }) => {
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const getDaysInMonth = (date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const renderCalendarDays = () => {
    const daysInMonth = getDaysInMonth(currentDate);
    const firstDay = getFirstDayOfMonth(currentDate);
    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(<View key={`empty-${i}`} style={styles.calendarDayEmpty} />);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
      const dateString = date.toDateString();
      const hasMood = savedMoods[dateString];
      const isToday = new Date().toDateString() === dateString;
      const isSelected = currentDate.toDateString() === dateString;

      days.push(
        <TouchableOpacity
          key={day}
          style={[
            styles.calendarDay,
            isToday && styles.calendarDayToday,
            isSelected && styles.calendarDaySelected,
            hasMood && !isSelected && styles.calendarDayWithMood
          ]}
          onPress={() => onDateClick(date)}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.calendarDayText,
            isToday && styles.calendarDayTextToday,
            isSelected && styles.calendarDayTextSelected,
            hasMood && !isSelected && styles.calendarDayTextWithMood
          ]}>
            {day}
          </Text>
          {hasMood && (
            <View style={styles.calendarMoodIndicator}>
              <Image
                source={typeof hasMood === 'number' ? [
                  require('../../assets/1.png'),
                  require('../../assets/2.png'),
                  require('../../assets/3.png'),
                  require('../../assets/4.png'),
                  require('../../assets/5.png')
                ][hasMood] : hasMood.emoji}
                style={styles.calendarMoodEmojiImage}
              />
            </View>
          )}
        </TouchableOpacity>
      );
    }

    return days;
  };

  const navigateMonth = (direction) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(currentDate.getMonth() + direction);
    onDateChange(newDate);
  };

  return (
    <View style={styles.monthViewContainer}>
      {/* Month Header */}
      <View style={styles.monthHeader}>
        <TouchableOpacity onPress={() => navigateMonth(-1)}>
          <Text style={styles.monthNavButton}>‹</Text>
        </TouchableOpacity>
        <Text style={styles.monthTitle}>
          {monthNames[currentDate.getMonth()]}
        </Text>
        <TouchableOpacity onPress={() => navigateMonth(1)}>
          <Text style={styles.monthNavButton}>›</Text>
        </TouchableOpacity>
      </View>

      <Text style={styles.todayLabel}>TODAY</Text>

      {/* Days of week header */}
      <View style={styles.calendarWeekHeader}>
        {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => (
          <Text key={index} style={styles.calendarWeekDay}>{day}</Text>
        ))}
      </View>

      {/* Calendar Grid */}
      <View style={styles.calendarGrid}>
        {renderCalendarDays()}
      </View>
    </View>
  );
};



const Dashboard = () => {
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideUpAnim = useRef(new Animated.Value(50)).current;

  // Mood emoji animations - individual animations for each emoji
  const moodAnimations = useRef([
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0)
  ]).current;

  const router = useRouter();
  const { userData, toggleHidePersonalInfo, setMoodSaved } = useUser();
  const { getUpcomingAppointments } = useAppointment();

  // State management
  const [selectedMood, setSelectedMood] = useState(null);
  const [showMoodTracker, setShowMoodTracker] = useState(true);
  const [showDailyInsights, setShowDailyInsights] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [savedMoods, setSavedMoods] = useState(() => {
    // Add test mood for today to verify functionality
    const today = new Date().toDateString();
    return {
      [today]: 4 // Happy mood for testing
    };
  }); // Store moods by date
  const [currentWeekStart, setCurrentWeekStart] = useState(() => {
    const today = new Date();
    const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - currentDay);
    return startOfWeek;
  });
  const [showCalendarModal, setShowCalendarModal] = useState(false);
  const [currentCalendarDate, setCurrentCalendarDate] = useState(new Date());
  const [showDatePopup, setShowDatePopup] = useState(false);
  const [selectedCalendarDate, setSelectedCalendarDate] = useState(null);
  const [tempMoodSelection, setTempMoodSelection] = useState(null);
  // Note: dateScrollRef removed as we now use calendar card design

  // Mood options matching the reference image exactly
  const moodOptions = [
    { emoji: require('../../assets/5.png'), label: 'Sad' },
    { emoji: require('../../assets/2.png'), label: 'Meh' },
    { emoji: require('../../assets/1.png'), label: 'Okay' },
    { emoji: require('../../assets/4.png'), label: 'Good' },
    { emoji: require('../../assets/3.png'), label: 'Great' }
  ];

  // Get the earliest upcoming appointment
  const upcomingAppointments = getUpcomingAppointments();
  const nextAppointment = upcomingAppointments.length > 0 ? upcomingAppointments[0] : null;

  // Load saved moods from database on component mount
  useEffect(() => {
    const loadSavedMoods = async () => {
      if (!userData?.email) return;

      try {
        // Get mood entries for the last 365 days
        const endDate = formatDateForDB(new Date());
        const startDate = formatDateForDB(new Date(Date.now() - 365 * 24 * 60 * 60 * 1000));

        const moodEntries = await getMoodEntries(userData.email, startDate, endDate);

        // Convert database format to component format
        const moodsMap = {};
        moodEntries.forEach(entry => {
          const date = new Date(entry.entry_date);
          const dateKey = date.toDateString();
          moodsMap[dateKey] = entry.mood_value;
        });

        setSavedMoods(moodsMap);
        console.log('✅ Loaded moods from database:', Object.keys(moodsMap).length, 'entries');
      } catch (error) {
        console.error('Error loading saved moods:', error);

        // Fallback to AsyncStorage
        try {
          const savedMoodsData = await AsyncStorage.getItem('savedMoods');
          if (savedMoodsData) {
            setSavedMoods(JSON.parse(savedMoodsData));
            console.log('✅ Loaded moods from AsyncStorage fallback');
          }
        } catch (fallbackError) {
          console.error('AsyncStorage fallback failed:', fallbackError);
        }
      }
    };

    loadSavedMoods();

    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideUpAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Start emoji pop-up animations with staggered timing
    const animateEmojis = () => {
      const animations = moodAnimations.map((anim, index) =>
        Animated.sequence([
          Animated.delay(index * 150), // Stagger each emoji by 150ms
          Animated.spring(anim, {
            toValue: 1,
            tension: 100,
            friction: 8,
            useNativeDriver: true,
          })
        ])
      );

      Animated.parallel(animations).start();
    };

    // Start emoji animations after main content loads
    setTimeout(animateEmojis, 1000);
  }, []);

  // Prevent back navigation to app-features when user is logged in
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      // Prevent back navigation from dashboard - user should use logout instead
      return true; // Return true to prevent default back behavior
    });

    return () => backHandler.remove();
  }, []);

  // Note: Mood data is now saved directly to database in handleSaveMood function
  // AsyncStorage is used only as a fallback in the mood-service

  // Test date formatting on component mount
  useEffect(() => {
    console.log('🧪 Date Formatting Test:');
    const testDate = new Date(2025, 5, 10); // June 10, 2025 (month is 0-indexed)
    console.log('Test Date Object:', testDate);
    console.log('Test Date toString():', testDate.toString());
    console.log('Test Date toDateString():', testDate.toDateString());
    console.log('Test formatDateForDB():', formatDateForDB(testDate));
    console.log('Expected: 2025-06-10');
  }, []);



  const handleMoodSelect = (index) => {
    setSelectedMood(index);

    // Animate selected emoji with a bounce effect
    Animated.sequence([
      Animated.spring(moodAnimations[index], {
        toValue: 1.3,
        tension: 100,
        friction: 3,
        useNativeDriver: true,
      }),
      Animated.spring(moodAnimations[index], {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      })
    ]).start();
  };

  const handleSaveMood = async () => {
    if (selectedMood !== null && userData?.email) {
      try {
        const dateForDB = formatDateForDB(selectedDate);
        const dateKey = selectedDate.toDateString();
        const isUpdating = savedMoods[dateKey] !== undefined;

        // Debug logging to check date conversion
        console.log('🔍 Date Debug Info:');
        console.log('Selected Date Object:', selectedDate);
        console.log('Selected Date toString():', selectedDate.toString());
        console.log('Selected Date toDateString():', selectedDate.toDateString());
        console.log('Date for DB (formatDateForDB):', dateForDB);
        console.log('Date Key (toDateString):', dateKey);

        // Save/update mood to database
        const result = await saveMoodEntry(userData.email, selectedMood, dateForDB);

        if (result.success) {
          // Update local state
          setSavedMoods(prev => ({
            ...prev,
            [dateKey]: selectedMood
          }));

          // Set mood as saved for the current context
          setMoodSaved(true);

          // Show appropriate success message
          if (isUpdating) {
            console.log('✅ Mood updated successfully');
          } else {
            console.log('✅ Mood saved successfully');
          }
        } else {
          console.error('Failed to save mood:', result.error);
        }
      } catch (error) {
        console.error('Error saving mood:', error);
      }
    }
  };

  const handleDeleteMood = async () => {
    if (userData?.email) {
      try {
        const dateForDB = formatDateForDB(selectedDate);
        const dateKey = selectedDate.toDateString();

        // Delete mood from database
        const result = await deleteMoodEntry(userData.email, dateForDB);

        if (result.success) {
          // Update local state - remove the mood
          setSavedMoods(prev => {
            const newMoods = { ...prev };
            delete newMoods[dateKey];
            return newMoods;
          });

          // Clear selected mood
          setSelectedMood(null);

          console.log('✅ Mood deleted successfully');
        } else {
          console.error('Failed to delete mood:', result.error);
        }
      } catch (error) {
        console.error('Error deleting mood:', error);
      }
    }
  };

  const handleProfilePress = () => {
    router.push('/(main)/profile');
  };

  const handleDateSelect = (date) => {
    setSelectedDate(date);

    // Load saved mood for the selected date
    const dateKey = date.toDateString();
    const savedMoodForDate = savedMoods[dateKey];
    setSelectedMood(savedMoodForDate || null);
  };

  const getCurrentDate = () => {
    const today = new Date();
    return today.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const handleCalendarDateClick = (date) => {
    setCurrentCalendarDate(date); // Move the circle to the clicked date
    setSelectedCalendarDate(date);
    const dateKey = date.toDateString();
    const savedMoodForDate = savedMoods[dateKey];
    setTempMoodSelection(savedMoodForDate || null);
    setShowDatePopup(true);
  };

  const closeCalendarModal = () => {
    setShowCalendarModal(false);
    setShowDatePopup(false);
    setSelectedCalendarDate(null);
    setTempMoodSelection(null);
  };





  const handleSaveDateMood = async () => {
    if (tempMoodSelection !== null && selectedCalendarDate && userData?.email) {
      try {
        const dateForDB = formatDateForDB(selectedCalendarDate);
        const result = await saveMoodEntry(userData.email, tempMoodSelection, dateForDB);

        if (result.success) {
          const dateKey = selectedCalendarDate.toDateString();
          setSavedMoods(prev => ({
            ...prev,
            [dateKey]: tempMoodSelection
          }));
          console.log('✅ Calendar mood saved successfully');
        }
      } catch (error) {
        console.error('Error saving calendar mood:', error);
      }
    }
    setShowDatePopup(false);
  };

  const handleDeleteDateMood = async () => {
    if (selectedCalendarDate && userData?.email) {
      try {
        const dateForDB = formatDateForDB(selectedCalendarDate);
        const result = await deleteMoodEntry(userData.email, dateForDB);

        if (result.success) {
          const dateKey = selectedCalendarDate.toDateString();
          setSavedMoods(prev => {
            const newMoods = { ...prev };
            delete newMoods[dateKey];
            return newMoods;
          });
          console.log('✅ Calendar mood deleted successfully');
        }
      } catch (error) {
        console.error('Error deleting calendar mood:', error);
      }
    }
    setShowDatePopup(false);
  };

  // Mini mood chart for dashboard preview
  const renderMiniMoodChart = () => {
    const chartData = [];
    const today = new Date();

    // Get all mood dates to find the earliest entry
    const moodDates = Object.keys(savedMoods).map(dateKey => new Date(dateKey));

    if (moodDates.length === 0) {
      // No mood data yet
      return (
        <View style={[styles.miniChartContainer, { justifyContent: 'center', alignItems: 'center' }]}>
          <Text style={styles.noMoodDataText}>Start tracking your mood to see trends</Text>
        </View>
      );
    }

    // Find the earliest mood entry date
    const firstMoodDate = new Date(Math.min(...moodDates.map(d => d.getTime())));

    // Show from first mood entry to today (max 7 days for mini chart)
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const startDate = firstMoodDate > weekAgo ? firstMoodDate : weekAgo;

    // Build data from start date to today
    const currentDate = new Date(startDate);
    while (currentDate <= today) {
      const dateKey = currentDate.toDateString();
      const moodValue = savedMoods[dateKey];

      chartData.push({
        date: currentDate.getDate(),
        mood: moodValue !== undefined ? moodValue : null,
        hasData: moodValue !== undefined
      });

      currentDate.setDate(currentDate.getDate() + 1);
    }

    const chartWidth = 280;
    const chartHeight = 80;
    const padding = 20;
    const plotWidth = chartWidth - (padding * 2);
    const plotHeight = chartHeight - (padding * 2);

    // Create SVG path for mood line
    let pathData = '';
    const validPoints = chartData.filter(point => point.hasData);

    if (validPoints.length > 1) {
      validPoints.forEach((point, index) => {
        const dataIndex = chartData.findIndex(d => d.date === point.date);
        const x = padding + (dataIndex / (chartData.length - 1)) * plotWidth;
        const y = padding + plotHeight - ((point.mood / 4) * plotHeight);

        if (index === 0) {
          pathData += `M ${x} ${y}`;
        } else {
          pathData += ` L ${x} ${y}`;
        }
      });
    }

    return (
      <View style={styles.miniChart}>
        {/* Grid lines */}
        <View style={styles.chartGrid}>
          {[0, 1, 2, 3, 4].map(i => (
            <View
              key={`h-${i}`}
              style={[
                styles.gridLineHorizontal,
                { top: padding + (i / 4) * plotHeight }
              ]}
            />
          ))}
          {chartData.map((_, i) => (
            <View
              key={`v-${i}`}
              style={[
                styles.gridLineVertical,
                { left: padding + (i / (chartData.length - 1)) * plotWidth }
              ]}
            />
          ))}
        </View>

        {/* Mood points and line */}
        <View style={styles.chartContent}>
          {/* Line path (simplified with View components) */}
          {validPoints.length > 1 && validPoints.map((point, index) => {
            if (index === validPoints.length - 1) return null;

            const currentIndex = chartData.findIndex(d => d.date === point.date);
            const nextPoint = validPoints[index + 1];
            const nextIndex = chartData.findIndex(d => d.date === nextPoint.date);

            const x1 = padding + (currentIndex / (chartData.length - 1)) * plotWidth;
            const y1 = padding + plotHeight - ((point.mood / 4) * plotHeight);
            const x2 = padding + (nextIndex / (chartData.length - 1)) * plotWidth;
            const y2 = padding + plotHeight - ((nextPoint.mood / 4) * plotHeight);

            const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
            const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;

            return (
              <View
                key={`line-${index}`}
                style={[
                  styles.chartLine,
                  {
                    left: x1,
                    top: y1,
                    width: length,
                    transform: [{ rotate: `${angle}deg` }]
                  }
                ]}
              />
            );
          })}

          {/* Data points */}
          {chartData.map((point, index) => {
            if (!point.hasData) return null;

            const x = padding + (index / (chartData.length - 1)) * plotWidth;
            const y = padding + plotHeight - ((point.mood / 4) * plotHeight);

            return (
              <View
                key={`point-${index}`}
                style={[
                  styles.chartPoint,
                  {
                    left: x - 4,
                    top: y - 4
                  }
                ]}
              />
            );
          })}
        </View>

        {/* Date labels */}
        <View style={styles.chartLabels}>
          {chartData.map((point, index) => (
            <Text
              key={`label-${index}`}
              style={[
                styles.chartLabel,
                { left: padding + (index / (chartData.length - 1)) * plotWidth - 10 }
              ]}
            >
              {point.date}
            </Text>
          ))}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header with integrated status bar */}
      <LinearGradient
        colors={['#66948a', '#66948a']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.headerGradient}
      >
        <StatusBar backgroundColor="#66948a" barStyle="light-content" translucent />
        <SafeAreaView style={styles.headerSafeArea}>
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.profilePicture}
              onPress={handleProfilePress}
            >
              <Text style={styles.profileInitial}>
                {(userData.firstName || 'L').charAt(0)}
              </Text>
            </TouchableOpacity>

            <View style={styles.headerDateContainer}>
              <Text style={styles.headerDate}>
                {selectedDate.toLocaleDateString('en-US', {
                  month: 'long',
                  day: 'numeric'
                })}
              </Text>
            </View>

            <TouchableOpacity
              style={styles.calendarButton}
              onPress={() => setShowCalendarModal(true)}
            >
              <Text style={styles.calendarButtonIcon}>📅</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </LinearGradient>

      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Calendar Card - Matching Reference Design */}
          <View style={styles.calendarCard}>
            {/* Week View */}
            <View style={styles.weekContainer}>
              {/* Days of week header */}
              <View style={styles.weekHeader}>
                {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => (
                  <Text key={index} style={styles.weekDayLabel}>{day}</Text>
                ))}
              </View>

              {/* Week dates */}
              <View style={styles.weekDates}>
                {Array.from({ length: 7 }, (_, index) => {
                  // Get the start of current week (Sunday)
                  const today = new Date();
                  const currentDay = today.getDay(); // 0 = Sunday
                  const startOfWeek = new Date(today);
                  startOfWeek.setDate(today.getDate() - currentDay);

                  // Calculate the date for this day of the week
                  const date = new Date(startOfWeek);
                  date.setDate(startOfWeek.getDate() + index);

                  const isToday = date.toDateString() === today.toDateString();
                  const isSelected = date.toDateString() === selectedDate.toDateString();
                  const hasMood = savedMoods[date.toDateString()] !== undefined;

                  return (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.weekDateItem,
                        isSelected && styles.weekDateSelected
                      ]}
                      onPress={() => handleDateSelect(date)}
                    >
                      <Text style={[
                        styles.weekDateNumber,
                        isSelected && styles.weekDateNumberSelected
                      ]}>
                        {date.getDate()}
                      </Text>
                      {hasMood && (
                        <View style={styles.weekMoodIndicator}>
                          <Image
                            source={moodOptions[savedMoods[date.toDateString()]]?.emoji}
                            style={styles.weekMoodEmojiImage}
                          />
                        </View>
                      )}
                    </TouchableOpacity>
                  );
                })}
              </View>
            </View>
          </View>

          {/* Mood Tracker Card - Exactly Matching Reference Image */}
          <Animated.View
            style={[
              (savedMoods[selectedDate.toDateString()] !== undefined) ? styles.moodCardSaved : styles.moodCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
              <View style={styles.moodCardContent}>
                <Text style={styles.moodTitle}>
                  {savedMoods[selectedDate.toDateString()] !== undefined
                    ? (selectedDate.toDateString() === new Date().toDateString()
                        ? "Update today's mood"
                        : "Update your mood")
                    : (selectedDate.toDateString() === new Date().toDateString()
                        ? "How are you feeling today?"
                        : "How were you feeling?")}
                </Text>
                <View style={styles.moodDateContainer}>
                  <Text style={styles.moodSubtitle}>
                    {savedMoods[selectedDate.toDateString()] !== undefined
                      ? (selectedDate.toDateString() === new Date().toDateString()
                          ? "Currently: " + moodOptions[savedMoods[selectedDate.toDateString()]]?.emoji + " " + moodOptions[savedMoods[selectedDate.toDateString()]]?.label
                          : "Saved: " + moodOptions[savedMoods[selectedDate.toDateString()]]?.emoji + " " + moodOptions[savedMoods[selectedDate.toDateString()]]?.label)
                      : (selectedDate.toDateString() === new Date().toDateString()
                          ? "Today's mood"
                          : "Mood for")}
                  </Text>
                  <Text style={styles.moodDate}>
                    {selectedDate.toLocaleDateString('en-US', {
                      month: 'long',
                      day: 'numeric',
                      year: 'numeric'
                    })}
                  </Text>
                </View>

                <View style={styles.moodOptions}>
                  {moodOptions.map((mood, index) => (
                    <TouchableOpacity
                      key={index}
                      style={styles.moodOption}
                      onPress={() => handleMoodSelect(index)}
                    >
                      <Animated.View
                        style={[
                          styles.moodEmojiContainer,
                          {
                            transform: [
                              { scale: moodAnimations[index] },
                              {
                                translateY: moodAnimations[index].interpolate({
                                  inputRange: [0, 1],
                                  outputRange: [20, 0]
                                })
                              }
                            ],
                            opacity: moodAnimations[index]
                          }
                        ]}
                      >
                        <Image
                          source={mood.emoji}
                          style={[
                            styles.moodEmojiImage,
                            selectedMood === index && styles.selectedMoodEmojiImage
                          ]}
                        />
                      </Animated.View>
                      <Text style={[
                        styles.moodLabel,
                        selectedMood === index && styles.selectedMoodLabel
                      ]}>
                        {mood.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>

              </View>
            </Animated.View>

          {/* Save Mood Button */}
          <TouchableOpacity
            style={[
              styles.saveMoodButton,
              selectedMood === null && styles.saveMoodButtonDisabled
            ]}
            onPress={handleSaveMood}
            disabled={selectedMood === null}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={selectedMood === null ? ['#BDC3C7', '#95A5A6'] : ['#66948a', '#4A7C59']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.saveMoodButtonGradient}
            >
              <Text style={[
                styles.saveMoodButtonText,
                selectedMood === null && styles.saveMoodButtonTextDisabled
              ]}>
                ✨ Save Mood ✨
              </Text>
            </LinearGradient>
          </TouchableOpacity>

          {/* Delete Button - Only show when editing existing mood */}
          {savedMoods[selectedDate.toDateString()] !== undefined && (
            <TouchableOpacity
              style={styles.deleteMoodButton}
              onPress={handleDeleteMood}
              activeOpacity={0.7}
            >
              <Text style={styles.deleteMoodButtonText}>🗑️ Remove Mood</Text>
            </TouchableOpacity>
          )}




          {/* Upcoming Appointment Section - Matching Reference Design */}
          {nextAppointment ? (
            <View style={styles.appointmentSection}>
              <Text style={styles.appointmentSectionTitle}>Upcoming Appointment</Text>

              <Animated.View
                style={[
                  styles.appointmentCard,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideUpAnim }]
                  }
                ]}
              >
                <View style={styles.appointmentContent}>
                  <View style={styles.doctorInfo}>
                    <View style={styles.doctorAvatar}>
                      <Text style={styles.doctorInitial}>
                        {nextAppointment.psychometrician?.name?.charAt(0)?.toUpperCase() || 'P'}
                      </Text>
                    </View>
                    <View style={styles.doctorDetails}>
                      <Text style={styles.doctorName}>
                        {nextAppointment.psychometrician?.name || 'Psychometrician'}
                      </Text>
                      <Text style={styles.doctorSpecialty}>
                        {nextAppointment.psychometrician?.specialty || 'Psychometrician'}
                      </Text>
                    </View>
                  </View>
                  <View style={styles.appointmentTime}>
                    <Text style={styles.appointmentDate}>
                      {new Date(nextAppointment.date).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                      })}
                    </Text>
                    <Text style={styles.appointmentTimeText}>
                      {(() => {
                        const [hours, minutes] = nextAppointment.start_time.split(':');
                        const hour = parseInt(hours);
                        const ampm = hour >= 12 ? 'pm' : 'am';
                        const displayHour = hour > 12 ? hour - 12 : hour === 0 ? 12 : hour;
                        return `${displayHour}:${minutes} ${ampm}`;
                      })()}
                    </Text>
                  </View>
                </View>
              </Animated.View>
            </View>
          ) : (
            <View style={styles.appointmentSection}>
              <Text style={styles.appointmentSectionTitle}>Upcoming Appointment</Text>
              <Animated.View
                style={[
                  styles.appointmentCard,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideUpAnim }]
                  }
                ]}
              >
                <View style={styles.noAppointmentContent}>
                  <Text style={styles.noAppointmentText}>No upcoming appointments</Text>
                  <TouchableOpacity
                    style={styles.bookAppointmentButton}
                    onPress={() => router.push('/(main)/appointments')}
                  >
                    <Text style={styles.bookAppointmentButtonText}>Book Appointment</Text>
                  </TouchableOpacity>
                </View>
              </Animated.View>
            </View>
          )}

          {/* AI Mental Health Assistant Card - Exactly Matching Reference */}
          <Animated.View
            style={[
              styles.aiAssistantCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <View style={styles.aiAssistantContent}>
              <View style={styles.aiAssistantHeader}>
                <View style={styles.aiIcon}>
                  <Text style={styles.aiIconText}>💬</Text>
                </View>
                <Text style={styles.aiAssistantTitle}>AI Mental Health Assistant</Text>
              </View>
              <Text style={styles.aiAssistantSubtitle}>
                Feeling overwhelmed or need someone to talk to?
              </Text>
              <Text style={styles.aiAssistantDescription}>
                Our AI assistant is here to listen, provide support, and offer guidance 24/7
              </Text>
              <TouchableOpacity
                style={styles.chatButton}
                onPress={() => router.push('/(main)/consultation/ai-chatbot')}
              >
                <Text style={styles.chatButtonText}>Chat with Aira</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>

          {/* Mood Analytics Card */}
          <Animated.View
            style={[
              styles.moodAnalyticsCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <View style={styles.moodAnalyticsHeader}>
              <Text style={styles.moodAnalyticsTitle}>Analytics (Mood tracker)</Text>
              <TouchableOpacity
                style={styles.viewMoreButton}
                onPress={() => router.push('/(main)/mental-health/mood-analytics')}
              >
                <Text style={styles.viewMoreText}>→</Text>
              </TouchableOpacity>
            </View>

            {/* Mini Chart Preview */}
            <View style={styles.miniChartContainer}>
              {renderMiniMoodChart()}
            </View>

            <Text style={styles.moodAnalyticsSubtext}>
              Track your mood patterns over time to better understand your mental health journey
            </Text>
          </Animated.View>

          {/* Mental Health Assessment Card - Exactly Matching Reference */}
          <Animated.View
            style={[
              styles.assessmentCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <LinearGradient
              colors={['#66948a', '#66948a']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.assessmentGradient}
            >
              <Text style={styles.assessmentTitle}>Mental Health Assessment</Text>
              <Text style={styles.assessmentDescription}>
                Take a quuick assessment to better understand your current mental health state and get personalized recommendation
              </Text>
              <TouchableOpacity
                style={styles.assessmentButton}
                onPress={() => router.push('/(main)/mental-health')}
              >
                <Text style={styles.assessmentButtonText}>Start self-assessment</Text>
              </TouchableOpacity>
            </LinearGradient>
          </Animated.View>

          {/* Stress Management Card - Exactly Matching Reference */}
          <Animated.View
            style={[
              styles.stressManagementCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <View style={styles.stressManagementContent}>
              <View style={styles.stressManagementHeader}>
                <View style={styles.stressIcon}>
                  <Text style={styles.stressIconText}>🌱</Text>
                </View>
                <Text style={styles.stressManagementTitle}>Stress Management</Text>
              </View>
              <Text style={styles.stressManagementDescription}>
                Track your thoughts, feelings, and activities to identify patterns and improve your mental well-being.
              </Text>
              <TouchableOpacity
                style={styles.stressManagementButton}
                onPress={() => router.push({
                  pathname: '/(main)/mental-health/mood-journal',
                  params: { fromDashboard: 'true' }
                })}
              >
                <Text style={styles.stressManagementButtonText}>Start journaling</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>


        </ScrollView>

        {/* Bottom Navigation */}
        <BottomNavigation />
      </SafeAreaView>

      {/* Daily Insights Modal */}
      <Modal
        visible={showDailyInsights}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDailyInsights(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.dailyInsightsCard}>
            <View style={styles.insightsHeader}>
              <Text style={styles.insightsTitle}>Daily Insights</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowDailyInsights(false)}
              >
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
            </View>

            <Text style={styles.insightsSubtitle}>
              It seems like you are stress in the past few weeks.{'\n'}
              Here are some recommendations:
            </Text>

            <View style={styles.recommendationsList}>
              <Text style={styles.recommendationItem}>• Take a deept breath</Text>
              <Text style={styles.recommendationItem}>• write down what is bothering you</Text>
              <Text style={styles.recommendationItem}>• Do something kind to yourself</Text>
            </View>
          </View>
        </View>
      </Modal>

      {/* Calendar Modal */}
      <Modal
        visible={showCalendarModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={closeCalendarModal}
      >
        <SafeAreaView style={styles.calendarModalContainer}>
          <StatusBar barStyle="light-content" backgroundColor="#66948a" />

          {/* Calendar Header */}
          <View style={styles.calendarHeader}>
            <TouchableOpacity
              style={styles.calendarCloseButton}
              onPress={closeCalendarModal}
            >
              <Text style={styles.calendarCloseIcon}>✕</Text>
            </TouchableOpacity>

            <Text style={styles.calendarHeaderTitle}>Mood Tracker</Text>

            <TouchableOpacity
              style={styles.todayButton}
              onPress={() => setCurrentCalendarDate(new Date())}
            >
              <Text style={styles.todayButtonText}>Today</Text>
            </TouchableOpacity>
          </View>

          {/* Calendar Content */}
          <ScrollView
            style={styles.calendarContent}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            <MonthView
              currentDate={currentCalendarDate}
              onDateChange={setCurrentCalendarDate}
              savedMoods={savedMoods}
              onDateClick={handleCalendarDateClick}
            />
          </ScrollView>

          {/* Date Popup Overlay - Inside Calendar Modal */}
          {showDatePopup && (
            <View style={styles.datePopupOverlay}>
              <View style={styles.datePopupContainer}>
                {/* Close Button */}
                <TouchableOpacity
                  style={styles.datePopupCloseButton}
                  onPress={() => setShowDatePopup(false)}
                >
                  <Text style={styles.datePopupCloseIcon}>✕</Text>
                </TouchableOpacity>

                {/* Date Header */}
                <Text style={styles.datePopupTitle}>
                  {selectedCalendarDate?.toLocaleDateString('en-US', {
                    month: 'long',
                    day: 'numeric'
                  })}
                </Text>

                {/* Mood Section */}
                <View style={styles.datePopupMoodSection}>
                  <Text style={styles.datePopupMoodTitle}>How were you feeling?</Text>
                  <Text style={styles.datePopupMoodSubtitle}>Mood</Text>

                  <View style={styles.datePopupMoodOptions}>
                    {moodOptions.map((mood, index) => (
                      <TouchableOpacity
                        key={index}
                        style={styles.datePopupMoodOption}
                        onPress={() => setTempMoodSelection(index)}
                      >
                        <Image
                          source={mood.emoji}
                          style={[
                            styles.datePopupMoodEmojiImage,
                            tempMoodSelection === index && styles.datePopupMoodEmojiImageSelected
                          ]}
                        />
                        <Text style={[
                          styles.datePopupMoodLabel,
                          tempMoodSelection === index && styles.datePopupMoodLabelSelected
                        ]}>
                          {mood.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>

                {/* Action Buttons */}
                <View style={styles.datePopupActions}>
                  <TouchableOpacity
                    style={styles.datePopupRemoveButton}
                    onPress={handleDeleteDateMood}
                  >
                    <Text style={styles.datePopupRemoveButtonText}>Remove Mood</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.datePopupUpdateButton,
                      tempMoodSelection === null && styles.datePopupUpdateButtonDisabled
                    ]}
                    onPress={handleSaveDateMood}
                    disabled={tempMoodSelection === null}
                  >
                    <Text style={[
                      styles.datePopupUpdateButtonText,
                      tempMoodSelection === null && styles.datePopupUpdateButtonTextDisabled
                    ]}>
                      Update Mood
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          )}
        </SafeAreaView>
      </Modal>




    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  safeArea: {
    flex: 1,
  },
  headerGradient: {
    paddingTop: 0,
    paddingBottom: 8,
    paddingHorizontal: 20,
  },
  headerSafeArea: {
    paddingHorizontal: 0,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
    paddingHorizontal: 4,
    height: 44,
  },
  headerDateContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 16,
  },
  headerDate: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  profilePicture: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F39C12',
    justifyContent: 'center',
    alignItems: 'center',
    // Fitts's Law: Better touch target and proper alignment
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  profileInitial: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  userName: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  calendarButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    justifyContent: 'center',
    alignItems: 'center',
    // Fitts's Law: Better touch target and proper alignment
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  calendarButtonIcon: {
    fontSize: 18,
  },
  scrollContent: {
    padding: 20,
    paddingTop: 20,
    paddingBottom: 100,
  },

  // Calendar Card Styles - Matching Reference Design
  calendarCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    overflow: 'hidden',
  },

  calendarCardHeader: {
    backgroundColor: '#66948a',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },

  profileIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#FFA500',
    justifyContent: 'center',
    alignItems: 'center',
  },

  profileIconText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },

  calendarCardDate: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },

  calendarCardIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  calendarCardIconText: {
    fontSize: 16,
  },

  weekContainer: {
    padding: 20,
  },

  weekHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },

  weekDayLabel: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
    textAlign: 'center',
    width: 40,
  },

  weekDates: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  weekDateItem: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },

  weekDateSelected: {
    backgroundColor: '#66948a',
  },

  weekDateNumber: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '500',
  },

  weekDateNumberSelected: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },

  weekMoodIndicator: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#66948a',
  },

  weekMoodEmoji: {
    fontSize: 10,
  },
  weekMoodEmojiImage: {
    width: 10,
    height: 10,
    resizeMode: 'contain',
  },
  todayButton: {
    backgroundColor: '#66948a',
    borderRadius: 15,
    paddingHorizontal: 12,
    paddingVertical: 6,
    shadowColor: '#66948a',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  todayButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  dateScrollView: {
    height: 110,
    marginVertical: 5,
  },
  dateScrollContent: {
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  dateItem: {
    alignItems: 'center',
    marginHorizontal: 8,
    paddingVertical: 8,
    minWidth: 40,
  },
  todayDateItem: {
    // Additional styling for today's date item container
  },
  selectedDateItem: {
    // Additional styling for selected date item container
  },
  moodSavedDateItem: {
    // Additional styling for date item with saved mood
  },
  dayLabel: {
    fontSize: 11,
    color: '#7F8C8D',
    fontWeight: '500',
    marginBottom: 3,
    textAlign: 'center',
  },
  todayDayLabel: {
    color: '#2C3E50',
    fontWeight: '600',
  },
  selectedDayLabel: {
    color: '#66948a',
    fontWeight: '600',
  },
  todayText: {
    fontSize: 12,
    color: '#2C3E50',
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 4,
  },
  dateCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
    position: 'relative',
  },
  todayCircle: {
    backgroundColor: '#2C3E50',
    shadowColor: '#2C3E50',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedDateCircle: {
    backgroundColor: '#66948a',
    shadowColor: '#66948a',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  moodSavedCircle: {
    backgroundColor: 'transparent',
  },
  dateNumber: {
    fontSize: 16,
    color: '#2C3E50',
    fontWeight: '500',
  },
  todayDateNumber: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  selectedDateNumber: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  moodSavedDateNumber: {
    color: '#6B9B7A',
    fontWeight: 'bold',
  },
  moodIndicator: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#6B9B7A',
  },
  moodIndicatorEmoji: {
    fontSize: 10,
  },
  moodCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    marginBottom: 15,
    marginTop: 0,
    marginHorizontal: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 15,
  },
  moodCardSaved: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    marginBottom: 15,
    marginTop: 0,
    marginHorizontal: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 5,
  },
  moodCardContent: {
    padding: 25,
    paddingBottom: 30,
  },
  moodTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 12,
  },
  moodDateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 30,
  },
  moodSubtitle: {
    fontSize: 16,
    color: '#7F8C8D',
    fontWeight: '500',
  },
  moodDate: {
    fontSize: 16,
    color: '#2C3E50',
    fontWeight: '600',
  },
  moodOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 25,
    paddingHorizontal: 10,
  },
  moodOption: {
    alignItems: 'center',
    flex: 1,
  },
  moodEmojiContainer: {
    marginBottom: 12,
  },
  moodEmoji: {
    fontSize: 40,
  },
  moodEmojiImage: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  },
  selectedMoodEmoji: {
    fontSize: 45,
  },
  selectedMoodEmojiImage: {
    width: 45,
    height: 45,
    resizeMode: 'contain',
  },
  moodLabel: {
    fontSize: 14,
    color: '#7F8C8D',
    fontWeight: '500',
  },
  selectedMoodLabel: {
    color: '#6B9B7A',
    fontWeight: 'bold',
  },
  saveMoodButton: {
    borderRadius: 30,
    marginHorizontal: 20,
    marginBottom: 25,
    marginTop: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  saveMoodButtonGradient: {
    borderRadius: 30,
    paddingVertical: 18,
    paddingHorizontal: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveMoodButtonDisabled: {
    shadowOpacity: 0.1,
    elevation: 2,
  },
  saveMoodButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: 0.5,
    textAlign: 'center',
  },
  saveMoodButtonTextDisabled: {
    color: '#7F8C8D',
    opacity: 0.7,
  },

  deleteMoodButton: {
    backgroundColor: '#F8F9FA',
    borderRadius: 20,
    paddingVertical: 12,
    paddingHorizontal: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#E74C3C',
    shadowColor: '#E74C3C',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  deleteMoodButtonText: {
    fontSize: 14,
    color: '#E74C3C',
    fontWeight: '600',
  },

  appointmentSection: {
    marginBottom: 25,
  },
  appointmentSectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 15,
    marginLeft: 5,
  },
  appointmentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    marginBottom: 20,
    padding: 20,
    marginHorizontal: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  appointmentContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 18,
  },
  doctorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  doctorAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#E67E22',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  doctorInitial: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  doctorDetails: {
    flex: 1,
  },
  doctorName: {
    fontSize: 17,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 3,
  },
  doctorSpecialty: {
    fontSize: 14,
    color: '#7F8C8D',
  },
  appointmentTime: {
    alignItems: 'flex-end',
  },
  appointmentDate: {
    fontSize: 14,
    color: '#2C3E50',
    fontWeight: '600',
    marginBottom: 2,
  },
  appointmentTimeText: {
    fontSize: 14,
    color: '#7F8C8D',
  },
  rescheduleButton: {
    backgroundColor: '#95A5A6',
    borderRadius: 25,
    paddingVertical: 14,
    alignItems: 'center',
    marginHorizontal: 0,
  },
  rescheduleButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  noAppointmentContent: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  noAppointmentText: {
    fontSize: 16,
    color: '#7F8C8D',
    marginBottom: 15,
    textAlign: 'center',
  },
  bookAppointmentButton: {
    backgroundColor: '#66948a',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 25,
  },
  bookAppointmentButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  aiAssistantCard: {
    backgroundColor: '#e0ede7',
    borderRadius: 25,
    marginBottom: 25,
    marginHorizontal: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  aiAssistantContent: {
    padding: 25,
  },
  aiAssistantHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  aiIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  aiIconText: {
    fontSize: 20,
  },
  aiAssistantTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  aiAssistantSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: 10,
  },
  aiAssistantDescription: {
    fontSize: 14,
    color: '#7F8C8D',
    lineHeight: 22,
    marginBottom: 20,
  },
  chatButton: {
    backgroundColor: '#66948a',
    borderRadius: 25,
    paddingVertical: 14,
    alignItems: 'center',
    marginHorizontal: 0,
  },
  chatButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  assessmentCard: {
    borderRadius: 30,
    marginBottom: 25,
    marginHorizontal: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 8,
  },
  assessmentGradient: {
    padding: 25,
    borderRadius: 30,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    minHeight: 160,
  },
  assessmentTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 10,
  },
  assessmentDescription: {
    fontSize: 14,
    color: '#FFFFFF',
    lineHeight: 20,
    marginBottom: 20,
    opacity: 0.9,
  },
  assessmentButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 25,
    alignSelf: 'flex-start',
  },
  assessmentButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B9B7A',
  },

  // Stress Management Card Styles
  stressManagementCard: {
    backgroundColor: '#e0ede7',
    borderRadius: 30,
    marginBottom: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 15,
    elevation: 8,
  },
  stressManagementContent: {
    padding: 25,
  },
  stressManagementHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  stressIcon: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'rgba(107, 155, 122, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  stressIconText: {
    fontSize: 16,
  },
  stressManagementTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  stressManagementDescription: {
    fontSize: 14,
    color: '#2C3E50',
    lineHeight: 20,
    marginBottom: 20,
    opacity: 0.8,
  },
  stressManagementButton: {
    backgroundColor: '#66948a',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 25,
    alignSelf: 'flex-start',
  },
  stressManagementButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },

  // Daily Insights Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  dailyInsightsCard: {
    backgroundColor: '#E8F5E8',
    borderRadius: 20,
    padding: 25,
    width: '90%',
    maxWidth: 350,
  },
  insightsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  insightsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#666',
    fontWeight: 'bold',
  },
  insightsSubtitle: {
    fontSize: 14,
    color: '#2C3E50',
    lineHeight: 20,
    marginBottom: 15,
  },
  recommendationsList: {
    marginTop: 5,
  },
  recommendationItem: {
    fontSize: 14,
    color: '#2C3E50',
    marginBottom: 8,
    lineHeight: 20,
  },

  // Calendar Modal Styles
  calendarModalContainer: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  calendarHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#F8F9FA',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  calendarCloseButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#E0E0E0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  calendarCloseIcon: {
    fontSize: 16,
    color: '#666',
    fontWeight: 'bold',
  },
  calendarHeaderTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
    flex: 1,
    textAlign: 'center',
  },

  calendarContent: {
    flex: 1,
    padding: 20,
  },

  // Month View Styles
  monthViewContainer: {
    flex: 1,
  },
  monthHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
    paddingHorizontal: 20,
  },
  monthNavButton: {
    fontSize: 24,
    color: '#2C3E50',
    fontWeight: 'bold',
    padding: 10,
  },
  monthTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  todayLabel: {
    textAlign: 'center',
    fontSize: 12,
    color: '#666',
    marginBottom: 20,
    fontWeight: '500',
  },
  calendarWeekHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
    paddingHorizontal: 20,
  },
  calendarWeekDay: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
    textAlign: 'center',
    width: 40,
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  calendarDay: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    position: 'relative',
  },
  calendarDayEmpty: {
    width: 40,
    height: 40,
    marginBottom: 10,
  },
  calendarDayToday: {
    backgroundColor: '#2C3E50',
  },
  calendarDaySelected: {
    backgroundColor: '#D3D3D3',
  },
  calendarDayWithMood: {
    backgroundColor: 'transparent',
  },
  calendarDayText: {
    fontSize: 16,
    color: '#2C3E50',
    fontWeight: '500',
  },
  calendarDayTextToday: {
    color: '#FFFFFF',
    fontWeight: '900', // Extra bold for today
    fontSize: 18,
  },
  calendarDayTextSelected: {
    color: '#2C3E50',
    fontWeight: 'bold',
  },
  calendarDayTextWithMood: {
    color: '#2C3E50',
    fontWeight: 'bold',
  },
  calendarMoodIndicator: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#FFB6C1',
  },
  calendarMoodEmoji: {
    fontSize: 10,
  },
  calendarMoodEmojiImage: {
    width: 10,
    height: 10,
    resizeMode: 'contain',
  },



  // Date Popup Modal Styles
  datePopupOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    justifyContent: 'flex-end',
    zIndex: 9999,
    pointerEvents: 'box-none',
  },
  datePopupContainer: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    padding: 25,
    paddingBottom: 40,
    minHeight: 400,
    position: 'relative',
    zIndex: 10000,
    pointerEvents: 'auto',
  },
  datePopupCloseButton: {
    position: 'absolute',
    top: 15,
    right: 15,
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  datePopupCloseIcon: {
    fontSize: 16,
    color: '#666',
    fontWeight: 'bold',
  },
  datePopupTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C3E50',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 30,
  },
  datePopupMoodSection: {
    marginBottom: 30,
  },
  datePopupMoodTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C3E50',
    textAlign: 'center',
    marginBottom: 8,
  },
  datePopupMoodSubtitle: {
    fontSize: 14,
    color: '#7F8C8D',
    textAlign: 'center',
    marginBottom: 20,
  },
  datePopupMoodOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
  },
  datePopupMoodOption: {
    alignItems: 'center',
    flex: 1,
    paddingVertical: 15,
    borderRadius: 15,
    marginHorizontal: 3,
  },
  datePopupMoodOptionSelected: {
    backgroundColor: '#E8F5E8',
  },
  datePopupMoodEmoji: {
    fontSize: 32,
    marginBottom: 8,
  },
  datePopupMoodEmojiImage: {
    width: 32,
    height: 32,
    marginBottom: 8,
    resizeMode: 'contain',
  },
  datePopupMoodEmojiSelected: {
    fontSize: 36,
  },
  datePopupMoodEmojiImageSelected: {
    width: 36,
    height: 36,
    marginBottom: 8,
    resizeMode: 'contain',
  },
  datePopupMoodLabel: {
    fontSize: 12,
    color: '#7F8C8D',
    fontWeight: '500',
  },
  datePopupMoodLabelSelected: {
    color: '#6B9B7A',
    fontWeight: 'bold',
  },
  datePopupActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  datePopupDeleteButton: {
    backgroundColor: '#E74C3C',
    borderRadius: 20,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 0.4,
  },
  datePopupDeleteButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  datePopupSaveButton: {
    backgroundColor: '#6B9B7A',
    borderRadius: 20,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 0.55,
  },
  datePopupSaveButtonDisabled: {
    backgroundColor: '#BDC3C7',
  },
  datePopupSaveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  datePopupSaveButtonTextDisabled: {
    color: '#7F8C8D',
  },
  datePopupRemoveButton: {
    backgroundColor: '#E74C3C',
    borderRadius: 25,
    paddingVertical: 14,
    paddingHorizontal: 20,
    flex: 0.45,
    marginRight: 10,
  },
  datePopupRemoveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  datePopupUpdateButton: {
    backgroundColor: '#2C5530',
    borderRadius: 25,
    paddingVertical: 14,
    paddingHorizontal: 20,
    flex: 0.45,
  },
  datePopupUpdateButtonDisabled: {
    backgroundColor: '#BDC3C7',
  },
  datePopupUpdateButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  datePopupUpdateButtonTextDisabled: {
    color: '#7F8C8D',
  },

  // Mood Analytics Card Styles
  moodAnalyticsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 20,
    marginHorizontal: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  moodAnalyticsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  moodAnalyticsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C3E50',
  },
  viewMoreButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(102, 148, 138, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewMoreText: {
    fontSize: 20,
    color: '#66948a',
    fontWeight: 'bold',
  },
  miniChartContainer: {
    height: 120,
    marginBottom: 15,
  },
  moodAnalyticsSubtext: {
    fontSize: 12,
    color: '#7F8C8D',
    textAlign: 'center',
    lineHeight: 16,
  },
  noMoodDataText: {
    fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'center',
    fontStyle: 'italic',
  },

  // Mini Chart Styles
  miniChart: {
    width: '100%',
    height: 100,
    position: 'relative',
    backgroundColor: '#FAFBFC',
    borderRadius: 10,
    overflow: 'hidden',
  },
  chartGrid: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  gridLineHorizontal: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: '#E5E7EB',
  },
  gridLineVertical: {
    position: 'absolute',
    top: 0,
    bottom: 20,
    width: 1,
    backgroundColor: '#E5E7EB',
  },
  chartContent: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  chartLine: {
    position: 'absolute',
    height: 2,
    backgroundColor: '#66948a',
    transformOrigin: 'left center',
  },
  chartPoint: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#66948a',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  chartLabels: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 20,
  },
  chartLabel: {
    position: 'absolute',
    fontSize: 10,
    color: '#9CA3AF',
    textAlign: 'center',
    width: 20,
    bottom: 2,
  },
});

export default Dashboard;
