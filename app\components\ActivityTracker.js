import React from 'react';
import { View, TouchableWithoutFeedback } from 'react-native';
import { useActivityTracker } from '../hooks/useActivityTracker';

/**
 * Activity Tracker Component
 * Wraps content and tracks user interactions for session timeout
 */
const ActivityTracker = ({ children, style }) => {
  const { trackActivity } = useActivityTracker();

  const handleInteraction = () => {
    trackActivity();
  };

  return (
    <TouchableWithoutFeedback onPress={handleInteraction}>
      <View style={[{ flex: 1 }, style]} onTouchStart={handleInteraction}>
        {children}
      </View>
    </TouchableWithoutFeedback>
  );
};

export default ActivityTracker;
