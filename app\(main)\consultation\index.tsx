import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';

export default function ConsultationIndex() {
  const router = useRouter();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Consultation Services</Text>
      <Text style={styles.subtitle}>Choose your consultation type</Text>
      
      <TouchableOpacity 
        style={styles.option}
        onPress={() => router.push('/(main)/consultation/ai-chatbot')}
      >
        <Text style={styles.optionText}>AI Chatbot</Text>
        <Text style={styles.optionDescription}>Get instant support from our AI assistant</Text>
      </TouchableOpacity>

      <TouchableOpacity 
        style={styles.option}
        onPress={() => router.push('/(main)/consultation/online')}
      >
        <Text style={styles.optionText}>Online Consultation</Text>
        <Text style={styles.optionDescription}>Schedule a session with a professional</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 30,
  },
  option: {
    backgroundColor: '#f5f5f5',
    padding: 20,
    borderRadius: 10,
    marginBottom: 15,
  },
  optionText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 5,
  },
  optionDescription: {
    fontSize: 14,
    color: '#666',
  },
});
