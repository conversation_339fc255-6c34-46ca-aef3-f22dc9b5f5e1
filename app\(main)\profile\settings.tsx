import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Switch,
  Platform,
  StatusBar
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useState } from 'react';
import { useRouter } from 'expo-router';

const AppSettings = () => {
  const router = useRouter();

  // Settings state
  const [twoFactorAuth, setTwoFactorAuth] = useState(true);
  const [dataSharing, setDataSharing] = useState(false);
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);

  const securitySettings = [
    {
      id: 'change-password',
      title: 'Change Password',
      showArrow: true,
      onPress: () => console.log('Change Password')
    },
    {
      id: 'two-factor',
      title: 'Two-Factor Authentication',
      toggle: true,
      value: twoFactorAuth,
      onToggle: setTwoFactorAuth
    },
    {
      id: 'data-sharing',
      title: 'Data Sharing',
      toggle: true,
      value: dataSharing,
      onToggle: setDataSharing
    }
  ];

  const appSharingSettings = [
    {
      id: 'notifications',
      title: 'Notifications',
      toggle: true,
      value: notifications,
      onToggle: setNotifications
    },
    {
      id: 'dark-mode',
      title: 'Dark mode',
      toggle: true,
      value: darkMode,
      onToggle: setDarkMode
    },
    {
      id: 'language',
      title: 'Language',
      value: 'English',
      showArrow: true,
      onPress: () => console.log('Language settings')
    }
  ];

  return (
    <View style={styles.container}>
      {/* Header with integrated status bar */}
      <LinearGradient
        colors={['#66948a', '#66948a']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.headerGradient}
      >
        <StatusBar backgroundColor="#66948a" barStyle="light-content" translucent />
        <SafeAreaView style={styles.headerSafeArea}>
          <View style={styles.headerContent}>
            <TouchableOpacity
              onPress={() => router.back()}
              style={styles.backButton}
              activeOpacity={0.7}
            >
              <Text style={styles.backButtonIcon}>←</Text>
              <Text style={styles.backButtonText}>Back</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>App settings</Text>
            <View style={styles.headerSpacer} />
          </View>
        </SafeAreaView>
      </LinearGradient>

      <SafeAreaView style={styles.safeArea}>
        <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Security Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Security</Text>
          <View style={styles.settingsContainer}>
            {securitySettings.map((setting, index) => (
              <TouchableOpacity
                key={setting.id}
                style={[
                  styles.settingItem,
                  index === securitySettings.length - 1 && styles.lastSettingItem
                ]}
                onPress={setting.onPress}
                disabled={setting.toggle}
              >
                <Text style={styles.settingTitle}>{setting.title}</Text>
                <View style={styles.settingControl}>
                  {setting.toggle ? (
                    <Switch
                      value={Boolean(setting.value)}
                      onValueChange={setting.onToggle}
                      trackColor={{ false: '#E5E5E5', true: '#66948a' }}
                      thumbColor={setting.value ? '#FFFFFF' : '#FFFFFF'}
                      ios_backgroundColor="#E5E5E5"
                    />
                  ) : setting.showArrow ? (
                    <Text style={styles.settingArrow}>›</Text>
                  ) : null}
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* App Sharing Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>App Sharing</Text>
          <View style={styles.settingsContainer}>
            {appSharingSettings.map((setting, index) => (
              <TouchableOpacity
                key={setting.id}
                style={[
                  styles.settingItem,
                  index === appSharingSettings.length - 1 && styles.lastSettingItem
                ]}
                onPress={setting.onPress}
                disabled={setting.toggle}
              >
                <Text style={styles.settingTitle}>{setting.title}</Text>
                <View style={styles.settingControl}>
                  {setting.toggle ? (
                    <Switch
                      value={Boolean(setting.value)}
                      onValueChange={setting.onToggle}
                      trackColor={{ false: '#E5E5E5', true: '#66948a' }}
                      thumbColor={setting.value ? '#FFFFFF' : '#FFFFFF'}
                      ios_backgroundColor="#E5E5E5"
                    />
                  ) : setting.value ? (
                    <View style={styles.languageContainer}>
                      <Text style={styles.languageText}>{setting.value}</Text>
                      <Text style={styles.settingArrow}>›</Text>
                    </View>
                  ) : setting.showArrow ? (
                    <Text style={styles.settingArrow}>›</Text>
                  ) : null}
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>MentalEase v1.0.0</Text>
          <Text style={styles.footerCopyright}>© 2023 MentalEase Inc.</Text>
        </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default AppSettings;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  headerGradient: {
    paddingTop: 0,
    paddingBottom: 8,
    paddingHorizontal: 20,
  },
  headerSafeArea: {
    paddingHorizontal: 0,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 0,
    height: 35,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  backButtonIcon: {
    fontSize: 22,
    color: '#FFFFFF',
    marginRight: 6,
  },
  backButtonText: {
    fontSize: 17,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  headerSpacer: {
    width: 60,
  },
  safeArea: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 15,
    marginLeft: 20,
    marginTop: 20,
  },
  settingsContainer: {
    backgroundColor: '#FFFFFF',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  lastSettingItem: {
    borderBottomWidth: 0,
  },
  settingTitle: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '400',
    flex: 1,
  },
  settingControl: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingArrow: {
    fontSize: 20,
    color: '#66948a',
    fontWeight: 'bold',
  },
  languageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  languageText: {
    fontSize: 16,
    color: '#666666',
    marginRight: 8,
  },
  footer: {
    alignItems: 'center',
    marginTop: 50,
    paddingHorizontal: 20,
  },
  footerText: {
    fontSize: 12,
    color: '#999999',
    marginBottom: 2,
  },
  footerCopyright: {
    fontSize: 12,
    color: '#999999',
  },
});
