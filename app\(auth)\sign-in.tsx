import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Image,
  Animated,
  ActivityIndicator,
  Dimensions,
  StatusBar
} from 'react-native';
import { useRouter } from 'expo-router';
import { useUser } from '../context/UserContext';
import { signIn } from '../lib/auth-service';
import AsyncStorage from '@react-native-async-storage/async-storage';

const SignIn = () => {
  const router = useRouter();
  const { updateUserData, setTransitionState } = useUser();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSigningIn, setIsSigningIn] = useState(false); // Prevent flickering
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const buttonScaleAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Function to clear user-specific AsyncStorage data to prevent data leakage between accounts
  const clearUserSpecificAsyncStorage = async () => {
    try {
      console.log('🧹 Clearing AsyncStorage to prevent data leakage between accounts...');

      // Assessment-related keys
      const assessmentKeys = [
        // DASS assessment
        'dass_saved_results',
        'dass_result_timestamp',
        'dass_answers',
        'dass_currentQuestion',
        'dass_progress',

        // PSS assessment
        'pss_saved_results',
        'pss_result_timestamp',
        'pss_answers',
        'pss_currentQuestion',
        'pss_progress',

        // Other potential assessments
        'gad7_saved_results',
        'gad7_result_timestamp',
        'gad7_answers',
        'gad7_currentQuestion',
        'gad7_progress',

        'phq9_saved_results',
        'phq9_result_timestamp',
        'phq9_answers',
        'phq9_currentQuestion',
        'phq9_progress',

        // Mood tracking
        'savedMoods',

        // Chat history (user-specific)
        'chatHistory'
      ];

      // Clear all assessment-related data
      await Promise.all(assessmentKeys.map(key => AsyncStorage.removeItem(key)));

      console.log('✅ AsyncStorage cleared successfully');
    } catch (error) {
      console.error('❌ Error clearing AsyncStorage:', error);
    }
  };

  const handleSignIn = async () => {
    // Input validation with specific error messages
    if (!username && !password) {
      // Shake animation for validation error (Aesthetic-Usability Effect)
      Animated.sequence([
        Animated.timing(slideAnim, { toValue: -10, duration: 100, useNativeDriver: true }),
        Animated.timing(slideAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(slideAnim, { toValue: 0, duration: 100, useNativeDriver: true }),
      ]).start();
      Alert.alert('Input Required', 'Please enter both username and password to continue.');
      return;
    }

    if (!username) {
      Animated.sequence([
        Animated.timing(slideAnim, { toValue: -10, duration: 100, useNativeDriver: true }),
        Animated.timing(slideAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(slideAnim, { toValue: 0, duration: 100, useNativeDriver: true }),
      ]).start();
      Alert.alert('Username Required', 'Please enter your username to continue.');
      return;
    }

    if (!password) {
      Animated.sequence([
        Animated.timing(slideAnim, { toValue: -10, duration: 100, useNativeDriver: true }),
        Animated.timing(slideAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(slideAnim, { toValue: 0, duration: 100, useNativeDriver: true }),
      ]).start();
      Alert.alert('Password Required', 'Please enter your password to continue.');
      return;
    }

    // Additional validation
    if (username.length < 3) {
      Animated.sequence([
        Animated.timing(slideAnim, { toValue: -10, duration: 100, useNativeDriver: true }),
        Animated.timing(slideAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(slideAnim, { toValue: 0, duration: 100, useNativeDriver: true }),
      ]).start();
      Alert.alert('Invalid Username', 'Username must be at least 3 characters long.');
      return;
    }

    if (password.length < 6) {
      Animated.sequence([
        Animated.timing(slideAnim, { toValue: -10, duration: 100, useNativeDriver: true }),
        Animated.timing(slideAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(slideAnim, { toValue: 0, duration: 100, useNativeDriver: true }),
      ]).start();
      Alert.alert('Invalid Password', 'Password must be at least 6 characters long.');
      return;
    }

    setIsLoading(true);
    setIsSigningIn(true);
    setTransitionState(true, 'signin');

    // Button press animation (Fitts's Law - visual feedback)
    Animated.sequence([
      Animated.timing(buttonScaleAnim, { toValue: 0.95, duration: 100, useNativeDriver: true }),
      Animated.timing(buttonScaleAnim, { toValue: 1, duration: 100, useNativeDriver: true }),
    ]).start();

    try {
      console.log('Attempting to sign in with Supabase...');

      // Use our Supabase auth service
      const { data, error } = await signIn(username, password);

      if (error) {
        throw error;
      }

      console.log('Sign in successful:', data);

      // 🚨 CRITICAL: Clear AsyncStorage to prevent data leakage between accounts
      await clearUserSpecificAsyncStorage();

      // Get the user data from the database response
      const user = data.user;

      // Check if user has personal information but has_completed_profile is false
      const hasPersonalInfo = user.first_name && user.last_name && (user.age !== null && user.age !== undefined) && user.gender && user.phone && user.address;
      const shouldUpdateProfileFlag = hasPersonalInfo && !user.has_completed_profile;

      console.log('🔍 Profile check:', {
        first_name: !!user.first_name,
        last_name: !!user.last_name,
        age: user.age,
        age_valid: (user.age !== null && user.age !== undefined),
        gender: !!user.gender,
        phone: !!user.phone,
        address: !!user.address,
        hasPersonalInfo,
        current_flag: user.has_completed_profile,
        shouldUpdate: shouldUpdateProfileFlag
      });

      if (shouldUpdateProfileFlag) {
        console.log('🔄 User has personal info but profile flag is false. Updating database...');

        try {
          // Update the has_completed_profile flag in the database
          const updateResponse = await fetch(`${process.env.EXPO_PUBLIC_SUPABASE_URL}/rest/v1/users?email=eq.${user.email}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'apikey': process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
              'Prefer': 'return=representation'
            },
            body: JSON.stringify({
              has_completed_profile: true
            }),
          });

          if (updateResponse.ok) {
            console.log('✅ Profile completion flag updated in database');
            user.has_completed_profile = true; // Update local user object
          } else {
            console.log('❌ Failed to update profile completion flag');
          }
        } catch (error) {
          console.error('Error updating profile flag:', error);
        }
      }

      // Update user context with actual database values
      updateUserData({
        id: user.id || 'user_' + Math.random().toString(36).substring(2),
        email: user.email,                                       // Get email from database
        username: user.username,                                 // Store username
        emailVerified: user.status || false,                     // Web uses 'status'
        hasCompletedProfile: user.has_completed_profile || false,
        isLoggedIn: true,                                        // Set logged in status for session timeout
        // Include any existing personal information (using web column names)
        firstName: user.name || "",                      // Web uses 'name' for first name
        middleName: user.middle_name || "",
        lastName: user.last_name || "",
        age: user.age || 0,
        gender: user.gender || "",
        civilStatus: user.civil_status || "",
        birthdate: user.birthdate || "",
        birthplace: user.birthplace || "",
        religion: user.religion || "",
        address: user.address || "",
        phone: user.contactnumber || "",                 // Web uses 'contactnumber'
        controlNumber: user.control_number || ""
      }, () => {
        console.log("User signed in successfully");

        // Show success alert
        Alert.alert(
          'Login Successful! 🎉',
          `Welcome back, ${user.username || user.name || 'User'}! You have successfully logged into MentalEase.`,
          [
            {
              text: 'Continue',
              style: 'default',
              onPress: () => {
                // Smooth transition to dashboard (Peak-End Rule - memorable ending)
                Animated.timing(fadeAnim, {
                  toValue: 0,
                  duration: 200,
                  useNativeDriver: true,
                }).start(() => {
                  setTransitionState(false);
                  setIsSigningIn(false);
                  router.replace('/(main)/dashboard');
                });
              }
            }
          ],
          { cancelable: false }
        );
      });
    } catch (error) {
      console.error('Error signing in:', error.message);
      setTransitionState(false);
      setIsSigningIn(false);

      // Error shake animation
      Animated.sequence([
        Animated.timing(slideAnim, { toValue: -15, duration: 100, useNativeDriver: true }),
        Animated.timing(slideAnim, { toValue: 15, duration: 100, useNativeDriver: true }),
        Animated.timing(slideAnim, { toValue: 0, duration: 100, useNativeDriver: true }),
      ]).start();

      // Provide specific error messages based on error type
      let alertTitle = "Login Failed ❌";
      let alertMessage = "";

      if (error.message.includes('Invalid username or password')) {
        alertTitle = "Invalid Credentials ❌";
        alertMessage = "The username or password you entered is incorrect. Please check your credentials and try again.";
      } else if (error.message.includes('Failed to find user')) {
        alertTitle = "Account Not Found ❌";
        alertMessage = "No account found with this username. Please check your username or create a new account.";
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        alertTitle = "Connection Error ❌";
        alertMessage = "Unable to connect to the server. Please check your internet connection and try again.";
      } else if (error.message.includes('timeout')) {
        alertTitle = "Request Timeout ❌";
        alertMessage = "The request took too long to complete. Please try again.";
      } else {
        alertTitle = "Login Error ❌";
        alertMessage = error.message || "An unexpected error occurred during login. Please try again later.";
      }

      Alert.alert(
        alertTitle,
        alertMessage,
        [
          {
            text: 'Try Again',
            style: 'default'
          },
          {
            text: 'Need Help?',
            style: 'cancel',
            onPress: () => {
              Alert.alert(
                'Need Help? 🤝',
                'If you continue to experience issues:\n\n• Check your username and password\n• Ensure you have a stable internet connection\n• Contact <NAME_EMAIL>',
                [{ text: 'OK', style: 'default' }]
              );
            }
          }
        ]
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#66948a" barStyle="light-content" translucent />
      {/* Back Button */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Logo */}
            <View style={styles.logoContainer}>
              <Image
                source={require('../../assets/mainlogo.png')}
                style={styles.logo}
                resizeMode="contain"
              />
              <Text style={styles.logoText}>mentalease</Text>
            </View>

            {/* Title */}
            <Text style={styles.title}>Log in to your account</Text>

            {/* Form */}
            <View style={styles.formContainer}>
              <TextInput
                style={[styles.input, { marginBottom: 16 }]}
                placeholder="Username"
                placeholderTextColor="#999999"
                value={username}
                onChangeText={setUsername}
                autoCapitalize="none"
                returnKeyType="next"
                autoCorrect={false}
                textContentType="username"
              />

              <TextInput
                style={styles.input}
                placeholder="Password"
                placeholderTextColor="#999999"
                value={password}
                onChangeText={setPassword}
                secureTextEntry
                returnKeyType="done"
                onSubmitEditing={Keyboard.dismiss}
                autoCorrect={false}
                textContentType="password"
              />

              <Animated.View style={{ transform: [{ scale: buttonScaleAnim }] }}>
                <TouchableOpacity
                  style={[styles.continueButton, isLoading && styles.continueButtonDisabled]}
                  onPress={() => {
                    Keyboard.dismiss();
                    handleSignIn();
                  }}
                  disabled={isLoading}
                  activeOpacity={0.8}
                >
                {isLoading ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="small" color="#FFFFFF" />
                    <Text style={[styles.continueButtonText, { marginLeft: 8 }]}>Signing in...</Text>
                  </View>
                ) : (
                  <Text style={styles.continueButtonText}>Continue</Text>
                )}
                </TouchableOpacity>
              </Animated.View>

              {/* Or divider */}
              <Text style={styles.orText}>Or</Text>

              {/* Sign Up Link */}
              <TouchableOpacity
                style={styles.signUpButton}
                onPress={() => router.push('/(auth)/create-account')}
              >
                <Text style={styles.signUpText}>Sign Up</Text>
              </TouchableOpacity>
            </View>

            {/* Partnership Text */}
            <Text style={styles.partnershipText}>
              in Partnership with Sanda Diagnostic Center
            </Text>
          </Animated.View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default SignIn;

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8E8E8',
  },
  header: {
    paddingHorizontal: Math.max(20, screenWidth * 0.05),
    paddingTop: Platform.OS === 'ios' ? 10 : StatusBar.currentHeight + 10,
    paddingBottom: 10,
  },
  backButton: {
    // Fitts's Law: Larger touch target for important navigation
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  backIcon: {
    fontSize: 20,
    color: '#2D5016',
    fontWeight: 'bold',
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: Math.max(24, screenWidth * 0.06),
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 40,
    maxWidth: 600,
    alignSelf: 'center',
    width: '100%',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 12,
  },
  logoText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2D5016',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2D5016',
    textAlign: 'center',
    marginBottom: 40,
  },
  formContainer: {
    width: '100%',
    maxWidth: 350,
    marginBottom: 30,
  },
  input: {
    backgroundColor: '#C5C5C5',
    borderRadius: 25,
    paddingHorizontal: 20,
    // Fitts's Law: Larger touch target for input fields
    paddingVertical: 18,
    marginBottom: 16,
    fontSize: 16,
    color: '#333333',
    // Aesthetic-Usability Effect: Better visual feedback
    borderWidth: 2,
    borderColor: 'transparent',
  },
  continueButton: {
    backgroundColor: '#1d473d',
    borderRadius: 25,
    // Fitts's Law: Larger touch target for primary action
    paddingVertical: 20,
    paddingHorizontal: 32,
    alignItems: 'center',
    marginBottom: 20,
    // Minimum touch target size
    minHeight: 56,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 5,
  },
  continueButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  continueButtonDisabled: {
    backgroundColor: '#A9A9A9',
    opacity: 0.7,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  orText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 20,
  },
  signUpButton: {
    alignItems: 'center',
    // Fitts's Law: Larger touch target for secondary action
    paddingVertical: 12,
    paddingHorizontal: 20,
    minHeight: 44,
  },
  signUpText: {
    fontSize: 16,
    color: '#2D5016',
    fontWeight: '600',
  },
  partnershipText: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
    marginTop: 20,
  },
});




