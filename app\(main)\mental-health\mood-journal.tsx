import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  TextInput,
  StatusBar,
  ActivityIndicator,
  Dimensions,
  Alert
} from 'react-native';
import { useState, useEffect } from 'react';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BottomNavigation from '../../components/BottomNavigation';
import StandardHeader from '../../components/StandardHeader';
import { useUser } from '../../context/UserContext';
import { supabase } from '../../lib/supabase';

const MoodJournal = () => {
  const router = useRouter();
  const { userData, updateLastInteraction } = useUser();
  const { date, fromDashboard, fromTracker, fromBottomNav, edit } = useLocalSearchParams();
  const [selectedMood, setSelectedMood] = useState(null);
  const [selectedEmotions, setSelectedEmotions] = useState([]);
  const [journalEntry, setJournalEntry] = useState('');
  const [showNewEntryForm, setShowNewEntryForm] = useState(false);
  const [journalEntries, setJournalEntries] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Load journal entries from database
  useEffect(() => {
    if (userData.id) {
      loadJournalEntries();
    }
  }, [userData.id]);

  const loadJournalEntries = async () => {
    if (!userData.id) {
      console.log('No user ID available for loading journal entries');
      return;
    }

    setIsLoading(true);
    try {
      console.log('📖 Loading journal entries for user:', userData.id);

      // Fetch journal entries from database
      const { data, error } = await supabase
        .from('journal')
        .select('*')
        .eq('user_id', userData.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading journal entries from database:', error);
        Alert.alert('Error', 'Failed to load journal entries. Please try again.');
        return;
      }

      console.log('📖 Loaded journal entries:', data);

      // Transform database entries to match UI format
      const transformedEntries = data.map(entry => ({
        id: entry.id,
        date: formatDateForDisplay(entry.created_at),
        text: entry.thoughts || '',
        mood: parseMoodFromDatabase(entry.mood),
        emotions: parseEmotionsFromDatabase(entry.emotion),
        created_at: entry.created_at
      }));

      setJournalEntries(transformedEntries);
    } catch (error) {
      console.error('Error loading journal entries:', error);
      Alert.alert('Error', 'Failed to load journal entries. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Helper functions for database operations
  const formatDateForDisplay = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getCurrentDate = () => {
    return new Date().toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const parseMoodFromDatabase = (moodString) => {
    if (!moodString) return { emoji: '😐', label: 'Okay' };

    // Map mood labels to emoji and label objects
    const moodMap = {
      'Sad': { emoji: '😔', label: 'Sad' },
      'Meh': { emoji: '😕', label: 'Meh' },
      'Okay': { emoji: '😐', label: 'Okay' },
      'Good': { emoji: '😊', label: 'Good' },
      'Great': { emoji: '😁', label: 'Great' }
    };

    // Handle both JSON format (legacy) and string format (current)
    try {
      const parsed = JSON.parse(moodString);
      if (parsed.emoji && parsed.label) return parsed;
    } catch (e) {
      // Not JSON, treat as string label
      return moodMap[moodString] || { emoji: '😐', label: 'Okay' };
    }

    return moodMap[moodString] || { emoji: '😐', label: 'Okay' };
  };

  const parseEmotionsFromDatabase = (emotionString) => {
    if (!emotionString) return [];

    // Handle both JSON format (legacy) and comma-separated string format (current)
    try {
      const parsed = JSON.parse(emotionString);
      return Array.isArray(parsed) ? parsed : [];
    } catch (e) {
      // Not JSON, treat as comma-separated string
      return emotionString.split(',').map(emotion => emotion.trim()).filter(Boolean);
    }
  };

  const moods = [
    { emoji: '😔', label: 'Sad' },
    { emoji: '😕', label: 'Meh' },
    { emoji: '😐', label: 'Okay' },
    { emoji: '😊', label: 'Good' },
    { emoji: '😁', label: 'Great' }
  ];

  const emotions = [
    'Joy', 'Trust', 'Fear', 'Surprise',
    'Sadness', 'Disgust', 'Anger', 'Anticipation',
    'Serenity', 'Interest', 'Apprehension', 'Distraction',
    'Pensiveness', 'Boredom', 'Annoyance', 'Acceptance',
    'Ecstasy', 'Vigilance', 'Terror', 'Amazement',
    'Grief', 'Loathing', 'Rage', 'Admiration',
    'Love', 'Submission', 'Awe', 'Disapproval',
    'Remorse', 'Contempt', 'Aggressiveness', 'Optimism'
  ];

  const handleMoodSelect = (moodIndex) => {
    setSelectedMood(moodIndex);
  };

  const handleEmotionToggle = (emotion) => {
    if (selectedEmotions.includes(emotion)) {
      setSelectedEmotions(selectedEmotions.filter(e => e !== emotion));
    } else {
      setSelectedEmotions([...selectedEmotions, emotion]);
    }
  };

  const handleSave = async () => {
    if (selectedMood !== null && journalEntry.trim()) {
      if (!userData.id) {
        Alert.alert('Error', 'Please log in to save journal entries.');
        return;
      }

      setIsSaving(true);
      updateLastInteraction(); // Update session activity

      try {
        console.log('💾 Saving journal entry to database...');

        // Prepare data for database - match existing format exactly like user ID 1
        const journalData = {
          user_id: userData.id, // Keep as number to match existing format
          mood: moods[selectedMood].label, // Store just the label string (e.g., "Good", "Great")
          emotion: selectedEmotions.join(','), // Store as comma-separated string (no spaces)
          thoughts: journalEntry.trim(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        console.log('📝 Journal data to save:', journalData);

        // Save to database
        const { data, error } = await supabase
          .from('journal')
          .insert(journalData)
          .select();

        if (error) {
          console.error('Database error:', error);
          Alert.alert('Error', 'Failed to save journal entry. Please try again.');
          return;
        }

        console.log('✅ Journal entry saved successfully:', data);

        // Create UI entry for immediate display
        const newEntry = {
          id: data[0].id,
          date: getCurrentDate(),
          text: journalEntry,
          mood: moods[selectedMood],
          emotions: selectedEmotions,
          created_at: data[0].created_at
        };

        // Update local state
        setJournalEntries(prevEntries => [newEntry, ...prevEntries]);

        // Reset form
        setSelectedMood(null);
        setSelectedEmotions([]);
        setJournalEntry('');
        setShowNewEntryForm(false);

        Alert.alert('Success', 'Journal entry saved successfully!');
      } catch (error) {
        console.error('Error saving journal entry:', error);
        Alert.alert('Error', 'Failed to save journal entry. Please try again.');
      } finally {
        setIsSaving(false);
      }
    } else {
      Alert.alert('Incomplete Entry', 'Please select a mood and write your thoughts before saving.');
    }
  };

  const handleBackPress = () => {
    // Determine the proper back navigation based on how user arrived
    if (fromDashboard === 'true') {
      // Came from mental health dashboard
      router.push('/(main)/mental-health');
    } else if (fromTracker === 'true') {
      // Came from mood tracker
      router.push('/(main)/mental-health/mood-tracker');
    } else if (fromBottomNav === 'true') {
      // Came from bottom navigation - go back to main dashboard
      router.push('/(main)/dashboard');
    } else {
      // Default: use router.back() for other cases (direct navigation)
      router.back();
    }
  };

  const handleCancel = () => {
    setSelectedMood(null);
    setSelectedEmotions([]);
    setJournalEntry('');
    setShowNewEntryForm(false);
  };

  return (
    <View style={styles.container}>
      <StandardHeader title="Stress Management" onBackPress={handleBackPress} />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Journal Header */}
        <View style={styles.journalHeader}>
          <View style={styles.journalTitleContainer}>
            <View style={styles.journalIcon}>
              <Text style={styles.journalIconText}>📝</Text>
            </View>
            <Text style={styles.journalTitle}>Journal</Text>
          </View>
          {!showNewEntryForm && (
            <TouchableOpacity
              style={styles.addButton}
              onPress={() => setShowNewEntryForm(true)}
            >
              <Text style={styles.addButtonText}>+</Text>
            </TouchableOpacity>
          )}
          {showNewEntryForm && (
            <Text style={styles.currentDate}>{getCurrentDate()}</Text>
          )}
        </View>

        {showNewEntryForm ? (
          /* New Entry Form */
          <View style={styles.newEntryForm}>
            {/* Mood Selection */}
            <Text style={styles.sectionTitle}>How are you feeling today?</Text>
            <View style={styles.moodContainer}>
              {moods.map((mood, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.moodOption,
                    selectedMood === index && styles.selectedMoodOption
                  ]}
                  onPress={() => handleMoodSelect(index)}
                >
                  <Text style={styles.moodEmoji}>{mood.emoji}</Text>
                  <Text style={styles.moodLabel}>{mood.label}</Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Emotion Selection */}
            <Text style={styles.sectionTitle}>What emotions are you feeling?</Text>
            <View style={styles.emotionsContainer}>
              {emotions.map((emotion) => (
                <TouchableOpacity
                  key={emotion}
                  style={[
                    styles.emotionChip,
                    selectedEmotions.includes(emotion) && styles.selectedEmotionChip
                  ]}
                  onPress={() => handleEmotionToggle(emotion)}
                >
                  <Text
                    style={[
                      styles.emotionText,
                      selectedEmotions.includes(emotion) && styles.selectedEmotionText
                    ]}
                  >
                    {emotion}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Text Input */}
            <Text style={styles.sectionTitle}>What's on your mind?</Text>
            <TextInput
              style={styles.journalInput}
              placeholder="Write your thoughts here..."
              value={journalEntry}
              onChangeText={setJournalEntry}
              multiline
              textAlignVertical="top"
              placeholderTextColor="#999"
            />

            {/* Action Buttons */}
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={handleCancel}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.saveButton, isSaving && styles.disabledButton]}
                onPress={handleSave}
                disabled={isSaving}
              >
                {isSaving ? (
                  <View style={styles.saveLoadingContainer}>
                    <ActivityIndicator size="small" color="#FFFFFF" />
                    <Text style={[styles.saveButtonText, { marginLeft: 8 }]}>Saving...</Text>
                  </View>
                ) : (
                  <Text style={styles.saveButtonText}>Save</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          /* Journal Entries List */
          <View style={styles.journalList}>
            {isLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#66948a" />
                <Text style={styles.loadingText}>Loading your journal entries...</Text>
              </View>
            ) : journalEntries.length > 0 ? (
              journalEntries.map((entry) => (
                <View key={entry.id} style={styles.journalEntry}>
                  <Text style={styles.entryDate}>{entry.date}</Text>
                  <Text style={styles.entryText}>{entry.text}</Text>

                  {/* Display emotions if available */}
                  {entry.emotions && entry.emotions.length > 0 && (
                    <View style={styles.entryEmotions}>
                      <Text style={styles.entryEmotionsLabel}>Emotions:</Text>
                      <View style={styles.entryEmotionsContainer}>
                        {entry.emotions.map((emotion, index) => (
                          <View key={index} style={styles.entryEmotionChip}>
                            <Text style={styles.entryEmotionText}>{emotion}</Text>
                          </View>
                        ))}
                      </View>
                    </View>
                  )}

                  <View style={styles.entryMood}>
                    <Text style={styles.entryMoodEmoji}>{entry.mood.emoji}</Text>
                    <Text style={styles.entryMoodLabel}>{entry.mood.label}</Text>
                  </View>
                </View>
              ))
            ) : (
              <View style={styles.emptyState}>
                <Text style={styles.emptyStateText}>No journal entries yet</Text>
                <Text style={styles.emptyStateSubtext}>Tap the + button to create your first entry</Text>
              </View>
            )}
          </View>
        )}
      </ScrollView>

      <BottomNavigation />
    </View>
  );
};

export default MoodJournal;

const { width: screenWidth } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },

  content: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  journalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  journalTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  journalIcon: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#E8F5E8',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  journalIconText: {
    fontSize: 16,
  },
  journalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#66948a',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButtonText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
  },
  currentDate: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  newEntryForm: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: Math.max(20, screenWidth * 0.05),
    marginVertical: 20,
    borderRadius: 20,
    padding: Math.max(20, screenWidth * 0.05),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    maxWidth: 600,
    alignSelf: 'center',
    width: '100%',
  },
  sectionTitle: {
    fontSize: 16,
    color: '#2C3E50',
    marginBottom: 15,
    fontWeight: '500',
  },
  moodContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 25,
    backgroundColor: '#F8F9FA',
    borderRadius: 15,
    padding: 15,
  },
  moodOption: {
    alignItems: 'center',
    flex: 1,
    paddingVertical: 10,
  },
  selectedMoodOption: {
    backgroundColor: '#E8F5E8',
    borderRadius: 10,
    marginHorizontal: 2,
  },
  moodEmoji: {
    fontSize: 32,
    marginBottom: 5,
  },
  moodLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  emotionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 25,
  },
  emotionChip: {
    backgroundColor: '#F8F9FA',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 15,
    margin: 4,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  selectedEmotionChip: {
    backgroundColor: '#6B9B7A',
    borderColor: '#6B9B7A',
  },
  emotionText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '500',
  },
  selectedEmotionText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  journalInput: {
    backgroundColor: '#F8F9FA',
    borderRadius: 15,
    padding: 15,
    height: 120,
    marginBottom: 25,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    fontSize: 16,
    color: '#2C3E50',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 15,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#66948a',
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#66948a',
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  journalList: {
    paddingHorizontal: Math.max(20, screenWidth * 0.05),
    paddingBottom: 100,
    alignItems: 'center',
  },
  journalEntry: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: Math.max(20, screenWidth * 0.05),
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    maxWidth: 600,
    width: '100%',
  },
  entryDate: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 10,
  },
  entryText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 15,
  },
  entryMood: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  entryMoodEmoji: {
    fontSize: 16,
    marginRight: 8,
  },
  entryMoodLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  entryEmotions: {
    marginBottom: 15,
  },
  entryEmotionsLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
    marginBottom: 8,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  entryEmotionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },
  entryEmotionChip: {
    backgroundColor: '#E8F5E8',
    borderRadius: 12,
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  entryEmotionText: {
    fontSize: 12,
    color: '#2E7D32',
    fontWeight: '500',
  },
  disabledButton: {
    opacity: 0.6,
  },
  loadingContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  saveLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
});
