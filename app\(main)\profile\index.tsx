import React, { useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Animated,
  Platform,
  StatusBar,
  Image,
  ActivityIndicator
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { useUser } from '../../context/UserContext';
import { Ionicons } from '@expo/vector-icons';

const UserProfile = () => {
  const router = useRouter();
  const { userData, logout, updateUserData } = useUser();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const [isLoading, setIsLoading] = useState(true);
  const [hideControlNumber, setHideControlNumber] = useState(false);

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, []);

  // Fetch user data from database when component loads
  useEffect(() => {
    const fetchUserData = async () => {
      if (!userData.email) {
        setIsLoading(false);
        return;
      }

      try {
        console.log('Fetching user profile data for:', userData.email);

        const response = await fetch(`${process.env.EXPO_PUBLIC_SUPABASE_URL}/rest/v1/users?email=eq.${userData.email}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'apikey': process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
          },
        });

        if (response.ok) {
          const users = await response.json();
          if (users && users.length > 0) {
            const user = users[0];

            // Update user context with database data
            const updatedUserData = {
              ...userData,
              firstName: user.name || userData.firstName,                    // Web uses 'name' for first name
              middleName: user.middle_name || userData.middleName,
              lastName: user.last_name || userData.lastName,
              age: user.age || userData.age,
              gender: user.gender || userData.gender,
              civilStatus: user.civil_status || userData.civilStatus,
              phone: user.contactnumber || userData.phone,                   // Web uses 'contactnumber'
              address: user.address || userData.address,
              birthdate: user.birthdate || userData.birthdate,
              birthplace: user.birthplace || userData.birthplace,
              religion: user.religion || userData.religion,
              controlNumber: user.control_number || userData.controlNumber,
              emailVerified: user.status || userData.emailVerified,          // Web uses 'status'
              hasCompletedProfile: user.has_completed_profile || userData.hasCompletedProfile,
              username: user.username || userData.username
            };

            updateUserData(updatedUserData);
            console.log('✅ User profile data updated from database');
          }
        } else {
          console.log('❌ Failed to fetch user profile data');
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, [userData.email]);

  const handleLogout = () => {
    logout();
    router.replace('/(auth)/sign-in');
  };

  const menuItems = [
    {
      id: 'app-settings',
      title: 'App settings',
      icon: 'settings-outline',
      onPress: () => router.push('/(main)/profile/settings'),
      showArrow: true
    },
    {
      id: 'appointment-history',
      title: 'Appointment History',
      icon: 'calendar-outline',
      onPress: () => router.push('/payment-details'),
      showArrow: true
    },
    {
      id: 'help-support',
      title: 'Help & Support',
      icon: 'help-circle-outline',
      onPress: () => router.push('/help-support'),
      showArrow: true
    },
    {
      id: 'privacy-policy',
      title: 'Privacy Policy',
      icon: 'document-text-outline',
      onPress: () => router.push('/privacy-policy'),
      showArrow: true
    },
    {
      id: 'terms-conditions',
      title: 'Terms & Conditions',
      icon: 'document-outline',
      onPress: () => router.push('/terms-conditions'),
      showArrow: true
    },
    {
      id: 'about',
      title: 'About',
      icon: 'information-circle-outline',
      onPress: () => router.push('/about'),
      showArrow: true
    }
  ];

  // Show loading state while fetching data
  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <StatusBar backgroundColor="#66948a" barStyle="light-content" />
          <SafeAreaView>
            <View style={styles.headerContent}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => router.back()}
              >
                <Ionicons name="chevron-back" size={24} color="#FFFFFF" />
                <Text style={styles.backButtonText}>Back</Text>
              </TouchableOpacity>
              <Text style={styles.headerTitle}>Profile & Settings</Text>
              <View style={styles.headerSpacer} />
            </View>
          </SafeAreaView>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#66948a" />
          <Text style={styles.loadingText}>Loading profile...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <StatusBar backgroundColor="#66948a" barStyle="light-content" />
        <SafeAreaView>
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Ionicons name="chevron-back" size={24} color="#FFFFFF" />
              <Text style={styles.backButtonText}>Back</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Profile & Settings</Text>
            <View style={styles.headerSpacer} />
          </View>
        </SafeAreaView>
      </View>

      <View style={styles.content}>
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Profile Section */}
          <View style={styles.profileSection}>
            <View style={styles.profileImageContainer}>
              {userData.profileImage ? (
                <Image source={userData.profileImage} style={styles.profileImage} />
              ) : (
                <View style={styles.profileImagePlaceholder}>
                  <Text style={styles.profileInitial}>
                    {(userData.firstName || 'L').charAt(0)}
                  </Text>
                </View>
              )}
            </View>

            <Text style={styles.userName}>
              {userData.firstName && userData.lastName
                ? `${userData.firstName}${userData.middleName ? ` ${userData.middleName}` : ''} ${userData.lastName}`.toUpperCase()
                : 'USER NAME'
              }
            </Text>

            <View style={styles.userIdContainer}>
              <Text style={styles.userIdLabel}>
                Control Number: {hideControlNumber
                  ? (userData.controlNumber ? userData.controlNumber.substring(0, 4) + '*******' : '****-****-****')
                  : (userData.controlNumber || 'Not assigned')
                }
              </Text>
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => setHideControlNumber(!hideControlNumber)}
              >
                <Ionicons
                  name={hideControlNumber ? "eye-off-outline" : "eye-outline"}
                  size={18}
                  color="#666666"
                />
              </TouchableOpacity>
            </View>

            <View style={styles.verifiedBadge}>
              <Ionicons name="checkmark" size={14} color="#7BA05B" />
              <Text style={styles.verifiedText}>Verified</Text>
            </View>

            <TouchableOpacity
              style={styles.editProfileButton}
              onPress={() => router.push('/(main)/profile/edit-profile')}
            >
              <Text style={styles.editProfileButtonText}>Edit Profile</Text>
            </TouchableOpacity>
          </View>

          {/* Menu Items */}
          <View style={styles.menuSection}>
            {menuItems.map((item, index) => (
              <TouchableOpacity
                key={item.id}
                style={[
                  styles.menuItem,
                  index === menuItems.length - 1 && styles.lastMenuItem
                ]}
                onPress={item.onPress}
              >
                <View style={styles.menuItemLeft}>
                  <Ionicons name={item.icon} size={20} color="#666666" />
                  <Text style={styles.menuItemTitle}>{item.title}</Text>
                </View>
                {item.showArrow && (
                  <Ionicons name="chevron-forward" size={20} color="#7BA05B" />
                )}
              </TouchableOpacity>
            ))}
          </View>

          {/* Logout Button */}
          <TouchableOpacity
            style={styles.logoutButton}
            onPress={handleLogout}
          >
            <Text style={styles.logoutButtonText}>Log Out</Text>
          </TouchableOpacity>

          {/* Version Info */}
          <View style={styles.versionSection}>
            <Text style={styles.versionText}>MentalEase v1.0.0</Text>
            <Text style={styles.copyrightText}>© 2023 MentalEase Inc.</Text>
          </View>
        </ScrollView>
      </View>
    </View>
  );
};

export default UserProfile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    backgroundColor: '#66948a',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 8,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '500',
    marginLeft: 4,
  },
  headerTitle: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  headerSpacer: {
    width: 60,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  profileSection: {
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  profileImageContainer: {
    marginBottom: 20,
  },
  profileImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  profileImagePlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#D4A574',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileInitial: {
    fontSize: 32,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
    textAlign: 'center',
  },
  userIdContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  userIdLabel: {
    fontSize: 14,
    color: '#666666',
    marginRight: 8,
  },
  eyeButton: {
    padding: 4,
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    marginBottom: 20,
  },
  verifiedText: {
    fontSize: 14,
    color: '#7BA05B',
    fontWeight: '500',
    marginLeft: 4,
  },
  editProfileButton: {
    backgroundColor: '#7BA05B',
    borderRadius: 20,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  editProfileButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  menuSection: {
    backgroundColor: '#FFFFFF',
    marginTop: 16,
    marginBottom: 16,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  lastMenuItem: {
    borderBottomWidth: 0,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemTitle: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '400',
    marginLeft: 16,
  },
  logoutButton: {
    backgroundColor: '#D2746B',
    borderRadius: 25,
    paddingVertical: 14,
    alignItems: 'center',
    marginHorizontal: 20,
    marginTop: 16,
    marginBottom: 32,
  },
  logoutButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  versionSection: {
    alignItems: 'center',
    marginBottom: 20,
  },
  versionText: {
    fontSize: 14,
    color: '#999999',
    marginBottom: 4,
  },
  copyrightText: {
    fontSize: 14,
    color: '#999999',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
});
