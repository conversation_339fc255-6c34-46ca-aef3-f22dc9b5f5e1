import { Stack } from 'expo-router';

export default function AuthLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
        contentStyle: {
          backgroundColor: '#ffffff',
        },
        // Smooth auth flow transitions (Peak-End Rule - memorable experience)
        animation: 'slide_from_right',
        animationDuration: 250,
        gestureEnabled: true,
        presentation: 'card',
      }}
    >
      <Stack.Screen name="welcome" />
      <Stack.Screen name="consent" />
      <Stack.Screen name="sign-in" />
      <Stack.Screen name="create-account" />
      <Stack.Screen name="email-verification" />
      <Stack.Screen name="personal-information" />
    </Stack>
  );
}
