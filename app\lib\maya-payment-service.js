import { supabaseUrl, supabase<PERSON>nonKey } from './supabase';

// Maya API Sandbox Configuration (Party 1)
const MAYA_CONFIG = {
  // Sandbox API Base URL
  baseUrl: 'https://pg-sandbox.paymaya.com',

  // Sandbox Public Key (Party 1)
  publicKey: 'pk-Z0OSzLvIcOI2UIvDhdTGVVfRSSeiGStnceqwUE7n0Ah',

  // Sandbox Secret Key (Party 1) - In production, this should be stored securely on backend
  secretKey: 'sk-X8qolYjy62kIzEbr0QRK1h4b4KDVHaNcwMYk39jInSl',

  // Test Cards for Sandbox (Official Maya test cards)
  testCards: {
    visa: {
      number: '****************',
      expMonth: '12',
      expYear: '2025',
      cvc: '123'
    },
    visa2: {
      number: '****************',
      expMonth: '12',
      expYear: '2025',
      cvc: '111'
    },
    mastercard: {
      number: '****************',
      expMonth: '12',
      expYear: '2025',
      cvc: '111'
    },
    mastercard2: {
      number: '****************',
      expMonth: '12',
      expYear: '2025',
      cvc: '111'
    }
  }
};

/**
 * Create invoice record in database
 * @param {Object} invoiceData - Invoice information
 * @returns {Promise<Object>} - Created invoice
 */
export const createInvoice = async (invoiceData) => {
  try {
    console.log('📄 Creating invoice record...');

    const invoice = {
      user_id: invoiceData.user_id,
      reference_number: invoiceData.reference_number,
      payment_status: invoiceData.payment_status || 'pending',
      amount: invoiceData.amount,
      schedule_id: invoiceData.schedule_id || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const response = await fetch(`${supabaseUrl}/rest/v1/invoices`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Prefer': 'return=representation'
      },
      body: JSON.stringify(invoice)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to create invoice: ${errorText}`);
    }

    const result = await response.json();
    console.log('✅ Invoice created successfully:', result[0]);
    return result[0];
  } catch (error) {
    console.error('❌ Error creating invoice:', error);
    throw error;
  }
};

/**
 * Update invoice payment status
 * @param {string} referenceNumber - Invoice reference number
 * @param {string} status - Payment status
 * @returns {Promise<Object>} - Updated invoice
 */
export const updateInvoiceStatus = async (referenceNumber, status) => {
  try {
    console.log('💾 Updating invoice status...');

    const updateData = {
      payment_status: status,
      updated_at: new Date().toISOString()
    };

    const response = await fetch(`${supabaseUrl}/rest/v1/invoices?reference_number=eq.${referenceNumber}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Prefer': 'return=representation'
      },
      body: JSON.stringify(updateData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to update invoice: ${errorText}`);
    }

    const result = await response.json();
    console.log('✅ Invoice status updated successfully');
    return result[0];
  } catch (error) {
    console.error('❌ Error updating invoice status:', error);
    throw error;
  }
};

/**
 * Create a Maya payment token for a card
 * @param {Object} cardDetails - Card information
 * @returns {Promise<Object>} - Payment token response
 */
export const createPaymentToken = async (cardDetails) => {
  try {
    console.log('🔐 Creating Maya payment token...');
    console.log('Card details:', cardDetails);

    const tokenData = {
      card: {
        number: cardDetails.number.toString(),
        expMonth: cardDetails.expMonth.toString(),
        expYear: cardDetails.expYear.toString(),
        cvc: cardDetails.cvc.toString()
      }
    };

    console.log('Token data being sent:', tokenData);

    const response = await fetch(`${MAYA_CONFIG.baseUrl}/payments/v1/payment-tokens`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${btoa(MAYA_CONFIG.publicKey + ':')}`
      },
      body: JSON.stringify(tokenData)
    });

    const result = await response.json();
    console.log('Maya token response:', result);

    if (!response.ok) {
      console.error('Maya token error response:', result);
      throw new Error(result.message || result.error || 'Failed to create payment token');
    }

    console.log('✅ Payment token created successfully');
    return result;
  } catch (error) {
    console.error('❌ Error creating payment token:', error);
    throw error;
  }
};

/**
 * Create a Maya payment for appointment
 * @param {Object} paymentData - Payment information
 * @returns {Promise<Object>} - Payment response
 */
export const createAppointmentPayment = async (paymentData) => {
  try {
    console.log('💳 Creating Maya payment for appointment...');
    console.log('Payment data:', paymentData);

    const {
      amount,
      currency = 'PHP',
      description,
      appointmentId,
      userId,
      paymentToken,
      redirectUrl
    } = paymentData;

    // Create payment request
    const paymentRequest = {
      totalAmount: {
        value: amount,
        currency: currency
      },
      buyer: {
        firstName: paymentData.buyer?.firstName || 'Test',
        lastName: paymentData.buyer?.lastName || 'User',
        email: paymentData.buyer?.email || '<EMAIL>',
        phone: paymentData.buyer?.phone || '+639171234567'
      },
      items: [
        {
          name: description || 'Mental Health Consultation',
          quantity: 1,
          code: `APPT-${appointmentId}`,
          description: `Appointment ID: ${appointmentId}`,
          amount: {
            value: amount,
            currency: currency
          },
          totalAmount: {
            value: amount,
            currency: currency
          }
        }
      ],
      redirectUrl: {
        success: redirectUrl?.success || 'https://your-app.com/payment/success',
        failure: redirectUrl?.failure || 'https://your-app.com/payment/failure',
        cancel: redirectUrl?.cancel || 'https://your-app.com/payment/cancel'
      },
      requestReferenceNumber: `APPT-${appointmentId}-${Date.now()}`,
      metadata: {
        appointmentId: appointmentId.toString(),
        userId: userId.toString(),
        paymentType: 'appointment_consultation'
      }
    };

    // If payment token is provided, add it to the request
    if (paymentToken) {
      paymentRequest.paymentTokenId = paymentToken;
    }

    console.log('Payment request being sent:', JSON.stringify(paymentRequest, null, 2));

    const response = await fetch(`${MAYA_CONFIG.baseUrl}/checkout/v1/checkouts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${btoa(MAYA_CONFIG.publicKey + ':')}`
      },
      body: JSON.stringify(paymentRequest)
    });

    const result = await response.json();
    console.log('Maya payment response:', result);
    console.log('Response status:', response.status);

    if (!response.ok) {
      console.error('Maya payment error response:', result);
      throw new Error(result.message || result.error || 'Failed to create payment');
    }

    console.log('✅ Maya payment created successfully');
    console.log('Payment ID:', result.checkoutId);
    console.log('Redirect URL:', result.redirectUrl);

    return result;
  } catch (error) {
    console.error('❌ Error creating Maya payment:', error);
    throw error;
  }
};

/**
 * Verify payment status with Maya
 * @param {string} checkoutId - Maya checkout ID
 * @returns {Promise<Object>} - Payment status
 */
export const verifyPaymentStatus = async (checkoutId) => {
  try {
    console.log('🔍 Verifying payment status for checkout:', checkoutId);

    const response = await fetch(`${MAYA_CONFIG.baseUrl}/checkout/v1/checkouts/${checkoutId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${btoa(MAYA_CONFIG.secretKey + ':')}`
      }
    });

    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(result.message || 'Failed to verify payment');
    }

    console.log('✅ Payment status verified:', result.status);
    return result;
  } catch (error) {
    console.error('❌ Error verifying payment:', error);
    throw error;
  }
};

/**
 * Update appointment payment status in database
 * @param {number} appointmentId - Appointment ID
 * @param {Object} paymentData - Payment information
 * @returns {Promise<Object>} - Update result
 */
export const updateAppointmentPaymentStatus = async (appointmentId, paymentData) => {
  try {
    console.log('💾 Updating appointment payment status...');

    const updateData = {
      payment_status: paymentData.status === 'completed',
      payment_method: 'maya',
      updated_at: new Date().toISOString()
    };

    const response = await fetch(`${supabaseUrl}/rest/v1/appointment?id=eq.${appointmentId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Prefer': 'return=representation'
      },
      body: JSON.stringify(updateData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to update appointment: ${errorText}`);
    }

    const result = await response.json();
    console.log('✅ Appointment payment status updated successfully');
    return result;
  } catch (error) {
    console.error('❌ Error updating appointment payment status:', error);
    throw error;
  }
};

/**
 * Generate unique reference number
 * @param {number} userId - User ID
 * @param {number} appointmentId - Appointment ID
 * @returns {string} - Reference number
 */
export const generateReferenceNumber = (userId, appointmentId) => {
  const timestamp = Date.now();
  return `MAYA-${userId}-${appointmentId}-${timestamp}`;
};

/**
 * Get test card for sandbox testing
 * @param {string} cardType - Type of card (visa, mastercard, jcb)
 * @returns {Object} - Test card details
 */
export const getTestCard = (cardType = 'visa') => {
  return MAYA_CONFIG.testCards[cardType] || MAYA_CONFIG.testCards.visa;
};

/**
 * Process appointment payment with Maya (Sandbox)
 * @param {Object} appointmentData - Appointment and payment data
 * @returns {Promise<Object>} - Payment result
 */
export const processAppointmentPayment = async (appointmentData) => {
  try {
    console.log('🚀 Processing appointment payment with Maya...');

    const {
      appointmentId,
      userId,
      amount,
      cardDetails,
      buyer,
      scheduleId = null
    } = appointmentData;

    // Generate reference number
    const referenceNumber = generateReferenceNumber(userId, appointmentId);

    // Step 1: Create invoice record
    const invoice = await createInvoice({
      user_id: userId,
      reference_number: referenceNumber,
      payment_status: 'pending',
      amount: amount,
      schedule_id: scheduleId
    });

    // Step 2: Create payment token with provided card details
    console.log('Creating Maya payment token with card details...');

    const tokenResponse = await createPaymentToken(cardDetails);

    // Step 3: Create payment with token
    console.log('Creating Maya checkout payment...');

    const paymentResponse = await createAppointmentPayment({
      amount: amount,
      description: 'Mental Health Consultation Fee',
      appointmentId: appointmentId,
      userId: userId,
      paymentToken: tokenResponse.paymentTokenId,
      buyer: buyer
    });

    // Step 4: Update invoice status (simulate success for sandbox)
    await updateInvoiceStatus(referenceNumber, 'paid');

    // Step 5: Update appointment payment status
    await updateAppointmentPaymentStatus(appointmentId, {
      status: 'completed'
    });

    console.log('✅ Appointment payment processed successfully');
    return {
      success: true,
      invoiceId: invoice.id,
      referenceNumber: referenceNumber,
      checkoutId: paymentResponse.checkoutId,
      amount: amount,
      status: 'completed'
    };
  } catch (error) {
    console.error('❌ Error processing appointment payment:', error);

    // Update invoice status to failed if it was created
    if (error.referenceNumber) {
      try {
        await updateInvoiceStatus(error.referenceNumber, 'failed');
      } catch (updateError) {
        console.error('Failed to update invoice status to failed:', updateError);
      }
    }

    throw error;
  }
};

export default {
  createInvoice,
  updateInvoiceStatus,
  createPaymentToken,
  createAppointmentPayment,
  verifyPaymentStatus,
  updateAppointmentPaymentStatus,
  getTestCard,
  generateReferenceNumber,
  processAppointmentPayment,
  MAYA_CONFIG
};
