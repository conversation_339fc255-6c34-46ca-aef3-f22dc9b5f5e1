import React, { useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Animated,
  Dimensions,
  StatusBar
} from 'react-native';
import { useRouter } from 'expo-router';

const { width } = Dimensions.get('window');

const Welcome = () => {
  const router = useRouter();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#66948a" barStyle="light-content" translucent />
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        {/* Welcome Title */}
        <Text style={styles.welcomeTitle}>Welcome to</Text>

        {/* Logo */}
        <View style={styles.logoContainer}>
          <Image
            source={require('../../assets/mainlogo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.logoText}>mentalease</Text>
        </View>

        {/* Description */}
        <Text style={styles.description}>
          Your mobile companion for emotional well-being.
          Meet Aira, your mental chatbot companion, and
          access easy appointments and assessments for your
          mental well-being journey.
        </Text>

        {/* Buttons */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.accountButton}
            onPress={() => router.push('/(auth)/sign-in')}
          >
            <Text style={styles.accountButtonText}>I already have an account</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.startedButton}
            onPress={() => router.push('/(auth)/consent')}
          >
            <Text style={styles.startedButtonText}>Let's get started</Text>
          </TouchableOpacity>
        </View>

        {/* Partnership Text */}
        <Text style={styles.partnershipText}>
          in Partnership with Sanda Diagnostic Center
        </Text>
      </Animated.View>
    </SafeAreaView>
  );
};

export default Welcome;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#E8E8E8',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  welcomeTitle: {
    fontSize: 28,
    fontWeight: '600',
    color: '#2D5016',
    textAlign: 'center',
    marginBottom: 30,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 12,
  },
  logoText: {
    fontSize: 24,
    fontWeight: '600',
    color: '#2D5016',
  },
  description: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 60,
    paddingHorizontal: 20,
  },
  buttonContainer: {
    width: '100%',
    marginBottom: 40,
  },
  accountButton: {
    backgroundColor: '#1d473d',
    borderRadius: 25,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  accountButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  startedButton: {
    backgroundColor: '#7c928d',
    borderRadius: 25,
    paddingVertical: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  startedButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  partnershipText: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
  },
});
