// Updated auth service to work with unified 'users' table
// This replaces auth-service.js after migration
import { supabaseUrl, supabase<PERSON>nonKey } from './supabase';
import { getEmailServerUrls, EMAIL_CONFIG } from '../config/email-config';

export const signUp = async (email, password) => {
  try {
    console.log('Creating user in users table:', email);

    // Check if user already exists
    const checkResponse = await fetch(`${supabaseUrl}/rest/v1/users?email=eq.${email}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnon<PERSON>ey,
      },
    });

    const existingUsers = await checkResponse.json();

    if (existingUsers && existingUsers.length > 0) {
      throw new Error('User with this email already exists');
    }

    // Create new user in users table with web-compatible schema
    const signUpResponse = await fetch(`${supabaseUrl}/rest/v1/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabase<PERSON>nonKey,
        'Prefer': 'return=representation'
      },
      body: JSON.stringify({
        email,
        password,
        status: false,                    // Web uses 'status' instead of 'email_verified'
        role: 'patient',                  // Default role for mobile users
        has_completed_profile: false,     // Mobile-specific field
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }),
    });

    const data = await signUpResponse.json();

    if (!signUpResponse.ok) {
      throw new Error(data.message || 'Failed to create user');
    }

    console.log('User created in users table:', data);

    // Generate verification code
    if (data && data.length > 0) {
      const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
      const expirationTime = Date.now() + (10 * 60 * 1000); // 10 minutes

      // Store verification code in database using web column names
      try {
        const updateCodeResponse = await fetch(`${supabaseUrl}/rest/v1/users?email=eq.${email}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabaseAnonKey,
            'Prefer': 'return=representation'
          },
          body: JSON.stringify({
            activationcode: verificationCode,           // Web uses 'activationcode'
            verification_code_expires: new Date(expirationTime).toISOString(),
            updated_at: new Date().toISOString()
          }),
        });

        if (updateCodeResponse.ok) {
          console.log('✅ Verification code stored in users table');
        } else {
          console.log('❌ Failed to store verification code');
        }
      } catch (dbError) {
        console.log('❌ Database error storing verification code:', dbError);
      }

      console.log('');
      console.log('🎯 ================================');
      console.log('🔢 VERIFICATION CODE:', verificationCode);
      console.log('📧 For email:', email);
      console.log('⏰ Valid for 10 minutes');
      console.log('🎯 ================================');
      console.log('');

      // Send email using dynamic email server discovery
      try {
        console.log('📧 Sending custom verification code via email...');
        
        // Get all possible email server URLs with priority order
        const priorityUrls = [
          'http://************:3001/send-verification',  // Your working Wi-Fi IP
          'http://************:3001/send-verification',  // Your working Ethernet IP
          'http://********:3001/send-verification',      // Android emulator bridge
          'http://localhost:3001/send-verification',     // Localhost
        ];
        
        const allUrls = [...priorityUrls, ...getEmailServerUrls()];
        let emailSent = false;
        
        // Try to find and use working email server
        for (const url of allUrls) {
          try {
            console.log(`🔄 Trying to connect to: ${url}`);
            
            // Create a promise with timeout
            const timeoutPromise = new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Timeout')), 3000)
            );
            
            const fetchPromise = fetch(url, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                email: email,
                verificationCode: verificationCode
              }),
            });

            const emailResponse = await Promise.race([fetchPromise, timeoutPromise]);

            console.log(`📊 Response status for ${url}:`, emailResponse.status);
            
            if (emailResponse.ok) {
              try {
                const result = await emailResponse.json();
                console.log('✅ Email sent successfully via:', url);
                console.log('📧 Response:', result);
                emailSent = true;
                break;
              } catch (jsonError) {
                console.log('⚠️ Response OK but JSON parse failed:', jsonError.message);
                // Still consider it successful if status is OK
                console.log('✅ Email likely sent successfully via:', url);
                emailSent = true;
                break;
              }
            } else {
              console.log(`❌ Failed with ${url}:`, emailResponse.status);
              const errorText = await emailResponse.text().catch(() => 'No error text');
              console.log(`❌ Error details:`, errorText);
            }
          } catch (urlError) {
            console.log(`❌ Connection failed to ${url}:`, urlError.message);
            continue;
          }
        }

        if (emailSent) {
          console.log('✅ Custom verification email sent successfully to:', email);
          console.log('📧 User should receive the 6-digit code:', verificationCode);
        } else {
          console.log('❌ All email server connections failed');
          console.log('🔢 Verification code available in console:', verificationCode);
          console.log('💡 Make sure your email server is running: node email-server.js');
          console.log('💡 The app will automatically find the server when it\'s available');
        }
      } catch (emailError) {
        console.log('❌ Email sending error:', emailError);
        console.log('🔢 Verification code available in console:', verificationCode);
      }
    }

    return { data: data[0], error: null };
  } catch (error) {
    console.error('Create user error:', error);
    return { data: null, error };
  }
};

export const signIn = async (email, password) => {
  try {
    console.log('Finding user in users table:', email);

    // Find user in users table using web schema
    const response = await fetch(`${supabaseUrl}/rest/v1/users?email=eq.${email}&password=eq.${password}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error('Failed to find user');
    }

    if (!data || data.length === 0) {
      throw new Error('Invalid email or password');
    }

    const user = data[0];
    console.log('User found in users table:', user);

    return { data: { user }, error: null };
  } catch (error) {
    console.error('Find user error:', error);
    return { data: null, error };
  }
};

export const signOut = async () => {
  try {
    console.log('Signing out user');
    return { error: null };
  } catch (error) {
    console.error('Sign out error:', error);
    return { error };
  }
};

export default { signUp, signIn, signOut };
