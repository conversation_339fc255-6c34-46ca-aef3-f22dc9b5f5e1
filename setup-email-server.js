#!/usr/bin/env node

// Setup script for MentalEase email server
// This script helps configure the email server on any computer

const os = require('os');
const fs = require('fs');
const path = require('path');

console.log('🚀 MentalEase Email Server Setup');
console.log('================================\n');

// Get network interfaces
function getNetworkInterfaces() {
  const interfaces = os.networkInterfaces();
  const addresses = [];
  
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      // Skip internal and non-IPv4 addresses
      if (interface.family === 'IPv4' && !interface.internal) {
        addresses.push({
          name: name,
          address: interface.address,
          netmask: interface.netmask
        });
      }
    }
  }
  
  return addresses;
}

// Display network information
function displayNetworkInfo() {
  console.log('📡 Network Configuration:');
  console.log('========================');
  
  const interfaces = getNetworkInterfaces();
  
  if (interfaces.length === 0) {
    console.log('❌ No network interfaces found');
    return null;
  }
  
  interfaces.forEach((iface, index) => {
    console.log(`${index + 1}. ${iface.name}: ${iface.address}`);
  });
  
  console.log('\n💡 The React Native app will automatically try to connect to these addresses.');
  console.log('💡 Make sure your email server is running on port 3001.\n');
  
  return interfaces;
}

// Check if email server configuration exists
function checkEmailServerConfig() {
  console.log('📧 Email Server Configuration:');
  console.log('==============================');
  
  const emailServerPath = path.join(__dirname, 'email-server.js');
  
  if (!fs.existsSync(emailServerPath)) {
    console.log('❌ email-server.js not found');
    return false;
  }
  
  console.log('✅ email-server.js found');
  
  // Read and check configuration
  try {
    const content = fs.readFileSync(emailServerPath, 'utf8');
    
    // Check for Gmail configuration
    if (content.includes('smtp.gmail.com')) {
      console.log('✅ Gmail SMTP configuration detected');
    } else {
      console.log('⚠️  Gmail SMTP configuration not found');
    }
    
    // Check for CORS
    if (content.includes('cors')) {
      console.log('✅ CORS enabled');
    } else {
      console.log('⚠️  CORS not enabled');
    }
    
    return true;
  } catch (error) {
    console.log('❌ Error reading email server configuration:', error.message);
    return false;
  }
}

// Generate instructions
function generateInstructions() {
  console.log('\n📋 Setup Instructions:');
  console.log('======================');
  
  console.log('1. 📧 Configure Gmail App Password:');
  console.log('   - Go to Google Account settings');
  console.log('   - Enable 2-factor authentication');
  console.log('   - Generate an App Password for "Mail"');
  console.log('   - Update email-server.js with your credentials\n');
  
  console.log('2. 🚀 Start the email server:');
  console.log('   npm install express nodemailer cors');
  console.log('   node email-server.js\n');
  
  console.log('3. 📱 Run your React Native app:');
  console.log('   npm start');
  console.log('   # or');
  console.log('   npx expo start\n');
  
  console.log('4. ✅ Test the system:');
  console.log('   - Create a new account in the app');
  console.log('   - Check console logs for email server connection');
  console.log('   - Check your email for verification code\n');
}

// Create a portable configuration file
function createPortableConfig() {
  const interfaces = getNetworkInterfaces();
  
  const config = {
    timestamp: new Date().toISOString(),
    hostname: os.hostname(),
    platform: os.platform(),
    networkInterfaces: interfaces,
    emailServerPort: 3001,
    instructions: {
      setup: [
        'Install dependencies: npm install express nodemailer cors',
        'Configure Gmail credentials in email-server.js',
        'Start email server: node email-server.js',
        'Run React Native app: npm start'
      ],
      troubleshooting: [
        'Make sure email server is running on port 3001',
        'Check firewall settings',
        'Verify Gmail app password is correct',
        'Ensure devices are on same network'
      ]
    }
  };
  
  const configPath = path.join(__dirname, 'email-server-config.json');
  
  try {
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
    console.log(`📄 Configuration saved to: ${configPath}`);
  } catch (error) {
    console.log('❌ Error saving configuration:', error.message);
  }
}

// Main setup function
function main() {
  console.log(`🖥️  Computer: ${os.hostname()}`);
  console.log(`🔧 Platform: ${os.platform()}`);
  console.log(`👤 User: ${os.userInfo().username}\n`);
  
  // Display network information
  const interfaces = displayNetworkInfo();
  
  // Check email server configuration
  checkEmailServerConfig();
  
  // Generate instructions
  generateInstructions();
  
  // Create portable configuration
  createPortableConfig();
  
  console.log('🎉 Setup complete!');
  console.log('💡 The app will automatically discover the email server when running.');
  console.log('💡 Share this entire folder to use on other computers.\n');
}

// Run setup
main();
