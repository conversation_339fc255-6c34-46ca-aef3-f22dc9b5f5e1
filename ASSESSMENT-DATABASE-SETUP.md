# 🧠 Assessment Database Integration Setup

## 🚨 **Problem Fixed:**
The DASS assessment was only saving to local device storage (AsyncStorage), which meant:
- ❌ **Second attempts overwrote first attempts**
- ❌ **No tracking of multiple attempts**
- ❌ **Results didn't persist across devices**
- ❌ **No "latest result" concept**

## ✅ **Solution Implemented:**
- ✅ **Database storage** - All results saved to Supabase
- ✅ **Multiple attempts tracking** - Each attempt gets its own record
- ✅ **Latest result retrieval** - Always shows most recent attempt
- ✅ **Attempt counting** - Shows how many times user took each assessment
- ✅ **Backup to AsyncStorage** - Fallback for offline scenarios

---

## 🛠️ **Setup Instructions:**

### **1. Create Database Table**
1. Open your **Supabase Dashboard**
2. Go to **SQL Editor**
3. Copy and paste the contents of `database/assessment_results_table.sql`
4. Click **Run** to create the table

### **2. Verify Table Creation**
```sql
-- Check if table exists
SELECT * FROM assessment_results LIMIT 1;

-- Should show empty table with columns:
-- id, user_email, assessment_type, results, scores, completed_at, created_at, updated_at
```

### **3. Test the System**
1. **Take DASS assessment** (first time)
2. **Complete and save** the results
3. **Take DASS assessment again** (second time)
4. **Complete and save** the results
5. **View Results** - Should show the **latest (second) attempt**

---

## 📊 **How It Works:**

### **When User Completes Assessment:**
```javascript
// 1. Save to database with timestamp
const assessmentData = {
  user_email: "<EMAIL>",
  assessment_type: "dass",
  results: JSON.stringify(results),
  scores: JSON.stringify({depression: 12, anxiety: 8, stress: 15}),
  completed_at: "2024-01-15T10:30:00Z"
};

// 2. Also backup to AsyncStorage
await AsyncStorage.setItem('dass_saved_results', JSON.stringify(results));
```

### **When User Views Results:**
```javascript
// 1. Try database first (gets latest attempt)
const response = await fetch(
  `${supabaseUrl}/rest/v1/assessment_results?user_email=eq.${email}&assessment_type=eq.dass&order=completed_at.desc&limit=1`
);

// 2. Fallback to AsyncStorage if database fails
if (!databaseResults) {
  const localResults = await AsyncStorage.getItem('dass_saved_results');
}
```

---

## 🔍 **Database Schema:**

```sql
assessment_results (
  id                SERIAL PRIMARY KEY,
  user_email        VARCHAR(255) NOT NULL,
  assessment_type   VARCHAR(50) NOT NULL,    -- 'dass', 'pss', 'gad7', 'phq9'
  results           JSONB NOT NULL,          -- Complete assessment results
  scores            JSONB,                   -- Extracted scores for querying
  completed_at      TIMESTAMP WITH TIME ZONE,
  created_at        TIMESTAMP WITH TIME ZONE,
  updated_at        TIMESTAMP WITH TIME ZONE
)
```

### **Example Data:**
```json
{
  "id": 1,
  "user_email": "<EMAIL>",
  "assessment_type": "dass",
  "results": {
    "depression": {"score": 12, "level": "Mild"},
    "anxiety": {"score": 8, "level": "Normal"},
    "stress": {"score": 15, "level": "Moderate"}
  },
  "scores": {
    "depression": 12,
    "anxiety": 8,
    "stress": 15
  },
  "completed_at": "2024-01-15T10:30:00Z"
}
```

---

## 🧪 **Testing Scenarios:**

### **Scenario 1: Multiple DASS Attempts**
1. ✅ **First attempt**: Depression=10, Anxiety=5, Stress=12
2. ✅ **Second attempt**: Depression=8, Anxiety=3, Stress=9
3. ✅ **View Results**: Should show **second attempt** (latest)

### **Scenario 2: Different Assessment Types**
1. ✅ **Take DASS**: Results saved
2. ✅ **Take PSS**: Results saved separately
3. ✅ **View DASS**: Shows DASS results
4. ✅ **View PSS**: Shows PSS results

### **Scenario 3: Offline/Online Sync**
1. ✅ **Take assessment offline**: Saves to AsyncStorage
2. ✅ **Go online**: Next save attempts database first
3. ✅ **View results**: Tries database, falls back to AsyncStorage

---

## 🔧 **Useful Database Queries:**

### **Get Latest Result for User:**
```sql
SELECT * FROM assessment_results 
WHERE user_email = '<EMAIL>' 
AND assessment_type = 'dass' 
ORDER BY completed_at DESC 
LIMIT 1;
```

### **Count User's Attempts:**
```sql
SELECT assessment_type, COUNT(*) as attempts, MAX(completed_at) as latest
FROM assessment_results 
WHERE user_email = '<EMAIL>' 
GROUP BY assessment_type;
```

### **Get Score Trends:**
```sql
SELECT completed_at, 
       scores->>'depression' as depression_score,
       scores->>'anxiety' as anxiety_score,
       scores->>'stress' as stress_score
FROM assessment_results 
WHERE user_email = '<EMAIL>' 
AND assessment_type = 'dass'
ORDER BY completed_at;
```

---

## 🎯 **Expected Behavior After Setup:**

1. **First DASS attempt**: Saves to database ✅
2. **Second DASS attempt**: Saves as new record ✅
3. **View Results**: Shows latest attempt ✅
4. **Assessment list**: Shows "Last completed: [date]" ✅
5. **Multiple assessments**: Each type tracked separately ✅

**Your assessment results will now properly track multiple attempts and always show the latest one!** 🎉
