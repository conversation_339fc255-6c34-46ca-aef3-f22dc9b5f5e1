-- Create schedules table for psychometrician availability
-- Run this in your Supabase SQL Editor

-- Step 1: Create the schedules table
CREATE TABLE IF NOT EXISTS schedules (
    id SERIAL PRIMARY KEY,
    psychometrician_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    day_of_week VARCHAR(10) NOT NULL CHECK (day_of_week IN ('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')),
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure no overlapping schedules for same psychometrician on same day
    UNIQUE(psychometrician_id, day_of_week, start_time),
    
    -- Ensure end_time is after start_time
    CHECK (end_time > start_time)
);

-- Step 2: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_schedules_psychometrician_id ON schedules(psychometrician_id);
CREATE INDEX IF NOT EXISTS idx_schedules_day_of_week ON schedules(day_of_week);
CREATE INDEX IF NOT EXISTS idx_schedules_psychometrician_day ON schedules(psychometrician_id, day_of_week);

-- Step 3: Create function to auto-update updated_at timestamp
CREATE OR REPLACE FUNCTION update_schedules_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Step 4: Create trigger for auto-updating updated_at
DROP TRIGGER IF EXISTS update_schedules_updated_at ON schedules;
CREATE TRIGGER update_schedules_updated_at
    BEFORE UPDATE ON schedules
    FOR EACH ROW
    EXECUTE FUNCTION update_schedules_updated_at();

-- Step 5: Add comments for documentation
COMMENT ON TABLE schedules IS 'Weekly schedules for psychometricians availability';
COMMENT ON COLUMN schedules.psychometrician_id IS 'Psychometrician user ID (references users.id where role=psychometrician)';
COMMENT ON COLUMN schedules.day_of_week IS 'Day of the week (monday, tuesday, etc.)';
COMMENT ON COLUMN schedules.start_time IS 'Start time for availability (HH:MM:SS)';
COMMENT ON COLUMN schedules.end_time IS 'End time for availability (HH:MM:SS)';
COMMENT ON COLUMN schedules.is_available IS 'Whether this schedule slot is active';

-- Step 6: Grant necessary permissions
GRANT ALL ON schedules TO authenticated;
GRANT USAGE ON SEQUENCE schedules_id_seq TO authenticated;

-- Step 7: Insert sample schedules for existing psychometricians
-- This creates default Monday-Friday 9AM-5PM schedules for all psychometricians
INSERT INTO schedules (psychometrician_id, day_of_week, start_time, end_time)
SELECT 
    u.id as psychometrician_id,
    day_name,
    '09:00:00'::TIME as start_time,
    '17:00:00'::TIME as end_time
FROM users u
CROSS JOIN (
    VALUES 
        ('monday'),
        ('tuesday'), 
        ('wednesday'),
        ('thursday'),
        ('friday')
) AS days(day_name)
WHERE u.role = 'psychometrician'
ON CONFLICT (psychometrician_id, day_of_week, start_time) DO NOTHING;

-- Step 8: Verification query
SELECT 
    s.id,
    u.name as psychometrician_name,
    u.email,
    s.day_of_week,
    s.start_time,
    s.end_time,
    s.is_available
FROM schedules s
JOIN users u ON s.psychometrician_id = u.id
WHERE u.role = 'psychometrician'
ORDER BY u.name, 
    CASE s.day_of_week 
        WHEN 'monday' THEN 1
        WHEN 'tuesday' THEN 2
        WHEN 'wednesday' THEN 3
        WHEN 'thursday' THEN 4
        WHEN 'friday' THEN 5
        WHEN 'saturday' THEN 6
        WHEN 'sunday' THEN 7
    END,
    s.start_time;
