import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Platform,
  Alert,
  ActivityIndicator,
  TextInput,
  Modal,
  TouchableWithoutFeedback,
  Keyboard
} from 'react-native';
import { useRouter } from 'expo-router';
import { useUser } from '../../context/UserContext';
import { Ionicons } from '@expo/vector-icons';

// Type definitions
interface FormData {
  firstName: string;
  middleName: string;
  lastName: string;
  age: string;
  gender: string;
  civilStatus: string;
  phone: string;
  address: string;
  birthdate: string;
  birthplace: string;
  religion: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  age?: string;
  gender?: string;
  civilStatus?: string;
  phone?: string;
  address?: string;
  birthdate?: string;
  birthplace?: string;
}

const EditProfile = () => {
  const router = useRouter();
  const { userData, updateUserData } = useUser();
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Options for dropdowns
  const genderOptions = ['Male', 'Female', 'Non-binary', 'Prefer not to say', 'Other'];
  const civilStatusOptions = ['Single', 'Married', 'Divorced', 'Widowed', 'Separated', 'Other'];

  // Form state management
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    middleName: '',
    lastName: '',
    age: '',
    gender: '',
    civilStatus: '',
    phone: '',
    birthdate: '',
    birthplace: '',
    religion: '',
    address: ''
  });

  // UI state management
  const [errors, setErrors] = useState<FormErrors>({});
  const [showGenderModal, setShowGenderModal] = useState<boolean>(false);
  const [showCivilStatusModal, setShowCivilStatusModal] = useState<boolean>(false);

  // Form field update handler
  const updateField = useCallback((field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field when user types
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  }, [errors]);

  // Fetch user data from database when component loads
  useEffect(() => {
    const fetchUserData = async () => {
      if (!userData.email) {
        setIsLoading(false);
        return;
      }

      try {
        console.log('Fetching user profile data for:', userData.email);

        const response = await fetch(`${process.env.EXPO_PUBLIC_SUPABASE_URL}/rest/v1/users?email=eq.${userData.email}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'apikey': process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
          },
        });

        if (response.ok) {
          const users = await response.json();
          if (users && users.length > 0) {
            const user = users[0];

            // Update user context with database data
            const updatedUserData = {
              ...userData,
              firstName: user.name || userData.firstName,                    // Web uses 'name' for first name
              middleName: user.middle_name || userData.middleName,
              lastName: user.last_name || userData.lastName,
              age: user.age || userData.age,
              gender: user.gender || userData.gender,
              civilStatus: user.civil_status || userData.civilStatus,
              phone: user.contactnumber || userData.phone,                   // Web uses 'contactnumber'
              address: user.address || userData.address,
              birthdate: user.birthdate || userData.birthdate,
              birthplace: user.birthplace || userData.birthplace,
              religion: user.religion || userData.religion,
              controlNumber: user.control_number || userData.controlNumber,
              emailVerified: user.status || userData.emailVerified,          // Web uses 'status'
              hasCompletedProfile: user.has_completed_profile || userData.hasCompletedProfile,
              username: user.username || userData.username
            };

            updateUserData(updatedUserData);

            // Initialize form data with fetched user data
            setFormData({
              firstName: user.name || '',
              middleName: user.middle_name || '',
              lastName: user.last_name || '',
              age: user.age?.toString() || '',
              gender: user.gender || '',
              civilStatus: user.civil_status || '',
              phone: user.contactnumber || '',
              address: user.address || '',
              birthdate: user.birthdate || '',
              birthplace: user.birthplace || '',
              religion: user.religion || ''
            });

            console.log('✅ User profile data updated from database');
          }
        } else {
          console.log('❌ Failed to fetch user profile data');
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, [userData.email]);

  // Form validation
  const validateForm = useCallback(() => {
    const requiredFields: Array<{ key: keyof FormData; label: string }> = [
      { key: 'firstName', label: 'First name' },
      { key: 'lastName', label: 'Last name' },
      { key: 'age', label: 'Age' },
      { key: 'gender', label: 'Gender' },
      { key: 'civilStatus', label: 'Civil status' },
      { key: 'phone', label: 'Phone number' },
      { key: 'birthdate', label: 'Birthdate' },
      { key: 'birthplace', label: 'Birthplace' },
      { key: 'address', label: 'Address' }
    ];

    const newErrors: FormErrors = {};
    let isValid = true;

    // Check required fields
    requiredFields.forEach(field => {
      if (!formData[field.key] || !formData[field.key].trim()) {
        newErrors[field.key] = `${field.label} is required`;
        isValid = false;
      }
    });

    // Additional validation for age
    if (formData.age && (isNaN(Number(formData.age)) || parseInt(formData.age) <= 0)) {
      newErrors.age = 'Please enter a valid age';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  }, [formData]);

  // Handle edit mode toggle
  const handleEditInformation = () => {
    setIsEditing(true);
  };

  // Handle cancel editing
  const handleCancelEdit = () => {
    // Reset form data to original user data
    setFormData({
      firstName: userData.firstName || '',
      middleName: userData.middleName || '',
      lastName: userData.lastName || '',
      age: userData.age?.toString() || '',
      gender: userData.gender || '',
      civilStatus: userData.civilStatus || '',
      phone: userData.phone || '',
      address: userData.address || '',
      birthdate: userData.birthdate || '',
      birthplace: userData.birthplace || '',
      religion: userData.religion || ''
    });
    setErrors({});
    setIsEditing(false);
  };

  // Form submission handler
  const handleSubmit = useCallback(async () => {
    if (validateForm()) {
      setIsSubmitting(true);

      try {
        // Prepare user data for database (using web column names)
        const dbUserData = {
          name: formData.firstName.trim(),                // Web uses 'name' for first name
          middle_name: formData.middleName.trim(),
          last_name: formData.lastName.trim(),
          age: parseInt(formData.age),
          gender: formData.gender.trim(),
          civil_status: formData.civilStatus.trim(),
          contactnumber: formData.phone.trim(),           // Web uses 'contactnumber'
          birthdate: formData.birthdate.trim(),
          birthplace: formData.birthplace.trim(),
          religion: formData.religion.trim() || 'Not specified',
          address: formData.address.trim(),
          updated_at: new Date().toISOString()            // Add updated_at timestamp
        };

        // Update user in database
        const updateResponse = await fetch(`${process.env.EXPO_PUBLIC_SUPABASE_URL}/rest/v1/users?email=eq.${userData.email}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'apikey': process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
            'Prefer': 'return=representation'
          },
          body: JSON.stringify(dbUserData),
        });

        if (!updateResponse.ok) {
          throw new Error('Failed to update user profile in database');
        }

        console.log('✅ User profile updated in database with timestamp');

        // Prepare user data for context (using camelCase)
        const contextUserData = {
          firstName: formData.firstName.trim(),
          middleName: formData.middleName.trim(),
          lastName: formData.lastName.trim(),
          age: parseInt(formData.age),
          gender: formData.gender.trim(),
          civilStatus: formData.civilStatus.trim(),
          phone: formData.phone.trim(),
          birthdate: formData.birthdate.trim(),
          birthplace: formData.birthplace.trim(),
          religion: formData.religion.trim() || 'Not specified',
          address: formData.address.trim()
        };

        // Update user data in context
        updateUserData(contextUserData);

        setIsSubmitting(false);
        setIsEditing(false);

        Alert.alert(
          "Profile Updated",
          "Your profile information has been successfully updated.",
          [{ text: "OK" }]
        );

      } catch (error) {
        console.error('Error updating user profile:', error);
        Alert.alert(
          "Update Failed",
          "There was an error updating your profile. Please try again.",
          [{ text: "OK" }]
        );
        setIsSubmitting(false);
      }
    } else {
      Alert.alert(
        "Incomplete Information",
        "Please fill in all required fields correctly.",
        [{ text: "OK" }]
      );
    }
  }, [formData, validateForm, updateUserData, userData.email]);

  // Show loading state while fetching data
  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <StatusBar backgroundColor="#66948a" barStyle="light-content" />
          <SafeAreaView>
            <View style={styles.headerContent}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => router.back()}
              >
                <Ionicons name="chevron-back" size={24} color="#FFFFFF" />
                <Text style={styles.backButtonText}>Back</Text>
              </TouchableOpacity>
              <Text style={styles.headerTitle}>Edit Profile</Text>
              <View style={styles.headerSpacer} />
            </View>
          </SafeAreaView>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#66948a" />
          <Text style={styles.loadingText}>Loading profile data...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <StatusBar backgroundColor="#66948a" barStyle="light-content" />
        <SafeAreaView>
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Ionicons name="chevron-back" size={24} color="#FFFFFF" />
              <Text style={styles.backButtonText}>Back</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Edit Profile</Text>
            <View style={styles.headerSpacer} />
          </View>
        </SafeAreaView>
      </View>

      <View style={styles.content}>
        {!isEditing ? (
          // Read-only view
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            {/* Personal Information Section */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Personal Information</Text>
              <Text style={styles.subsectionTitle}>Basic Information</Text>
              <View style={styles.infoContainer}>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>First Name</Text>
                  <Text style={styles.infoValue}>{userData.firstName || 'Not provided'}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Middle Name</Text>
                  <Text style={styles.infoValue}>{userData.middleName || 'N/A'}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Last Name</Text>
                  <Text style={styles.infoValue}>{userData.lastName || 'Not provided'}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Age</Text>
                  <Text style={styles.infoValue}>{userData.age?.toString() || 'Not provided'}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Gender</Text>
                  <Text style={styles.infoValue}>{userData.gender || 'Not provided'}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Civil Status</Text>
                  <Text style={styles.infoValue}>{userData.civilStatus || 'Not provided'}</Text>
                </View>
              </View>
            </View>

            {/* Contact Information Section */}
            <View style={styles.section}>
              <Text style={styles.subsectionTitle}>Contact Information</Text>
              <View style={styles.infoContainer}>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Email</Text>
                  <Text style={styles.infoValue}>{userData.email || 'Not provided'}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Phone</Text>
                  <Text style={styles.infoValue}>{userData.phone || 'Not provided'}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Address</Text>
                  <Text style={styles.infoValue}>{userData.address || 'Not provided'}</Text>
                </View>
              </View>
            </View>

            {/* Background Information Section */}
            <View style={styles.section}>
              <Text style={styles.subsectionTitle}>Background Information</Text>
              <View style={styles.infoContainer}>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Birthdate</Text>
                  <Text style={styles.infoValue}>{userData.birthdate || 'Not provided'}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Birthplace</Text>
                  <Text style={styles.infoValue}>{userData.birthplace || 'Not provided'}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Religion</Text>
                  <Text style={styles.infoValue}>{userData.religion || 'Not provided'}</Text>
                </View>
              </View>
            </View>

            {/* Edit Information Button */}
            <TouchableOpacity
              style={styles.editButton}
              onPress={handleEditInformation}
            >
              <Text style={styles.editButtonText}>Edit Information</Text>
            </TouchableOpacity>
          </ScrollView>
        ) : (
          // Editable form view
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <ScrollView
              contentContainerStyle={styles.scrollContent}
              showsVerticalScrollIndicator={false}
            >
              <Text style={styles.subtitle}>
                Update your personal information below.
                Fields marked with * are required.
              </Text>

              <View style={styles.formSection}>
                <Text style={styles.sectionTitle}>Basic Information</Text>

                <Text style={styles.inputLabel}>First Name *</Text>
                <TextInput
                  style={[styles.input, errors.firstName && styles.inputError]}
                  placeholder="Enter your first name"
                  value={formData.firstName}
                  onChangeText={(text) => updateField('firstName', text)}
                  autoCapitalize="words"
                />
                {errors.firstName && <Text style={styles.errorText}>{errors.firstName}</Text>}

                <Text style={styles.inputLabel}>Middle Name</Text>
                <TextInput
                  style={styles.input}
                  placeholder="Enter your middle name (optional)"
                  value={formData.middleName}
                  onChangeText={(text) => updateField('middleName', text)}
                  autoCapitalize="words"
                />

                <Text style={styles.inputLabel}>Last Name *</Text>
                <TextInput
                  style={[styles.input, errors.lastName && styles.inputError]}
                  placeholder="Enter your last name"
                  value={formData.lastName}
                  onChangeText={(text) => updateField('lastName', text)}
                  autoCapitalize="words"
                />
                {errors.lastName && <Text style={styles.errorText}>{errors.lastName}</Text>}

                <Text style={styles.inputLabel}>Age *</Text>
                <TextInput
                  style={[styles.input, errors.age && styles.inputError]}
                  placeholder="Enter your age"
                  value={formData.age}
                  onChangeText={(text) => updateField('age', text)}
                  keyboardType="numeric"
                  maxLength={3}
                />
                {errors.age && <Text style={styles.errorText}>{errors.age}</Text>}

                <Text style={styles.inputLabel}>Gender *</Text>
                <TouchableOpacity
                  style={[styles.input, styles.dropdownInput, errors.gender && styles.inputError]}
                  onPress={() => setShowGenderModal(true)}
                  activeOpacity={0.7}
                >
                  <Text style={formData.gender ? styles.dropdownText : styles.placeholderText}>
                    {formData.gender || "Select your gender"}
                  </Text>
                  <Text style={styles.dropdownIcon}>▼</Text>
                </TouchableOpacity>
                {errors.gender && <Text style={styles.errorText}>{errors.gender}</Text>}

                <Text style={styles.inputLabel}>Civil Status *</Text>
                <TouchableOpacity
                  style={[styles.input, styles.dropdownInput, errors.civilStatus && styles.inputError]}
                  onPress={() => setShowCivilStatusModal(true)}
                  activeOpacity={0.7}
                >
                  <Text style={formData.civilStatus ? styles.dropdownText : styles.placeholderText}>
                    {formData.civilStatus || "Select your civil status"}
                  </Text>
                  <Text style={styles.dropdownIcon}>▼</Text>
                </TouchableOpacity>
                {errors.civilStatus && <Text style={styles.errorText}>{errors.civilStatus}</Text>}
              </View>

              <View style={styles.formSection}>
                <Text style={styles.sectionTitle}>Contact Information</Text>

                <Text style={styles.inputLabel}>Phone Number *</Text>
                <TextInput
                  style={[styles.input, errors.phone && styles.inputError]}
                  placeholder="Enter your phone number"
                  value={formData.phone}
                  onChangeText={(text) => updateField('phone', text)}
                  keyboardType="phone-pad"
                />
                {errors.phone && <Text style={styles.errorText}>{errors.phone}</Text>}

                <Text style={styles.inputLabel}>Address *</Text>
                <TextInput
                  style={[styles.input, styles.multilineInput, errors.address && styles.inputError]}
                  placeholder="Enter your complete address"
                  value={formData.address}
                  onChangeText={(text) => updateField('address', text)}
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                />
                {errors.address && <Text style={styles.errorText}>{errors.address}</Text>}
              </View>

              <View style={styles.formSection}>
                <Text style={styles.sectionTitle}>Background Information</Text>

                <Text style={styles.inputLabel}>Birthdate *</Text>
                <TextInput
                  style={[styles.input, errors.birthdate && styles.inputError]}
                  placeholder="YYYY-MM-DD"
                  value={formData.birthdate}
                  onChangeText={(text) => updateField('birthdate', text)}
                  keyboardType={Platform.OS === 'ios' ? 'default' : 'numeric'}
                />
                {errors.birthdate && <Text style={styles.errorText}>{errors.birthdate}</Text>}

                <Text style={styles.inputLabel}>Birthplace *</Text>
                <TextInput
                  style={[styles.input, errors.birthplace && styles.inputError]}
                  placeholder="Enter your birthplace"
                  value={formData.birthplace}
                  onChangeText={(text) => updateField('birthplace', text)}
                  autoCapitalize="words"
                />
                {errors.birthplace && <Text style={styles.errorText}>{errors.birthplace}</Text>}

                <Text style={styles.inputLabel}>Religion</Text>
                <TextInput
                  style={styles.input}
                  placeholder="Enter your religion (optional)"
                  value={formData.religion}
                  onChangeText={(text) => updateField('religion', text)}
                  autoCapitalize="words"
                />
              </View>

              {/* Action Buttons */}
              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={handleCancelEdit}
                  activeOpacity={0.8}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.saveButton, isSubmitting && styles.saveButtonDisabled]}
                  onPress={handleSubmit}
                  disabled={isSubmitting}
                  activeOpacity={0.8}
                >
                  {isSubmitting ? (
                    <ActivityIndicator color="#FFFFFF" size="small" />
                  ) : (
                    <Text style={styles.saveButtonText}>Save Changes</Text>
                  )}
                </TouchableOpacity>
              </View>
            </ScrollView>
          </TouchableWithoutFeedback>
        )}
      </View>

      {/* Gender Selection Modal */}
      <Modal
        visible={showGenderModal}
        transparent={true}
        animationType="fade"
        statusBarTranslucent={true}
      >
        <TouchableWithoutFeedback onPress={() => setShowGenderModal(false)}>
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>Select Gender</Text>

              <ScrollView style={styles.optionsList}>
                {genderOptions.map((option, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.optionItem,
                      formData.gender === option && styles.selectedOption
                    ]}
                    onPress={() => {
                      updateField('gender', option);
                      setShowGenderModal(false);
                    }}
                    activeOpacity={0.7}
                  >
                    <Text style={[
                      styles.optionText,
                      formData.gender === option && styles.selectedOptionText
                    ]}>
                      {option}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>

              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowGenderModal(false)}
                activeOpacity={0.7}
              >
                <Text style={styles.closeButtonText}>Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>

      {/* Civil Status Selection Modal */}
      <Modal
        visible={showCivilStatusModal}
        transparent={true}
        animationType="fade"
        statusBarTranslucent={true}
      >
        <TouchableWithoutFeedback onPress={() => setShowCivilStatusModal(false)}>
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>Select Civil Status</Text>

              <ScrollView style={styles.optionsList}>
                {civilStatusOptions.map((option, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.optionItem,
                      formData.civilStatus === option && styles.selectedOption
                    ]}
                    onPress={() => {
                      updateField('civilStatus', option);
                      setShowCivilStatusModal(false);
                    }}
                    activeOpacity={0.7}
                  >
                    <Text style={[
                      styles.optionText,
                      formData.civilStatus === option && styles.selectedOptionText
                    ]}>
                      {option}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>

              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowCivilStatusModal(false)}
                activeOpacity={0.7}
              >
                <Text style={styles.closeButtonText}>Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

export default EditProfile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    backgroundColor: '#66948a',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 8,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '500',
    marginLeft: 4,
  },
  headerTitle: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  headerSpacer: {
    width: 60,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
    marginTop: 16,
    paddingHorizontal: 16,
  },
  subsectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
    marginTop: 8,
    paddingHorizontal: 16,
  },
  infoContainer: {
    backgroundColor: '#FFFFFF',
    padding: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  infoLabel: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '400',
    flex: 1,
  },
  infoValue: {
    fontSize: 14,
    color: '#333333',
    fontWeight: '500',
    flex: 2,
    textAlign: 'right',
  },
  editButton: {
    backgroundColor: '#66948a',
    borderRadius: 25,
    paddingVertical: 14,
    alignItems: 'center',
    marginTop: 24,
    marginHorizontal: 20,
  },
  editButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  // Form styles
  subtitle: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginHorizontal: 20,
    marginVertical: 16,
    lineHeight: 20,
  },
  formSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    padding: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 8,
    marginTop: 12,
  },
  input: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#FFFFFF',
    color: '#333333',
  },
  inputError: {
    borderColor: '#FF6B6B',
  },
  multilineInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  dropdownInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownText: {
    fontSize: 16,
    color: '#333333',
  },
  placeholderText: {
    fontSize: 16,
    color: '#999999',
  },
  dropdownIcon: {
    fontSize: 12,
    color: '#666666',
  },
  errorText: {
    fontSize: 12,
    color: '#FF6B6B',
    marginTop: 4,
    marginLeft: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 20,
    marginTop: 24,
    marginBottom: 40,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#F5F5F5',
    borderRadius: 25,
    paddingVertical: 14,
    alignItems: 'center',
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  cancelButtonText: {
    color: '#666666',
    fontSize: 16,
    fontWeight: '500',
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#66948a',
    borderRadius: 25,
    paddingVertical: 14,
    alignItems: 'center',
    marginLeft: 10,
  },
  saveButtonDisabled: {
    backgroundColor: '#B0B0B0',
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxHeight: '70%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    textAlign: 'center',
    marginBottom: 20,
  },
  optionsList: {
    maxHeight: 300,
  },
  optionItem: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  selectedOption: {
    backgroundColor: '#E8F5E8',
  },
  optionText: {
    fontSize: 16,
    color: '#333333',
    textAlign: 'center',
  },
  selectedOptionText: {
    color: '#7BA05B',
    fontWeight: '500',
  },
  closeButton: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 16,
  },
  closeButtonText: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '500',
  },
});
