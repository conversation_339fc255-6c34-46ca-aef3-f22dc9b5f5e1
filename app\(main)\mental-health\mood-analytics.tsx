import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import StandardHeader from '../../components/StandardHeader';
import { getMoodEntries, formatDateForDB, calculateMoodStats } from '../../lib/mood-service';
import { useUser } from '../../context/UserContext';

const { width: screenWidth } = Dimensions.get('window');

const MoodAnalytics = () => {
  const [moodEntries, setMoodEntries] = useState([]);
  const [selectedPeriod, setSelectedPeriod] = useState('week'); // only week view
  const [currentDate, setCurrentDate] = useState(new Date());
  const [loading, setLoading] = useState(true);
  const { userData } = useUser();
  const scrollViewRef = useRef(null);
  const hasAutoScrolled = useRef(false);

  const moodOptions = [
    { emoji: '😔', label: 'Sad', value: 0, color: '#E74C3C' },
    { emoji: '😕', label: 'Meh', value: 1, color: '#F39C12' },
    { emoji: '😐', label: 'Okay', value: 2, color: '#F1C40F' },
    { emoji: '🙂', label: 'Good', value: 3, color: '#2ECC71' },
    { emoji: '😄', label: 'Great', value: 4, color: '#27AE60' }
  ];

  useEffect(() => {
    hasAutoScrolled.current = false; // Reset auto-scroll flag when data changes
    loadMoodData();
  }, [userData?.email]);

  const loadMoodData = async () => {
    if (!userData?.email) return;

    setLoading(true);
    try {
      // Load last 14 days of mood data, ensuring we include today
      const today = new Date();
      const endDate = formatDateForDB(new Date(today.getTime() + 24 * 60 * 60 * 1000)); // Tomorrow to ensure today is included
      const startDate = formatDateForDB(new Date(Date.now() - 14 * 24 * 60 * 60 * 1000));

      const entries = await getMoodEntries(userData.email, startDate, endDate);
      setMoodEntries(entries);
      console.log('✅ Loaded mood analytics data:', entries.length, 'entries for dates', startDate, 'to', endDate);
    } catch (error) {
      console.error('Error loading mood analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getChartData = () => {
    if (moodEntries.length === 0) {
      return [];
    }

    // Find the earliest mood entry date (user's mood tracking start date)
    const sortedEntries = [...moodEntries].sort((a, b) =>
      new Date(a.entry_date).getTime() - new Date(b.entry_date).getTime()
    );
    const firstEntryDate = new Date(sortedEntries[0].entry_date);
    const today = new Date();

    // Create a map of mood entries by date for quick lookup
    const moodMap = {};
    moodEntries.forEach(entry => {
      const entryDate = new Date(entry.entry_date);
      const dateKey = entryDate.toDateString();
      moodMap[dateKey] = entry.mood_value;
    });

    // Calculate date range based on selected period and user's tracking history
    let startDate: Date, endDate: Date;

    // Show last 14 days for better trend visibility, always including today
    const twoWeeksAgo = new Date(today.getTime() - 14 * 24 * 60 * 60 * 1000);
    startDate = firstEntryDate > twoWeeksAgo ? firstEntryDate : twoWeeksAgo;
    // Ensure we always include today's date
    endDate = new Date(today.getTime() + 24 * 60 * 60 * 1000); // Tomorrow to ensure today is included

    // Build data array from start date to end date
    const data = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      const dateKey = currentDate.toDateString();
      const moodValue = moodMap[dateKey];

      data.push({
        date: new Date(currentDate),
        dateKey: dateKey,
        mood: moodValue !== undefined ? moodValue : null,
        hasData: moodValue !== undefined,
        day: currentDate.getDate(),
        month: currentDate.getMonth(),
        year: currentDate.getFullYear()
      });

      currentDate.setDate(currentDate.getDate() + 1);
    }

    console.log('📊 Chart data generated:', data.length, 'days from', data[0]?.date?.toDateString(), 'to', data[data.length - 1]?.date?.toDateString());
    return data;
  };

  const renderChart = () => {
    const chartData = getChartData();
    const minChartWidth = screenWidth - 40;
    const dayWidth = 60; // Width per day for better spacing
    const chartWidth = Math.max(minChartWidth, chartData.length * dayWidth);
    const chartHeight = 200;
    const padding = 30;
    const plotWidth = chartWidth - (padding * 2);
    const plotHeight = chartHeight - (padding * 2);

    const validPoints = chartData.filter(point => point.hasData);

    if (validPoints.length === 0) {
      return (
        <View style={[styles.chartContainer, { height: chartHeight }]}>
          <Text style={styles.noDataText}>No mood data available for this period</Text>
        </View>
      );
    }

    return (
      <ScrollView
        ref={scrollViewRef}
        horizontal
        showsHorizontalScrollIndicator={false}
        style={[styles.chartScrollContainer, { height: chartHeight }]}
        contentContainerStyle={{ minWidth: chartWidth }}
        onContentSizeChange={(contentWidth) => {
          // Auto-scroll to end only once when content loads, if chart is wider than screen
          if (contentWidth > minChartWidth && !hasAutoScrolled.current) {
            hasAutoScrolled.current = true;
            setTimeout(() => {
              scrollViewRef.current?.scrollToEnd({ animated: false });
            }, 100);
          }
        }}
      >
        <View style={[styles.chartContainer, { width: chartWidth, height: chartHeight }]}>
          {/* Grid lines */}
          <View style={styles.chartGrid}>
          {[0, 1, 2, 3, 4].map(i => (
            <View
              key={`h-${i}`}
              style={[
                styles.gridLineHorizontal,
                { top: padding + (i / 4) * plotHeight }
              ]}
            />
          ))}
          {/* Vertical grid lines */}
          {chartData.map((_, i) => {
            const xRatio = chartData.length > 1 ? i / (chartData.length - 1) : 0;
            return (
              <View
                key={`v-${i}`}
                style={[
                  styles.gridLineVertical,
                  { left: padding + xRatio * plotWidth }
                ]}
              />
            );
          })}
        </View>

        {/* Y-axis labels */}
        <View style={styles.yAxisLabels}>
          {moodOptions.map((mood, index) => (
            <View
              key={index}
              style={[
                styles.yAxisLabel,
                { bottom: padding + (index / 4) * plotHeight - 10 }
              ]}
            >
              <Text style={styles.yAxisLabelText}>{mood.emoji}</Text>
            </View>
          ))}
        </View>

        {/* X-axis labels (dates) */}
        <View style={styles.xAxisLabels}>
          {chartData.map((point, index) => {
            // Show every day for better visibility
            const xRatio = chartData.length > 1 ? index / (chartData.length - 1) : 0;
            return (
              <View
                key={`x-${index}`}
                style={[
                  styles.xAxisLabel,
                  { left: padding + xRatio * plotWidth - 15 }
                ]}
              >
                <Text style={styles.xAxisLabelText}>
                  {point.date ? `${point.date.getMonth() + 1}/${point.date.getDate()}` : ''}
                </Text>
              </View>
            );
          })}
        </View>

        {/* Chart content */}
        <View style={styles.chartContent}>
          {/* Line segments */}
          {validPoints.length > 1 && validPoints.map((point, index) => {
            if (index === validPoints.length - 1) return null;

            const currentIndex = chartData.findIndex(d => d.dateKey === point.dateKey);
            const nextPoint = validPoints[index + 1];
            const nextIndex = chartData.findIndex(d => d.dateKey === nextPoint.dateKey);

            // Safety checks
            if (currentIndex === -1 || nextIndex === -1 || chartData.length <= 1) return null;

            const x1 = padding + (currentIndex / (chartData.length - 1)) * plotWidth;
            const y1 = padding + plotHeight - ((point.mood / 4) * plotHeight);
            const x2 = padding + (nextIndex / (chartData.length - 1)) * plotWidth;
            const y2 = padding + plotHeight - ((nextPoint.mood / 4) * plotHeight);
            
            const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
            const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;
            
            return (
              <View
                key={`line-${index}`}
                style={[
                  styles.chartLine,
                  {
                    left: x1,
                    top: y1,
                    width: length,
                    transform: [{ rotate: `${angle}deg` }]
                  }
                ]}
              />
            );
          })}

          {/* Data points */}
          {chartData.map((point, index) => {
            if (!point.hasData || point.mood === null || point.mood === undefined) return null;

            // Safety check for division by zero
            const xRatio = chartData.length > 1 ? index / (chartData.length - 1) : 0;
            const x = padding + xRatio * plotWidth;
            const y = padding + plotHeight - ((point.mood / 4) * plotHeight);

            // Safety check for mood value bounds
            const safeMoodValue = Math.max(0, Math.min(4, point.mood));
            const moodColor = moodOptions[safeMoodValue]?.color || '#66948a';
            
            return (
              <View
                key={`point-${index}`}
                style={[
                  styles.chartPoint,
                  {
                    left: x - 6,
                    top: y - 6,
                    backgroundColor: moodColor
                  }
                ]}
              />
            );
          })}
        </View>
        </View>
      </ScrollView>
    );
  };

  const getMoodStats = () => {
    const stats = calculateMoodStats(moodEntries);
    return {
      average: stats.averageMood,
      mostCommon: stats.mostCommonMood,
      trend: stats.trend,
      totalEntries: stats.totalEntries
    };
  };

  const stats = getMoodStats();

  return (
    <View style={styles.container}>
      <StandardHeader title="Mood Analytics" />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Period Selector - Week Only */}
        <View style={styles.periodSelector}>
          <TouchableOpacity
            style={[styles.periodButton, styles.periodButtonActive]}
          >
            <Text style={[styles.periodButtonText, styles.periodButtonTextActive]}>
              Week
            </Text>
          </TouchableOpacity>
        </View>

        {/* Chart */}
        <View style={styles.chartCard}>
          <Text style={styles.chartTitle}>Mood Trend</Text>
          {loading ? (
            <View style={[styles.chartContainer, { height: 200 }]}>
              <Text style={styles.loadingText}>Loading mood data...</Text>
            </View>
          ) : (
            renderChart()
          )}
        </View>

        {/* Statistics */}
        <View style={styles.statsCard}>
          <Text style={styles.statsTitle}>Statistics</Text>
          
          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{stats.totalEntries}</Text>
              <Text style={styles.statLabel}>Total Entries</Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{stats.average}</Text>
              <Text style={styles.statLabel}>Average Mood</Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {stats.mostCommon !== null ? moodOptions[stats.mostCommon]?.emoji : '—'}
              </Text>
              <Text style={styles.statLabel}>Most Common</Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={[
                styles.statValue,
                { color: stats.trend === 'improving' ? '#27AE60' : 
                         stats.trend === 'declining' ? '#E74C3C' : '#7F8C8D' }
              ]}>
                {stats.trend === 'improving' ? '↗️' : 
                 stats.trend === 'declining' ? '↘️' : '→'}
              </Text>
              <Text style={styles.statLabel}>Trend</Text>
            </View>
          </View>
        </View>

        {/* Insights */}
        <View style={styles.insightsCard}>
          <Text style={styles.insightsTitle}>Insights</Text>
          <View style={styles.insightsList}>
            {stats.totalEntries === 0 && (
              <Text style={styles.insightText}>
                Start tracking your daily mood to see personalized insights and patterns.
              </Text>
            )}
            
            {stats.totalEntries > 0 && stats.trend === 'improving' && (
              <Text style={styles.insightText}>
                🎉 Great news! Your mood has been trending upward recently. Keep up the positive momentum!
              </Text>
            )}
            
            {stats.totalEntries > 0 && stats.trend === 'declining' && (
              <Text style={styles.insightText}>
                💙 Your mood has been lower lately. Consider reaching out to a mental health professional or trying some self-care activities.
              </Text>
            )}
            
            {stats.totalEntries > 0 && stats.trend === 'neutral' && (
              <Text style={styles.insightText}>
                📊 Your mood has been relatively stable. Consistency in mood tracking helps identify patterns over time.
              </Text>
            )}
            
            {stats.totalEntries >= 7 && (
              <Text style={styles.insightText}>
                📈 You've been consistently tracking your mood! This data helps healthcare providers understand your mental health patterns.
              </Text>
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },

  content: {
    flex: 1,
    padding: 20,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    padding: 4,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 20,
    alignItems: 'center',
  },
  periodButtonActive: {
    backgroundColor: '#66948a',
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#7F8C8D',
  },
  periodButtonTextActive: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  chartCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: 15,
    textAlign: 'center',
  },
  chartScrollContainer: {
    backgroundColor: '#FAFBFC',
    borderRadius: 10,
  },
  chartContainer: {
    position: 'relative',
    backgroundColor: '#FAFBFC',
    borderRadius: 10,
    overflow: 'hidden',
  },
  noDataText: {
    textAlign: 'center',
    color: '#7F8C8D',
    fontSize: 14,
    marginTop: 80,
  },
  loadingText: {
    textAlign: 'center',
    color: '#7F8C8D',
    fontSize: 14,
    marginTop: 80,
  },
  chartGrid: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  gridLineHorizontal: {
    position: 'absolute',
    left: 30,
    right: 30,
    height: 1,
    backgroundColor: '#E5E7EB',
  },
  gridLineVertical: {
    position: 'absolute',
    top: 30,
    bottom: 30,
    width: 1,
    backgroundColor: '#E5E7EB',
  },
  yAxisLabels: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: 25,
  },
  yAxisLabel: {
    position: 'absolute',
    left: 0,
    width: 25,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  yAxisLabelText: {
    fontSize: 16,
  },
  chartContent: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  chartLine: {
    position: 'absolute',
    height: 3,
    backgroundColor: '#66948a',
    transformOrigin: 'left center',
    borderRadius: 1.5,
  },
  chartPoint: {
    position: 'absolute',
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 3,
    borderColor: '#FFFFFF',
  },
  xAxisLabels: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 25,
  },
  xAxisLabel: {
    position: 'absolute',
    width: 30,
    bottom: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  xAxisLabelText: {
    fontSize: 10,
    color: '#9CA3AF',
    textAlign: 'center',
  },
  statsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: 15,
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    width: '48%',
    backgroundColor: '#F8F9FA',
    borderRadius: 15,
    padding: 15,
    alignItems: 'center',
    marginBottom: 10,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 12,
    color: '#7F8C8D',
    textAlign: 'center',
  },
  insightsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  insightsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: 15,
    textAlign: 'center',
  },
  insightsList: {
    gap: 10,
  },
  insightText: {
    fontSize: 14,
    color: '#34495E',
    lineHeight: 20,
    backgroundColor: '#F8F9FA',
    padding: 15,
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#66948a',
  },
});

export default MoodAnalytics;
