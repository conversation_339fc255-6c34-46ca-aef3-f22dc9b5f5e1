import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Image,
  TextInput,
  Modal,
  Platform,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
  StatusBar
} from 'react-native';
import { useState, useEffect } from 'react';
import { useRouter } from 'expo-router';
import { useAppointment } from '../../context/AppointmentContext';
import { useUser } from '../../context/UserContext';
import StandardHeader from '../../components/StandardHeader';
import { processAppointmentPayment } from '../../lib/maya-payment-service';
import { supabaseUrl, supabaseAnonKey } from '../../lib/supabase';

const ScheduleAppointment = () => {
  const router = useRouter();
  const { userData, updateLastInteraction } = useUser();
  const {
    psychometricians,
    bookAppointment,
    isTimeSlotAvailable,
    getDates,
    getTimeSlots,
    formatDate,
    formatDateString,
    appointments,
    allAppointments,
    fetchAppointments
  } = useAppointment();

  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null);
  const [selectedPsychometrician, setSelectedPsychometrician] = useState(null);
  const [notes, setNotes] = useState('');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [availableTimeSlots, setAvailableTimeSlots] = useState([]);
  const [isBooking, setIsBooking] = useState(false);
  const [bookingError, setBookingError] = useState(null);

  // Card form states
  const [cardDetails, setCardDetails] = useState({
    firstName: '',
    lastName: '',
    cardNumber: '',
    expiryDate: '',
    cvv: ''
  });

  // Helper functions for card input formatting
  const formatCardNumber = (text) => {
    // Remove all non-digits
    const cleaned = text.replace(/\D/g, '');
    // Add spaces every 4 digits
    const formatted = cleaned.replace(/(\d{4})(?=\d)/g, '$1 ');
    return formatted.substring(0, 19); // Max 16 digits + 3 spaces
  };

  const formatExpiryDate = (text) => {
    // Remove all non-digits
    const cleaned = text.replace(/\D/g, '');
    // Add slash after 2 digits
    if (cleaned.length >= 2) {
      return cleaned.substring(0, 2) + '/' + cleaned.substring(2, 4);
    }
    return cleaned;
  };

  const updateCardDetails = (field, value) => {
    let formattedValue = value;

    if (field === 'cardNumber') {
      formattedValue = formatCardNumber(value);
    } else if (field === 'expiryDate') {
      formattedValue = formatExpiryDate(value);
    } else if (field === 'cvv') {
      formattedValue = value.replace(/\D/g, '').substring(0, 4); // Max 4 digits
    }

    setCardDetails(prev => ({
      ...prev,
      [field]: formattedValue
    }));
  };

  const isCardFormValid = () => {
    return cardDetails.firstName.trim() !== '' &&
           cardDetails.lastName.trim() !== '' &&
           cardDetails.cardNumber.replace(/\s/g, '').length >= 13 &&
           cardDetails.expiryDate.length === 5 &&
           cardDetails.cvv.length >= 3;
  };

  // Update available time slots when psychometrician or date changes
  useEffect(() => {
    if (selectedPsychometrician && selectedDate) {
      const slots = getTimeSlots(selectedPsychometrician.id, selectedDate);
      setAvailableTimeSlots(slots);

      // Only reset time slot if the currently selected one is not available in the new slots
      if (selectedTimeSlot) {
        const isCurrentSlotAvailable = slots.some(slot =>
          slot.start_time === selectedTimeSlot.start_time &&
          slot.end_time === selectedTimeSlot.end_time
        );
        if (!isCurrentSlotAvailable) {
          setSelectedTimeSlot(null);
        }
      }
    } else {
      setAvailableTimeSlots([]);
      setSelectedTimeSlot(null); // Only reset when no psychometrician or date
    }
  }, [selectedPsychometrician, selectedDate, getTimeSlots]);

  // Refresh time slots when appointments change (for real-time updates)
  useEffect(() => {
    if (selectedPsychometrician && selectedDate) {
      const slots = getTimeSlots(selectedPsychometrician.id, selectedDate);
      setAvailableTimeSlots(slots);

      // Check if currently selected time slot is still available
      if (selectedTimeSlot) {
        const isStillAvailable = isTimeSlotAvailable(
          selectedDate,
          selectedTimeSlot,
          selectedPsychometrician.id
        );
        if (!isStillAvailable) {
          setSelectedTimeSlot(null); // Clear selection if no longer available
        }
      }
    }
  }, [appointments, allAppointments]); // Also listen to allAppointments changes

  const handleBookAppointment = () => {
    if (selectedDate && selectedTimeSlot && selectedPsychometrician) {
      setShowConfirmModal(true);
    }
  };

  const confirmAppointment = () => {
    // After confirming details, show payment modal
    setShowConfirmModal(false);
    setShowPaymentModal(true);
  };

  const processPayment = async () => {
    // Process payment and book the appointment
    setIsBooking(true);
    setBookingError(null);
    updateLastInteraction(); // Update session activity

    try {
      // Validate user authentication
      if (!userData?.id) {
        throw new Error('Please log in to book an appointment.');
      }

      // Double-check availability before booking
      const isStillAvailable = isTimeSlotAvailable(
        selectedDate,
        selectedTimeSlot,
        selectedPsychometrician.id
      );

      if (!isStillAvailable) {
        throw new Error('This time slot is no longer available. Please select a different time.');
      }

      // Validate card form
      if (!isCardFormValid()) {
        console.log('❌ Card validation failed:', cardDetails);
        throw new Error('Please fill in all card details correctly.');
      }

      // Validate card details format
      const cardNumber = cardDetails.cardNumber.replace(/\s/g, '');
      const expiryParts = cardDetails.expiryDate.split('/');

      if (cardNumber.length < 13 || cardNumber.length > 19) {
        throw new Error('Please enter a valid card number.');
      }

      if (expiryParts.length !== 2 || expiryParts[0].length !== 2 || expiryParts[1].length !== 2) {
        throw new Error('Please enter expiry date in MM/YY format.');
      }

      if (cardDetails.cvv.length < 3 || cardDetails.cvv.length > 4) {
        throw new Error('Please enter a valid CVV.');
      }

      console.log('🚀 Starting appointment booking with Maya payment...');
      console.log('Card details being processed:', {
        cardNumber: cardNumber.substring(0, 4) + '****',
        expiry: cardDetails.expiryDate,
        cvvLength: cardDetails.cvv.length,
        firstName: cardDetails.firstName,
        lastName: cardDetails.lastName
      });

      // Step 1: Book the appointment first
      const appointmentResult = await bookAppointment(
        selectedPsychometrician.id,
        selectedDate,
        selectedTimeSlot,
        notes
      );

      console.log('✅ Appointment booked, processing payment...');

      // Step 2: Process Maya payment
      const cleanCardNumber = cardDetails.cardNumber.replace(/\s/g, '');
      const expMonth = expiryParts[0].padStart(2, '0');
      const expYear = expiryParts[1].length === 2 ? '20' + expiryParts[1] : expiryParts[1];

      // For sandbox testing, simulate Maya payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Generate reference number
      const referenceNumber = `MAYA-${userData.id}-${appointmentResult?.id || Date.now()}-${Date.now()}`;

      // Record payment in invoices table using Maya payment service
      const { createInvoice } = await import('../../lib/maya-payment-service');

      console.log('📄 Recording invoice...');
      const invoice = await createInvoice({
        user_id: userData.id,
        reference_number: referenceNumber,
        payment_status: 'paid',
        amount: 500.00,
        schedule_id: selectedTimeSlot?.id || null
      });

      const paymentResult = {
        success: true,
        referenceNumber: referenceNumber,
        amount: 500.00,
        status: 'completed',
        invoiceId: invoice.id
      };

      console.log('✅ Payment processed and invoice recorded:', paymentResult);

      setShowPaymentModal(false);
      setShowSuccessModal(true);

      // Reset form
      setSelectedDate(null);
      setSelectedTimeSlot(null);
      setSelectedPsychometrician(null);
      setNotes('');
      setSelectedPaymentMethod(null);
      setCardDetails({
        firstName: '',
        lastName: '',
        cardNumber: '',
        expiryDate: '',
        cvv: ''
      });

    } catch (error) {
      console.error('❌ Error processing appointment payment:', error);
      setBookingError(error.message || 'Failed to process payment. Please try again.');
    } finally {
      setIsBooking(false);
    }
  };

  const closeSuccessModal = async () => {
    setShowSuccessModal(false);

    // Add a small delay to ensure database has been updated
    setTimeout(() => {
      router.replace('/(main)/appointments');
    }, 500);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={styles.container}>
          <StandardHeader title="Schedule Appointment" />

          <ScrollView contentContainerStyle={styles.content}>
            <Text style={styles.sectionTitle}>Select a Psychometrician</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.therapistsContainer}
            >
              {psychometricians.map((psychometrician) => (
                <TouchableOpacity
                  key={psychometrician.id}
                  style={[
                    styles.therapistCard,
                    selectedPsychometrician?.id === psychometrician.id && styles.selectedTherapistCard,
                    !psychometrician.available && styles.unavailableTherapistCard
                  ]}
                  onPress={() => psychometrician.available !== false && setSelectedPsychometrician(psychometrician)}
                  disabled={psychometrician.available === false}
                >
                  <Text style={styles.therapistImage}>{psychometrician.image || '👨‍⚕️'}</Text>
                  <Text style={styles.therapistName}>{psychometrician.name}</Text>
                  <Text style={styles.therapistSpecialty}>{psychometrician.specialty || 'Psychometrician'}</Text>
                  <View style={styles.therapistInfoRow}>
                    <Text style={styles.therapistExperience}>{psychometrician.experience || '5+ years'}</Text>
                    <Text style={styles.therapistRating}>⭐ {psychometrician.rating || '4.8'}</Text>
                  </View>
                  {psychometrician.available === false && (
                    <View style={styles.unavailableOverlay}>
                      <Text style={styles.unavailableText}>Unavailable</Text>
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>

            <Text style={styles.sectionTitle}>Select a Date</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.datesContainer}
            >
              {getDates().map((date, index) => {
                const formattedDate = formatDate(date);
                const dateString = formatDateString(date);
                return (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.dateCard,
                      selectedDate === dateString && styles.selectedDateCard
                    ]}
                    onPress={() => setSelectedDate(dateString)}
                  >
                    <Text style={styles.dateDay}>{formattedDate.day}</Text>
                    <Text style={styles.dateNumber}>{formattedDate.date}</Text>
                    <Text style={styles.dateMonth}>{formattedDate.month}</Text>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>

            <Text style={styles.sectionTitle}>Select a Time</Text>
            <View style={styles.timeContainer}>
              {availableTimeSlots.map((timeSlot, index) => {
                // Safety check to prevent undefined timeSlot errors
                if (!timeSlot || !timeSlot.start_time || !timeSlot.end_time) {
                  return null;
                }

                const available = selectedDate && selectedPsychometrician ?
                  isTimeSlotAvailable(selectedDate, timeSlot, selectedPsychometrician.id) : true;
                return (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.timeCard,
                      selectedTimeSlot?.start_time === timeSlot.start_time && styles.selectedTimeCard,
                      !available && styles.unavailableTimeCard
                    ]}
                    onPress={() => available && setSelectedTimeSlot(timeSlot)}
                    disabled={!available}
                  >
                    <Text
                      style={[
                        styles.timeText,
                        selectedTimeSlot?.start_time === timeSlot.start_time && styles.selectedTimeText,
                        !available && styles.unavailableTimeText
                      ]}
                    >
                      {timeSlot.display}
                    </Text>
                    {!available && (
                      <Text style={styles.bookedText}>Booked</Text>
                    )}
                  </TouchableOpacity>
                );
              })}
              {availableTimeSlots.length === 0 && selectedPsychometrician && selectedDate && (
                <Text style={styles.noTimeSlotsText}>
                  No available time slots for this psychometrician on the selected date.
                </Text>
              )}
            </View>

            <Text style={styles.sectionTitle}>Additional Notes</Text>
            <TextInput
              style={styles.notesInput}
              placeholder="Add any notes for your psychometrician..."
              value={notes}
              onChangeText={setNotes}
              multiline
              textAlignVertical="top"
            />

            <TouchableOpacity
              style={[
                styles.bookButton,
                (!selectedDate || !selectedTimeSlot || !selectedPsychometrician) && styles.disabledButton
              ]}
              onPress={handleBookAppointment}
              disabled={!selectedDate || !selectedTimeSlot || !selectedPsychometrician}
            >
              <Text style={styles.bookButtonText}>Book Appointment</Text>
            </TouchableOpacity>
          </ScrollView>

          {/* Confirmation Modal */}
          <Modal
            visible={showConfirmModal}
            transparent={true}
            animationType="none"
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Confirm Appointment</Text>

                {selectedPsychometrician && (
                  <View style={styles.confirmTherapist}>
                    <Text style={styles.confirmTherapistImage}>{selectedPsychometrician.image || '👨‍⚕️'}</Text>
                    <View style={styles.confirmTherapistInfo}>
                      <Text style={styles.confirmTherapistName}>{selectedPsychometrician.name}</Text>
                      <Text style={styles.confirmTherapistSpecialty}>{selectedPsychometrician.specialty || 'Psychometrician'}</Text>
                    </View>
                  </View>
                )}

                {selectedDate && selectedTimeSlot && (
                  <View style={styles.confirmDateTime}>
                    <Text style={styles.confirmDateTimeText}>
                      {new Date(selectedDate).toLocaleDateString('en-US', {
                        weekday: 'short',
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </Text>
                    <Text style={styles.confirmDateTimeText}>at {selectedTimeSlot.display}</Text>
                  </View>
                )}

                <View style={styles.confirmButtons}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => setShowConfirmModal(false)}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.confirmButton}
                    onPress={confirmAppointment}
                  >
                    <Text style={styles.confirmButtonText}>Confirm</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>

          {/* Payment Modal */}
          <Modal
            visible={showPaymentModal}
            transparent={true}
            animationType="none"
          >
            <View style={styles.modalOverlay}>
              <ScrollView
                style={styles.paymentModalContent}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.paymentScrollContent}
              >
                <View style={styles.mayaHeader}>
                  <Text style={styles.mayaTitle}>Maya Developers Portal</Text>
                  <Text style={styles.mayaSubtitle}>Or pay using your debit/credit card or wallet</Text>
                </View>

                <View style={styles.cardFormContainer}>
                  <View style={styles.cardHeader}>
                    <Text style={styles.cardTitle}>Debit/Credit Card</Text>
                    <View style={styles.cardLogos}>
                      <View style={styles.mastercardLogo} />
                      <View style={styles.visaLogo} />
                      <View style={styles.jcbLogo} />
                    </View>
                  </View>

                  <View style={styles.nameRow}>
                    <View style={styles.nameField}>
                      <Text style={styles.fieldLabel}>First Name</Text>
                      <TextInput
                        style={styles.cardInput}
                        value={cardDetails.firstName}
                        onChangeText={(text) => updateCardDetails('firstName', text)}
                        autoCapitalize="words"
                      />
                    </View>
                    <View style={styles.nameField}>
                      <Text style={styles.fieldLabel}>Last Name</Text>
                      <TextInput
                        style={styles.cardInput}
                        value={cardDetails.lastName}
                        onChangeText={(text) => updateCardDetails('lastName', text)}
                        autoCapitalize="words"
                      />
                    </View>
                  </View>

                  <View style={styles.cardNumberContainer}>
                    <Text style={styles.fieldLabel}>Card Number</Text>
                    <TextInput
                      style={styles.cardInput}
                      value={cardDetails.cardNumber}
                      onChangeText={(text) => updateCardDetails('cardNumber', text)}
                      keyboardType="numeric"
                      maxLength={19}
                    />
                  </View>

                  <View style={styles.cardDetailsRow}>
                    <View style={styles.expiryField}>
                      <Text style={styles.fieldLabel}>Expiry Date</Text>
                      <TextInput
                        style={styles.cardInput}
                        value={cardDetails.expiryDate}
                        onChangeText={(text) => updateCardDetails('expiryDate', text)}
                        keyboardType="numeric"
                        maxLength={5}
                      />
                    </View>
                    <View style={styles.cvvField}>
                      <Text style={styles.fieldLabel}>CVV</Text>
                      <TextInput
                        style={styles.cardInput}
                        value={cardDetails.cvv}
                        onChangeText={(text) => updateCardDetails('cvv', text)}
                        keyboardType="numeric"
                        maxLength={4}
                        secureTextEntry={true}
                      />
                    </View>
                  </View>

                  <View style={styles.testCardInfo}>
                    <Text style={styles.testCardTitle}>🧪 Sandbox Test Mode</Text>
                    <Text style={styles.testCardNote}>Use test card: **************** | Exp: 12/25 | CVV: 123</Text>
                    <TouchableOpacity
                      style={styles.fillTestCardButton}
                      onPress={() => {
                        setCardDetails({
                          firstName: 'John',
                          lastName: 'Doe',
                          cardNumber: '4123 4501 3100 1381',
                          expiryDate: '12/25',
                          cvv: '123'
                        });
                      }}
                    >
                      <Text style={styles.fillTestCardText}>Fill Test Card</Text>
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.orderSummary}>
                  <Text style={styles.orderSummaryTitle}>Order Summary</Text>
                  <View style={styles.orderSummaryRow}>
                    <Text style={styles.orderSummaryLabel}>Subtotal</Text>
                    <Text style={styles.orderSummaryValue}>PHP 500.00</Text>
                  </View>
                  <View style={styles.orderSummaryDivider} />
                  <View style={styles.orderSummaryRow}>
                    <Text style={styles.orderSummaryTotal}>Total Amount</Text>
                    <Text style={styles.orderSummaryTotalValue}>PHP 500.00</Text>
                  </View>
                  <View style={styles.poweredByMaya}>
                    <Text style={styles.poweredByText}>Powered by </Text>
                    <Text style={styles.mayaBrand}>maya</Text>
                    <Text style={styles.businessText}> BUSINESS</Text>
                  </View>
                </View>

                {bookingError && (
                  <View style={styles.errorContainer}>
                    <Text style={styles.errorText}>{bookingError}</Text>
                  </View>
                )}

                <View style={styles.confirmButtons}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => {
                      setShowPaymentModal(false);
                      setBookingError(null);
                    }}
                    disabled={isBooking}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.confirmButton,
                      (!isCardFormValid() || isBooking) && styles.disabledButton
                    ]}
                    onPress={processPayment}
                    disabled={!isCardFormValid() || isBooking}
                  >
                    <Text style={styles.confirmButtonText}>
                      {isBooking ? 'Processing...' : 'Complete Order'}
                    </Text>
                  </TouchableOpacity>
                </View>
              </ScrollView>
            </View>
          </Modal>

          {/* Success Modal */}
          <Modal
            visible={showSuccessModal}
            transparent={true}
            animationType="none"
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <Text style={styles.successIcon}>✅</Text>
                <Text style={styles.successTitle}>Appointment Booked!</Text>
                <Text style={styles.successMessage}>
                  Your appointment has been successfully scheduled and payment processed. You will receive a confirmation email shortly.
                </Text>

                <TouchableOpacity
                  style={styles.doneButton}
                  onPress={closeSuccessModal}
                >
                  <Text style={styles.doneButtonText}>Done</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Modal>
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },

  content: {
    padding: 16,
    paddingBottom: 40,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
  },
  therapistsContainer: {
    paddingBottom: 10,
  },
  therapistCard: {
    width: 160,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedTherapistCard: {
    borderColor: '#6B9142',
    borderWidth: 2,
  },
  unavailableTherapistCard: {
    opacity: 0.6,
  },
  therapistImage: {
    fontSize: 40,
    marginBottom: 10,
    alignSelf: 'center',
  },
  therapistName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 4,
  },
  therapistSpecialty: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 8,
  },
  therapistInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  therapistExperience: {
    fontSize: 11,
    color: '#888888',
  },
  therapistRating: {
    fontSize: 11,
    color: '#FF9500',
    fontWeight: 'bold',
  },
  unavailableOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  unavailableText: {
    color: '#FF3B30',
    fontWeight: 'bold',
    fontSize: 14,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 10,
  },
  datesContainer: {
    paddingBottom: 10,
  },
  dateCard: {
    width: 70,
    height: 90,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  selectedDateCard: {
    backgroundColor: '#6B9142',
  },
  dateDay: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  dateNumber: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 2,
  },
  dateMonth: {
    fontSize: 14,
    color: '#666666',
  },
  timeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  timeCard: {
    width: '30%',
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 12,
    marginBottom: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  selectedTimeCard: {
    backgroundColor: '#6B9142',
  },
  unavailableTimeCard: {
    backgroundColor: '#F5F5F5',
  },
  timeText: {
    fontSize: 14,
    color: '#333333',
  },
  selectedTimeText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  unavailableTimeText: {
    color: '#999999',
  },
  bookedText: {
    fontSize: 10,
    color: '#FF3B30',
    marginTop: 4,
  },
  noTimeSlotsText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 20,
  },
  notesInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    height: 120,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#EEEEEE',
  },
  bookButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    shadowColor: '#6B9142',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
    shadowColor: '#999999',
  },
  bookButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 340,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 20,
  },
  confirmTherapist: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    width: '100%',
  },
  confirmTherapistImage: {
    fontSize: 40,
    marginRight: 15,
  },
  confirmTherapistInfo: {
    flex: 1,
  },
  confirmTherapistName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 4,
  },
  confirmTherapistSpecialty: {
    fontSize: 14,
    color: '#666666',
  },
  confirmDateTime: {
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    padding: 16,
    width: '100%',
    marginBottom: 20,
    alignItems: 'center',
  },
  confirmDateTimeText: {
    fontSize: 16,
    color: '#333333',
    marginBottom: 4,
  },
  confirmButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  cancelButton: {
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 1,
    marginRight: 10,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#666666',
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 1,
    marginLeft: 10,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  successIcon: {
    fontSize: 50,
    marginBottom: 16,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 12,
  },
  successMessage: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  doneButton: {
    backgroundColor: '#6B9142',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 40,
    alignItems: 'center',
  },
  doneButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  // Payment Modal Styles
  paymentModalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    maxHeight: '90%',
    width: '100%',
    maxWidth: 380,
  },
  paymentScrollContent: {
    padding: 24,
    paddingBottom: 30,
  },
  paymentInfoText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 16,
    textAlign: 'center',
  },
  paymentOptionsContainer: {
    width: '100%',
    marginBottom: 20,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F9EE',
    borderRadius: 12,
    padding: 12,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  selectedPaymentOption: {
    borderColor: '#6B9142',
    backgroundColor: '#F5F9EE',
  },
  paymentOptionIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  paymentOptionInfo: {
    flex: 1,
  },
  paymentOptionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 2,
  },
  paymentOptionDesc: {
    fontSize: 12,
    color: '#666666',
  },
  paymentSelectedIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#6B9142',
  },
  paymentSummary: {
    width: '100%',
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  paymentSummaryTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 12,
  },
  paymentSummaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  paymentSummaryLabel: {
    fontSize: 13,
    color: '#666666',
  },
  paymentSummaryValue: {
    fontSize: 13,
    color: '#333333',
    fontWeight: '500',
  },
  paymentSummaryDivider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginVertical: 8,
  },
  paymentSummaryTotal: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
  },
  paymentSummaryTotalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B9142',
  },
  errorContainer: {
    backgroundColor: '#FFE6E6',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    width: '100%',
  },
  errorText: {
    color: '#D32F2F',
    fontSize: 14,
    textAlign: 'center',
    fontWeight: '500',
  },
  testCardInfo: {
    backgroundColor: '#F0F8FF',
    borderRadius: 8,
    padding: 12,
    marginTop: 12,
    borderWidth: 1,
    borderColor: '#B0E0E6',
  },
  testCardTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2E86AB',
    marginBottom: 6,
  },
  testCardText: {
    fontSize: 12,
    color: '#2E86AB',
    fontFamily: 'monospace',
    marginBottom: 2,
  },
  testCardNote: {
    fontSize: 11,
    color: '#666666',
    fontStyle: 'italic',
    marginTop: 4,
  },
  cardFormContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  cardLogos: {
    flexDirection: 'row',
    gap: 8,
  },
  cardLogo: {
    fontSize: 20,
  },
  nameRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  nameField: {
    flex: 1,
  },
  fieldLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 6,
    fontWeight: '500',
  },
  cardInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#FAFAFA',
    color: '#333333',
  },
  cardNumberContainer: {
    marginBottom: 16,
  },
  cardDetailsRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  expiryField: {
    flex: 1,
  },
  cvvField: {
    flex: 1,
  },
  // Maya-themed styles
  mayaHeader: {
    alignItems: 'center',
    marginBottom: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  mayaTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  mayaSubtitle: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
  },
  mastercardLogo: {
    width: 32,
    height: 20,
    backgroundColor: '#EB001B',
    borderRadius: 4,
  },
  visaLogo: {
    width: 32,
    height: 20,
    backgroundColor: '#1A1F71',
    borderRadius: 4,
  },
  jcbLogo: {
    width: 32,
    height: 20,
    backgroundColor: '#006CB7',
    borderRadius: 4,
  },
  orderSummary: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
  },
  orderSummaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 16,
  },
  orderSummaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  orderSummaryLabel: {
    fontSize: 14,
    color: '#666666',
  },
  orderSummaryValue: {
    fontSize: 14,
    color: '#333333',
    fontWeight: '500',
  },
  orderSummaryDivider: {
    height: 1,
    backgroundColor: '#E5E5E5',
    marginVertical: 12,
  },
  orderSummaryTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
  },
  orderSummaryTotalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  poweredByMaya: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
  },
  poweredByText: {
    fontSize: 12,
    color: '#999999',
  },
  mayaBrand: {
    fontSize: 12,
    color: '#00D4AA',
    fontWeight: 'bold',
  },
  businessText: {
    fontSize: 12,
    color: '#999999',
    fontWeight: 'bold',
  },
});

export default ScheduleAppointment;
