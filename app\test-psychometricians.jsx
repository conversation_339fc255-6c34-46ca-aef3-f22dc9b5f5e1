import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { useAppointment } from './context/AppointmentContext';
import { supabase } from './lib/supabase';

export default function TestPsychometricians() {
  const { psychometricians, fetchPsychometricians } = useAppointment();
  const [loading, setLoading] = useState(false);
  const [usersWithPsychRole, setUsersWithPsychRole] = useState([]);

  // Fetch users with psychometrician role directly
  const fetchPsychometricianUsers = async () => {
    try {
      setLoading(true);
      console.log('🔍 Fetching users with psychometrician role...');
      
      const { data, error } = await supabase
        .from('users')
        .select('id, name, email, role, status')
        .eq('role', 'psychometrician');

      if (error) {
        console.error('❌ Error:', error);
        Alert.alert('Error', 'Failed to fetch psychometrician users: ' + error.message);
        return;
      }

      console.log('✅ Raw psychometrician users:', data);
      setUsersWithPsychRole(data || []);
      
      // Also refresh the appointment context
      await fetchPsychometricians();
      
    } catch (error) {
      console.error('❌ Unexpected error:', error);
      Alert.alert('Error', 'Unexpected error: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPsychometricianUsers();
  }, []);

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Psychometricians Test</Text>
      
      <TouchableOpacity 
        style={styles.refreshButton} 
        onPress={fetchPsychometricianUsers}
        disabled={loading}
      >
        <Text style={styles.refreshButtonText}>
          {loading ? 'Loading...' : 'Refresh Data'}
        </Text>
      </TouchableOpacity>

      {/* Raw Users with Psychometrician Role */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Raw Users (role = 'psychometrician'): {usersWithPsychRole.length}
        </Text>
        {usersWithPsychRole.map((user) => (
          <View key={user.id} style={styles.userCard}>
            <Text style={styles.userName}>ID: {user.id} | {user.name || 'No name'}</Text>
            <Text style={styles.userEmail}>{user.email}</Text>
            <Text style={styles.userRole}>Role: {user.role} | Status: {user.status ? 'Active' : 'Inactive'}</Text>
          </View>
        ))}
        {usersWithPsychRole.length === 0 && (
          <Text style={styles.noData}>No users with psychometrician role found</Text>
        )}
      </View>

      {/* Processed Psychometricians from Context */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Processed Psychometricians (from context): {psychometricians.length}
        </Text>
        {psychometricians.map((psych) => (
          <View key={psych.id} style={styles.psychCard}>
            <Text style={styles.psychName}>
              {psych.image} {psych.name}
            </Text>
            <Text style={styles.psychDetails}>
              ID: {psych.id} | User ID: {psych.user_id}
            </Text>
            <Text style={styles.psychDetails}>
              Email: {psych.email}
            </Text>
            <Text style={styles.psychDetails}>
              Specialty: {psych.specialty} | Experience: {psych.experience}
            </Text>
            <Text style={styles.psychDetails}>
              Rating: ⭐ {psych.rating} | Available: {psych.available ? 'Yes' : 'No'}
            </Text>
          </View>
        ))}
        {psychometricians.length === 0 && (
          <Text style={styles.noData}>No psychometricians loaded in context</Text>
        )}
      </View>

      <View style={styles.instructions}>
        <Text style={styles.instructionsTitle}>Instructions:</Text>
        <Text style={styles.instructionsText}>
          1. Check if users with role='psychometrician' appear in the first section
        </Text>
        <Text style={styles.instructionsText}>
          2. Verify they are processed correctly in the second section
        </Text>
        <Text style={styles.instructionsText}>
          3. If no users appear, check your Supabase users table
        </Text>
        <Text style={styles.instructionsText}>
          4. Make sure users have status=true (active)
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  refreshButton: {
    backgroundColor: '#66948a',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  refreshButtonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
  },
  section: {
    marginBottom: 30,
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  userCard: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 6,
    marginBottom: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#007bff',
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  userRole: {
    fontSize: 12,
    color: '#888',
    marginTop: 4,
  },
  psychCard: {
    backgroundColor: '#e8f5e8',
    padding: 12,
    borderRadius: 6,
    marginBottom: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#28a745',
  },
  psychName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  psychDetails: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  noData: {
    textAlign: 'center',
    color: '#999',
    fontStyle: 'italic',
    padding: 20,
  },
  instructions: {
    backgroundColor: '#fff3cd',
    padding: 15,
    borderRadius: 8,
    marginTop: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#ffc107',
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#856404',
    marginBottom: 10,
  },
  instructionsText: {
    fontSize: 14,
    color: '#856404',
    marginBottom: 5,
  },
});
