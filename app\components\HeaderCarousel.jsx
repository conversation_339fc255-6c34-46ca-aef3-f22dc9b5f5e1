import React, { useState, useEffect, useRef } from 'react';
import { View, Image, StyleSheet, Dimensions, Animated } from 'react-native';

// Placeholder images for the carousel
// In a real app, you would replace these with actual image imports or URLs
const placeholderImages = [
  { id: 1, color: '#E8F3D8' }, // Light green
  { id: 2, color: '#FFE8E8' }, // Light pink
  { id: 3, color: '#E0F0FF' }, // Light blue
  { id: 4, color: '#FFE8D6' }, // Light orange
];

const { width } = Dimensions.get('window');

const HeaderCarousel = ({ height = 120 }) => {
  const scrollX = useRef(new Animated.Value(0)).current;
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef(null);

  // Auto-scroll the carousel
  useEffect(() => {
    const timer = setInterval(() => {
      const nextIndex = (currentIndex + 1) % placeholderImages.length;
      setCurrentIndex(nextIndex);
      
      // Animate to the next image
      scrollX.setValue(nextIndex * width);
    }, 3000); // Change image every 3 seconds

    return () => clearInterval(timer);
  }, [currentIndex]);

  return (
    <View style={[styles.container, { height }]}>
      <Animated.View
        style={[
          styles.carouselContainer,
          {
            transform: [
              {
                translateX: scrollX.interpolate({
                  inputRange: placeholderImages.map((_, i) => i * width),
                  outputRange: placeholderImages.map((_, i) => -i * width),
                }),
              },
            ],
          },
        ]}
      >
        {placeholderImages.map((image, index) => (
          <View
            key={image.id}
            style={[
              styles.imageContainer,
              { width, backgroundColor: image.color },
            ]}
          >
            {/* You can replace this with actual Image components */}
            <View style={styles.placeholderPattern}>
              {/* Pattern elements */}
              {Array.from({ length: 5 }).map((_, i) => (
                <View 
                  key={i} 
                  style={[
                    styles.patternCircle, 
                    { 
                      left: 20 + (i * 30), 
                      top: 10 + (i % 3) * 20,
                      opacity: 0.1 + (i % 5) * 0.05
                    }
                  ]} 
                />
              ))}
            </View>
          </View>
        ))}
      </Animated.View>
      
      {/* Pagination dots */}
      <View style={styles.pagination}>
        {placeholderImages.map((_, index) => (
          <View
            key={index}
            style={[
              styles.dot,
              { opacity: currentIndex === index ? 1 : 0.5 },
            ]}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    width: '100%',
    overflow: 'hidden',
  },
  carouselContainer: {
    flexDirection: 'row',
    height: '100%',
  },
  imageContainer: {
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderPattern: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  patternCircle: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
  },
  pagination: {
    position: 'absolute',
    bottom: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#FFFFFF',
    margin: 3,
  },
});

export default HeaderCarousel;
