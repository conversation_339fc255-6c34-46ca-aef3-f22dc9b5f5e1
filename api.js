export const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlrZXkiOiJlNjg0Y2ZiMC0zYmUyLTQyNTYtYmI3Zi0zMjE3MDI2NmVhOTEiLCJwZXJtaXNzaW9ucyI6WyJhbGxvd19qb2luIl0sImlhdCI6MTc0OTQzNDA4MiwiZXhwIjoxNzY0OTg2MDgyfQ.ffrnM20Zkj7WwD_RLrDcG9tTczk2oWA1ovn-fQHCYb0";
// API call to create meeting
export const createMeeting = async ({ token }) => {
  const res = await fetch(`https://api.videosdk.live/v2/rooms`, {
    method: "POST",
    headers: {
      authorization: `${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({}),
  });

  const { roomId } = await res.json();
  return roomId;
};