// Get VideoSDK token from environment variables
export const token = process.env.EXPO_PUBLIC_VIDEOSDK_TOKEN;
// API call to create meeting
export const createMeeting = async ({ token }) => {
  const res = await fetch(`https://api.videosdk.live/v2/rooms`, {
    method: "POST",
    headers: {
      authorization: `${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      // Configure meeting to be persistent and not auto-close
      autoCloseConfig: {
        type: "NEVER", // Never auto-close the meeting
      },
      // Add additional configuration to prevent auto-leave
      region: "sg001", // Specify a region for better stability
    }),
  });

  const { roomId } = await res.json();
  return roomId;
};