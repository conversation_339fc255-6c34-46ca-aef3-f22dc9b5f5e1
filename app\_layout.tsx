// Import polyfills first
import './polyfills';

import { Stack } from 'expo-router';
import { useCallback } from 'react';
import { View, StatusBar } from 'react-native';
import { useFonts } from 'expo-font';
import { UserProvider } from './context/UserContext';
import { AppointmentProvider } from './context/AppointmentContext';
import ErrorBoundary from './components/ErrorBoundary';

export default function RootLayout() {
  const [fontsLoaded] = useFonts({
    // You can add custom fonts here if needed
    // 'CustomFont-Regular': require('../assets/fonts/CustomFont-Regular.ttf'),
  });

  // Simplified onLayoutRootView function without SplashScreen
  const onLayoutRootView = useCallback(() => {
    // No need to do anything here now
  }, [fontsLoaded]);

  if (!fontsLoaded) {
    return null;
  }

  return (
    <ErrorBoundary>
      <UserProvider>
        <AppointmentProvider>
          <View style={{ flex: 1 }} onLayout={onLayoutRootView}>
            <StatusBar
              barStyle="light-content"
              backgroundColor="#66948a"
              translucent={true}
            />
            <Stack
              screenOptions={{
                headerShown: false,
                contentStyle: {
                  backgroundColor: '#ffffff',
                },
                // Smooth transitions for better UX (Jakob's Law - familiar patterns)
                animation: 'slide_from_right',
                animationDuration: 300,
                // Enable gesture navigation for natural interaction
                gestureEnabled: true,
                // Card presentation for smooth transitions
                presentation: 'card',
              }}
            >
              {/* Root screens */}
              <Stack.Screen name="index" />
              <Stack.Screen name="app-features" />
              <Stack.Screen name="payment-details" />
              <Stack.Screen name="test-psychometricians" />
              <Stack.Screen name="test-session-timeout" />

              {/* Authentication Group - hidden from URL */}
              <Stack.Screen name="(auth)" />

              {/* Main App Group - hidden from URL - Disable gestures for main app */}
              <Stack.Screen
                name="(main)"
                options={{
                  gestureEnabled: false, // Prevent swiping back to app-features from main app
                }}
              />
            </Stack>
          </View>
        </AppointmentProvider>
      </UserProvider>
    </ErrorBoundary>
  );
}






