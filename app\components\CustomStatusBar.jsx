import React from 'react';
import { StatusBar, View, StyleSheet, Platform } from 'react-native';
import Constants from 'expo-constants';

// Get the correct status bar height for different platforms
const STATUSBAR_HEIGHT = Platform.OS === 'ios'
  ? Constants.statusBarHeight
  : StatusBar.currentHeight;

/**
 * CustomStatusBar component for consistent status bar styling across the app
 *
 * @param {string} backgroundColor - Background color of the status bar
 * @param {string} barStyle - Style of the status bar text ('light-content' or 'dark-content')
 * @param {Object} props - Additional props to pass to StatusBar
 * @returns {React.ReactNode} - Rendered component
 */
const CustomStatusBar = ({
  backgroundColor = '#66948a',
  barStyle = "light-content",
  ...props
}) => {
  return (
    <View style={[styles.statusBar, { backgroundColor }]}>
      <StatusBar
        translucent
        backgroundColor={backgroundColor}
        barStyle={barStyle}
        {...props}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  statusBar: {
    height: STATUSBAR_HEIGHT,
    width: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 100,
  },
});

export default CustomStatusBar;
