import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  StatusBar,
  Image,
  Platform,
  TextInput,
  KeyboardAvoidingView,
  Keyboard,
  TouchableWithoutFeedback
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import BottomNavigation from './components/BottomNavigation';
import { useUser } from './context/UserContext';
import { useAppointment } from './context/AppointmentContext';

const PaymentDetails = () => {
  const router = useRouter();
  const { userData } = useUser();
  const { appointments, fetchAppointments, getPsychometricianById } = useAppointment();
  const [activeTab, setActiveTab] = useState('payment-methods');
  const [paymentMethods, setPaymentMethods] = useState([
    { id: 1, type: 'credit', last4: '4242', expiry: '04/25', default: true },
    { id: 2, type: 'debit', last4: '1234', expiry: '12/24', default: false },
  ]);
  // Generate receipts from completed appointments
  const generateReceiptsFromAppointments = () => {
    return appointments
      .filter(appointment => appointment.status === 'completed')
      .map((appointment, index) => {
        const psychometrician = getPsychometricianById(appointment.psychometrician_id);
        const appointmentDate = new Date(appointment.date);
        const receiptDate = appointmentDate.toISOString().split('T')[0]; // YYYY-MM-DD format
        const receiptNo = `REC-${String(appointment.id).padStart(3, '0')}-${appointmentDate.getFullYear()}`;

        return {
          id: appointment.id,
          appointmentId: appointment.id,
          date: receiptDate,
          amount: appointment.consultation_fee || 500.00,
          description: `Consultation with ${psychometrician?.name || 'Psychometrician'}`,
          receiptNo: receiptNo,
          psychometrician: psychometrician,
          appointmentDate: appointment.date,
          appointmentTime: `${appointment.start_time} - ${appointment.end_time}`,
          paymentMethod: appointment.payment_method || 'Cash',
          notes: appointment.notes
        };
      })
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()); // Most recent first
  };

  // Load appointments when component mounts
  useEffect(() => {
    if (userData?.id) {
      fetchAppointments();
    }
  }, [userData?.id]);

  // Dismiss keyboard when tapping outside input fields
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  const renderPaymentMethods = () => (
    <View style={styles.tabContent}>
      <Text style={styles.sectionTitle}>Your Payment Methods</Text>
      {paymentMethods.map((method) => (
        <View key={method.id} style={styles.paymentMethodCard}>
          <View style={styles.cardIconContainer}>
            {method.type === 'credit' ? (
              <Ionicons name="card" size={24} color="#00BCD4" />
            ) : (
              <Ionicons name="wallet" size={24} color="#00BCD4" />
            )}
          </View>
          <View style={styles.cardDetails}>
            <Text style={styles.cardType}>
              {method.type === 'credit' ? 'Credit Card' : 'Debit Card'} 
              {method.default && <Text style={styles.defaultBadge}> (Default)</Text>}
            </Text>
            <Text style={styles.cardNumber}>**** **** **** {method.last4}</Text>
            <Text style={styles.cardExpiry}>Expires: {method.expiry}</Text>
          </View>
          <TouchableOpacity style={styles.editButton}>
            <Ionicons name="create-outline" size={20} color="#00BCD4" />
          </TouchableOpacity>
        </View>
      ))}
      <TouchableOpacity style={styles.addButton}>
        <Ionicons name="add-circle" size={20} color="#FFFFFF" style={styles.addIcon} />
        <Text style={styles.addButtonText}>Add Payment Method</Text>
      </TouchableOpacity>
    </View>
  );

  const renderAppointmentHistory = () => {
    // Sort appointments by date (most recent first)
    const sortedAppointments = [...appointments].sort((a, b) =>
      new Date(b.date).getTime() - new Date(a.date).getTime()
    );

    return (
      <View style={styles.tabContent}>
        <Text style={styles.sectionTitle}>Appointment History</Text>
        {sortedAppointments.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="calendar-outline" size={48} color="#CCCCCC" />
            <Text style={styles.emptyStateTitle}>No Appointments Yet</Text>
            <Text style={styles.emptyStateText}>
              Your appointment history will appear here once you book your first appointment.
            </Text>
          </View>
        ) : (
          sortedAppointments.map((appointment) => {
            const psychometrician = getPsychometricianById(appointment.psychometrician_id);
            const appointmentDate = new Date(appointment.date);
            const formattedDate = appointmentDate.toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
            });

            // Format time from 24-hour to 12-hour format
            const formatTime = (timeStr) => {
              const [hours, minutes] = timeStr.split(':');
              const hour = parseInt(hours);
              const ampm = hour >= 12 ? 'pm' : 'am';
              const displayHour = hour > 12 ? hour - 12 : hour === 0 ? 12 : hour;
              return `${displayHour}:${minutes} ${ampm}`;
            };

            // Get status color
            const getStatusColor = (status) => {
              switch (status) {
                case 'completed': return '#4CAF50';
                case 'scheduled': return '#2196F3';
                case 'cancelled': return '#F44336';
                case 'rescheduled': return '#FF9800';
                default: return '#9E9E9E';
              }
            };

            return (
              <View key={appointment.id} style={styles.appointmentCard}>
                <View style={styles.appointmentHeader}>
                  <View style={styles.appointmentDateContainer}>
                    <Text style={styles.appointmentDate}>{formattedDate}</Text>
                    <Text style={styles.appointmentTime}>
                      {formatTime(appointment.start_time)} - {formatTime(appointment.end_time)}
                    </Text>
                  </View>
                  <View style={styles.appointmentStatus}>
                    <View style={[
                      styles.statusIndicator,
                      { backgroundColor: getStatusColor(appointment.status) }
                    ]} />
                    <Text style={styles.statusText}>
                      {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                    </Text>
                  </View>
                </View>

                <View style={styles.psychometricianInfo}>
                  <View style={styles.psychometricianAvatar}>
                    <Text style={styles.psychometricianAvatarText}>
                      {psychometrician?.name?.charAt(0)?.toUpperCase() || 'P'}
                    </Text>
                  </View>
                  <View style={styles.psychometricianDetails}>
                    <Text style={styles.psychometricianName}>
                      {psychometrician?.name || 'Psychometrician'}
                    </Text>
                    <Text style={styles.psychometricianSpecialty}>
                      {psychometrician?.specialty || 'Mental Health Professional'}
                    </Text>
                  </View>
                </View>

                {appointment.notes && (
                  <View style={styles.appointmentNotes}>
                    <Text style={styles.notesLabel}>Notes:</Text>
                    <Text style={styles.notesText}>{appointment.notes}</Text>
                  </View>
                )}

                <View style={styles.appointmentFooter}>
                  <Text style={styles.consultationFee}>
                    Consultation Fee: ₱{appointment.consultation_fee?.toFixed(2) || '500.00'}
                  </Text>
                  {appointment.payment_method && (
                    <Text style={styles.paymentMethod}>
                      Payment: {appointment.payment_method}
                    </Text>
                  )}
                </View>
              </View>
            );
          })
        )}
      </View>
    );
  };

  const renderReceipts = () => {
    const receipts = generateReceiptsFromAppointments();

    return (
      <View style={styles.tabContent}>
        <Text style={styles.sectionTitle}>Receipts & Invoices</Text>
        {receipts.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="receipt-outline" size={48} color="#CCCCCC" />
            <Text style={styles.emptyStateTitle}>No Receipts Available</Text>
            <Text style={styles.emptyStateText}>
              Receipts will be generated automatically after completing your appointments.
            </Text>
          </View>
        ) : (
          receipts.map((receipt) => (
            <View key={receipt.id} style={styles.receiptCard}>
              <View style={styles.receiptHeader}>
                <View style={styles.receiptDateContainer}>
                  <Text style={styles.receiptDate}>
                    {new Date(receipt.date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric',
                    })}
                  </Text>
                  <Text style={styles.receiptTime}>{receipt.appointmentTime}</Text>
                </View>
                <Text style={styles.receiptNo}>{receipt.receiptNo}</Text>
              </View>

              <View style={styles.receiptDetails}>
                <Text style={styles.receiptDescription}>{receipt.description}</Text>
                <Text style={styles.receiptService}>Mental Health Consultation</Text>

                {receipt.psychometrician && (
                  <View style={styles.receiptPsychometrician}>
                    <Text style={styles.receiptPsychometricianLabel}>Provider:</Text>
                    <Text style={styles.receiptPsychometricianName}>
                      {receipt.psychometrician.name}
                    </Text>
                    <Text style={styles.receiptPsychometricianSpecialty}>
                      {receipt.psychometrician.specialty || 'Mental Health Professional'}
                    </Text>
                  </View>
                )}
              </View>

              <View style={styles.receiptPayment}>
                <View style={styles.receiptAmountContainer}>
                  <Text style={styles.receiptAmountLabel}>Total Amount:</Text>
                  <Text style={styles.receiptAmount}>₱{receipt.amount.toFixed(2)}</Text>
                </View>
                <Text style={styles.receiptPaymentMethod}>
                  Payment Method: {receipt.paymentMethod}
                </Text>
              </View>

              {receipt.notes && (
                <View style={styles.receiptNotes}>
                  <Text style={styles.receiptNotesLabel}>Session Notes:</Text>
                  <Text style={styles.receiptNotesText}>{receipt.notes}</Text>
                </View>
              )}

              <View style={styles.receiptActions}>
                <TouchableOpacity style={styles.downloadButton}>
                  <Ionicons name="download-outline" size={16} color="#FFFFFF" style={styles.downloadIcon} />
                  <Text style={styles.downloadText}>Download PDF</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.shareButton}>
                  <Ionicons name="share-outline" size={16} color="#66948a" style={styles.shareIcon} />
                  <Text style={styles.shareText}>Share</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))
        )}
      </View>
    );
  };

  return (
    <TouchableWithoutFeedback onPress={dismissKeyboard}>
      <SafeAreaView style={styles.container}>
        <StatusBar
          translucent={true}
          backgroundColor="#66948a"
          barStyle="light-content"
        />
        
        {/* Header */}
        <LinearGradient
          colors={['#66948a', '#66948a']}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Payment Details</Text>
            <View style={styles.placeholderButton} />
          </View>
        </LinearGradient>

        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'payment-methods' && styles.activeTab]}
            onPress={() => setActiveTab('payment-methods')}
          >
            <Text style={[styles.tabText, activeTab === 'payment-methods' && styles.activeTabText]}>
              Payment Methods
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'appointments' && styles.activeTab]}
            onPress={() => setActiveTab('appointments')}
          >
            <Text style={[styles.tabText, activeTab === 'appointments' && styles.activeTabText]}>
              Appointments
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'receipts' && styles.activeTab]}
            onPress={() => setActiveTab('receipts')}
          >
            <Text style={[styles.tabText, activeTab === 'receipts' && styles.activeTabText]}>
              Receipts
            </Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardAvoidingView}
        >
          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
            {activeTab === 'payment-methods' && renderPaymentMethods()}
            {activeTab === 'appointments' && renderAppointmentHistory()}
            {activeTab === 'receipts' && renderReceipts()}
          </ScrollView>
        </KeyboardAvoidingView>

        {/* Bottom Navigation */}
        <BottomNavigation />
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    paddingTop: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight + 15,
    paddingBottom: 15,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  placeholderButton: {
    width: 40,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingVertical: 5,
    paddingHorizontal: 10,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: 'rgba(0, 188, 212, 0.1)',
  },
  tabText: {
    fontSize: 13,
    color: '#666666',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#00BCD4',
    fontWeight: 'bold',
  },
  tabContent: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 16,
  },
  paymentMethodCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  cardIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 188, 212, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  cardDetails: {
    flex: 1,
  },
  cardType: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  defaultBadge: {
    color: '#00BCD4',
    fontWeight: 'bold',
  },
  cardNumber: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
  },
  cardExpiry: {
    fontSize: 12,
    color: '#999999',
  },
  editButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 188, 212, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButton: {
    backgroundColor: '#00BCD4',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
    shadowColor: '#00BCD4',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  addIcon: {
    marginRight: 8,
  },
  addButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  appointmentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  appointmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  appointmentDateContainer: {
    flex: 1,
  },
  appointmentDate: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 2,
  },
  appointmentTime: {
    fontSize: 14,
    color: '#666666',
  },
  appointmentStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  psychometricianInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  psychometricianAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#66948a',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  psychometricianAvatarText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  psychometricianDetails: {
    flex: 1,
  },
  psychometricianName: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 2,
  },
  psychometricianSpecialty: {
    fontSize: 13,
    color: '#666666',
  },
  appointmentNotes: {
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  notesLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666666',
    marginBottom: 4,
  },
  notesText: {
    fontSize: 14,
    color: '#333333',
    lineHeight: 18,
  },
  appointmentFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  consultationFee: {
    fontSize: 14,
    fontWeight: '600',
    color: '#66948a',
  },
  paymentMethod: {
    fontSize: 12,
    color: '#666666',
    textTransform: 'capitalize',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 20,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    color: '#666666',
  },
  receiptCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  receiptHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  receiptDate: {
    fontSize: 14,
    color: '#666666',
  },
  receiptNo: {
    fontSize: 12,
    color: '#999999',
  },
  receiptDescription: {
    fontSize: 15,
    color: '#333333',
    marginBottom: 4,
  },
  receiptAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 12,
  },
  downloadButton: {
    backgroundColor: '#00BCD4',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-start',
  },
  downloadIcon: {
    marginRight: 6,
  },
  downloadText: {
    color: '#FFFFFF',
    fontSize: 13,
    fontWeight: '600',
  },
});

export default PaymentDetails;
