-- Add missing mobile app columns to existing 'users' table
-- Run this in your Supabase SQL editor to sync with mobile app needs

-- Add mobile-specific columns that are missing from web 'users' table
ALTER TABLE "users" 
ADD COLUMN IF NOT EXISTS middle_name <PERSON><PERSON><PERSON><PERSON>,
ADD COLUMN IF NOT EXISTS last_name <PERSON><PERSON><PERSON><PERSON>,
ADD COLUMN IF NOT EXISTS age INTEGER,
ADD COLUMN IF NOT EXISTS gender VARCHAR,
ADD COLUMN IF NOT EXISTS civil_status VARCHAR,
ADD COLUMN IF NOT EXISTS birthdate VARCHAR,
ADD COLUMN IF NOT EXISTS birthplace VARCHAR,
ADD COLUMN IF NOT EXISTS religion VARCHAR,
ADD COLUMN IF NOT EXISTS address VARCHAR,
ADD COLUMN IF NOT EXISTS control_number VARCHAR UNIQUE,
ADD COLUMN IF NOT EXISTS has_completed_profile BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS verification_code_expires TIMESTAMPTZ;

-- Add comments for documentation
COMMENT ON COLUMN "users".middle_name IS 'User middle name (mobile app)';
COMMENT ON COLUMN "users".last_name IS 'User last name (mobile app)';
COMMENT ON COLUMN "users".age IS 'User age (mobile app)';
COMMENT ON COLUMN "users".gender IS 'User gender (mobile app)';
COMMENT ON COLUMN "users".civil_status IS 'User civil status (mobile app)';
COMMENT ON COLUMN "users".birthdate IS 'User birth date (mobile app)';
COMMENT ON COLUMN "users".birthplace IS 'User birth place (mobile app)';
COMMENT ON COLUMN "users".religion IS 'User religion (mobile app)';
COMMENT ON COLUMN "users".address IS 'User address (mobile app)';
COMMENT ON COLUMN "users".control_number IS 'Mobile app unique identifier (e.g., MH-2023-45678)';
COMMENT ON COLUMN "users".has_completed_profile IS 'Mobile app profile completion flag';
COMMENT ON COLUMN "users".verification_code_expires IS 'Expiration timestamp for activation code';

-- Create indexes for mobile-specific columns
CREATE INDEX IF NOT EXISTS idx_users_control_number ON "users"(control_number);
CREATE INDEX IF NOT EXISTS idx_users_has_completed_profile ON "users"(has_completed_profile);

-- Update existing users to have default values for mobile columns
UPDATE "users" 
SET 
  has_completed_profile = CASE 
    WHEN name IS NOT NULL AND name != '' THEN TRUE 
    ELSE FALSE 
  END,
  control_number = CASE 
    WHEN control_number IS NULL THEN 'MH-' || EXTRACT(YEAR FROM NOW()) || '-' || LPAD((id % 90000 + 10000)::text, 5, '0')
    ELSE control_number 
  END
WHERE control_number IS NULL OR has_completed_profile IS NULL;

-- Verification query to check added columns
-- SELECT column_name, data_type, is_nullable 
-- FROM information_schema.columns 
-- WHERE table_name = 'users' 
-- ORDER BY ordinal_position;
