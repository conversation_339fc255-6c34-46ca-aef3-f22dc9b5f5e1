import { createContext, useState, useContext, useEffect, useRef } from 'react';
import { clearChatHistory } from '../services/chatStorageService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';
import { useRouter } from 'expo-router';

// Create the context
const UserContext = createContext();

export default UserContext;

// Create a provider component
export const UserProvider = ({ children }) => {
  const router = useRouter();
  const sessionTimeoutRef = useRef(null);
  const warningTimeoutRef = useRef(null);
  const [isSessionWarningVisible, setIsSessionWarningVisible] = useState(false);

  const [userData, setUserData] = useState({
    // Basic info
    firstName: "",
    middleName: "",
    lastName: "",
    email: "", // No default email - must be set during signup
    controlNumber: "", // No default control number - generated during signup
    phone: "",
    profileImage: null, // Placeholder for profile image

    // Additional personal info
    age: 0,
    gender: "",
    civilStatus: "",
    birthdate: "",
    birthplace: "",
    religion: "",
    address: "",

    // Authentication and profile status
    emailVerified: false,
    hasCompletedProfile: false, // Flag to track if user has completed their profile

    // App settings
    notifications: true,
    dataSharing: false,
    twoFactorAuth: true,
    darkMode: false,
    language: "English",
    hidePersonalInfo: false,

    // Mood tracking
    moodSaved: false,

    // UX Enhancement states (Tesler's Law - hide complexity)
    isTransitioning: false,
    transitionType: null, // 'signin', 'signout', 'navigation'
    lastInteraction: Date.now(),
    isLoggedIn: false // Track login status for session management
  });

  // Session timeout constants (3 minutes = 180000ms)
  const SESSION_TIMEOUT = 3 * 60 * 1000; // 3 minutes in milliseconds
  const WARNING_TIME = 30 * 1000; // Show warning 30 seconds before timeout

  // Function to update user data with optional callback
  const updateUserData = (newData, callback) => {
    setUserData(prevData => {
      const updatedData = {
        ...prevData,
        ...newData
      };

      // If a callback was provided, call it with the updated data
      if (callback) {
        setTimeout(() => callback(updatedData), 0);
      }

      return updatedData;
    });
  };

  // Toggle hide personal info
  const toggleHidePersonalInfo = () => {
    setUserData(prevData => ({
      ...prevData,
      hidePersonalInfo: !prevData.hidePersonalInfo
    }));
  };

  // Set mood saved state
  const setMoodSaved = (saved) => {
    setUserData(prevData => ({
      ...prevData,
      moodSaved: saved
    }));
  };

  // UX Enhancement functions (Tesler's Law - simplify user interactions)
  const setTransitionState = (isTransitioning, transitionType = null) => {
    setUserData(prevData => ({
      ...prevData,
      isTransitioning,
      transitionType,
      lastInteraction: Date.now()
    }));
  };

  // Session timeout functions
  const resetSessionTimeout = () => {
    // Clear existing timeouts
    if (sessionTimeoutRef.current) {
      clearTimeout(sessionTimeoutRef.current);
    }
    if (warningTimeoutRef.current) {
      clearTimeout(warningTimeoutRef.current);
    }

    // Only set timeout if user is logged in
    if (userData.isLoggedIn) {
      // Set warning timeout (2.5 minutes)
      warningTimeoutRef.current = setTimeout(() => {
        setIsSessionWarningVisible(true);
        showSessionWarning();
      }, SESSION_TIMEOUT - WARNING_TIME);

      // Set session timeout (3 minutes)
      sessionTimeoutRef.current = setTimeout(() => {
        handleSessionTimeout();
      }, SESSION_TIMEOUT);
    }
  };

  const showSessionWarning = () => {
    Alert.alert(
      '⏰ Session Timeout Warning',
      'Your session will expire in 30 seconds due to inactivity. Tap "Stay Logged In" to continue.',
      [
        {
          text: 'Logout Now',
          style: 'destructive',
          onPress: () => {
            setIsSessionWarningVisible(false);
            handleSessionTimeout();
          }
        },
        {
          text: 'Stay Logged In',
          style: 'default',
          onPress: () => {
            setIsSessionWarningVisible(false);
            updateLastInteraction();
          }
        }
      ],
      { cancelable: false }
    );
  };

  const handleSessionTimeout = async () => {
    console.log('🔒 Session timeout - logging out user');
    setIsSessionWarningVisible(false);

    // Clear timeouts
    if (sessionTimeoutRef.current) {
      clearTimeout(sessionTimeoutRef.current);
    }
    if (warningTimeoutRef.current) {
      clearTimeout(warningTimeoutRef.current);
    }

    // Show timeout message
    Alert.alert(
      '🔒 Session Expired',
      'You have been logged out due to inactivity for security reasons.',
      [{ text: 'OK', onPress: () => router.replace('/(auth)/sign-in') }],
      { cancelable: false }
    );

    // Logout user
    await logout();
  };

  const updateLastInteraction = () => {
    setUserData(prevData => ({
      ...prevData,
      lastInteraction: Date.now()
    }));
    resetSessionTimeout();
  };

  // Function to clear user-specific AsyncStorage data to prevent data leakage between accounts
  const clearUserSpecificAsyncStorage = async () => {
    try {
      console.log('🧹 Clearing AsyncStorage to prevent data leakage between accounts...');

      // Assessment-related keys
      const assessmentKeys = [
        // DASS assessment
        'dass_saved_results',
        'dass_result_timestamp',
        'dass_answers',
        'dass_currentQuestion',
        'dass_progress',

        // PSS assessment
        'pss_saved_results',
        'pss_result_timestamp',
        'pss_answers',
        'pss_currentQuestion',
        'pss_progress',

        // Other potential assessments
        'gad7_saved_results',
        'gad7_result_timestamp',
        'gad7_answers',
        'gad7_currentQuestion',
        'gad7_progress',

        'phq9_saved_results',
        'phq9_result_timestamp',
        'phq9_answers',
        'phq9_currentQuestion',
        'phq9_progress',

        // Mood tracking
        'savedMoods',

        // Chat history (user-specific)
        'chatHistory'
      ];

      // Clear all assessment-related data
      await Promise.all(assessmentKeys.map(key => AsyncStorage.removeItem(key)));

      console.log('✅ AsyncStorage cleared successfully');
    } catch (error) {
      console.error('❌ Error clearing AsyncStorage:', error);
    }
  };

  // Session monitoring effect
  useEffect(() => {
    // Start session timeout when user logs in
    if (userData.isLoggedIn) {
      resetSessionTimeout();
    } else {
      // Clear timeouts when user logs out
      if (sessionTimeoutRef.current) {
        clearTimeout(sessionTimeoutRef.current);
      }
      if (warningTimeoutRef.current) {
        clearTimeout(warningTimeoutRef.current);
      }
    }

    // Cleanup on unmount
    return () => {
      if (sessionTimeoutRef.current) {
        clearTimeout(sessionTimeoutRef.current);
      }
      if (warningTimeoutRef.current) {
        clearTimeout(warningTimeoutRef.current);
      }
    };
  }, [userData.isLoggedIn]);

  // Logout with smooth transition
  const logout = async () => {
    setTransitionState(true, 'signout');

    // Clear session timeouts
    if (sessionTimeoutRef.current) {
      clearTimeout(sessionTimeoutRef.current);
    }
    if (warningTimeoutRef.current) {
      clearTimeout(warningTimeoutRef.current);
    }

    // Clear chat history for the current user
    try {
      const userId = userData?.id || userData?.email;
      if (userId) {
        await clearChatHistory(userId);
      }
    } catch (error) {
      console.error('Error clearing chat history on logout:', error);
    }

    // 🚨 CRITICAL: Clear AsyncStorage to prevent data leakage between accounts
    await clearUserSpecificAsyncStorage();

    // Reset user data after a brief delay for transition
    setTimeout(() => {
      setUserData({
        firstName: "",
        middleName: "",
        lastName: "",
        email: "", // Clear email on logout
        controlNumber: "", // Clear control number on logout
        phone: "",
        profileImage: null,
        age: 0,
        gender: "",
        civilStatus: "",
        birthdate: "",
        birthplace: "",
        religion: "",
        address: "",
        emailVerified: false,
        hasCompletedProfile: false,
        notifications: true,
        dataSharing: false,
        twoFactorAuth: true,
        darkMode: false,
        language: "English",
        hidePersonalInfo: false,
        moodSaved: false,
        isTransitioning: false,
        transitionType: null,
        lastInteraction: Date.now(),
        isLoggedIn: false
      });
    }, 300);
  };

  return (
    <UserContext.Provider value={{
      userData,
      updateUserData,
      toggleHidePersonalInfo,
      setMoodSaved,
      setTransitionState,
      logout,
      updateLastInteraction,
      isSessionWarningVisible
    }}>
      {children}
    </UserContext.Provider>
  );
};

// Custom hook to use the context
export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
