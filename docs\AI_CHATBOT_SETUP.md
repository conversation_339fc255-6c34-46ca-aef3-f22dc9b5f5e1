# AI Chatbot Integration Guide

## Overview
Your MentalEase app now includes a fully integrated AI chatbot (AIRA) powered by your trained OpenAI model. The chatbot provides empathetic mental health support and can be easily customized.

## Configuration

### Environment Variables (.env)
```
EXPO_PUBLIC_OPENAI_API_KEY=your-api-key-here
EXPO_PUBLIC_OPENAI_ORGANIZATION=your-org-id-here
```

### AI Configuration (app/config/aiConfig.js)
You can customize the AI behavior by modifying the configuration:

```javascript
export const AI_CONFIG = {
  model: 'gpt-3.5-turbo', // Change to your fine-tuned model ID
  maxTokens: 200,         // Response length
  temperature: 0.7,       // Creativity (0.0-1.0)
  // ... other settings
};
```

## Using Your Fine-Tuned Model

If you have a fine-tuned model, update the model ID in `app/config/aiConfig.js`:

```javascript
model: 'ft:gpt-3.5-turbo-1106:your-org:your-model-name:abc123'
```

## Features

### 1. Intelligent Responses
- Uses your trained OpenAI model for contextual responses
- Maintains conversation history for better context
- Provides mental health-focused support

### 2. Crisis Detection
- Automatically detects crisis keywords
- Shows emergency support alerts
- Encourages professional help when needed

### 3. Fallback System
- Graceful handling of API errors
- Offline-capable fallback responses
- Rate limiting protection

### 4. Quick Reply Options
- Pre-defined conversation starters
- Categorized by feelings, thoughts, and support needs
- Easy to customize in the UI component

## Customization Options

### 1. System Prompt
Modify the system prompt in `app/config/aiConfig.js` to change the AI's personality and behavior.

### 2. Crisis Keywords
Add or remove crisis detection keywords in the configuration.

### 3. Fallback Responses
Customize responses for when the API is unavailable.

### 4. UI Customization
- Update quick reply options in `app/ai-chatbot.jsx`
- Modify the welcome message
- Customize the chat interface styling

## Testing the Integration

1. Navigate to the AI Chatbot from the dashboard
2. Try sending messages to test the OpenAI integration
3. Test quick reply buttons
4. Verify crisis detection with test phrases (be careful with actual crisis keywords)

## Monitoring and Analytics

Consider adding:
- Conversation logging for improvement
- User satisfaction ratings
- Usage analytics
- Error monitoring

## Security Considerations

- API keys are stored in environment variables
- Crisis detection provides immediate support resources
- No sensitive user data is sent to OpenAI by default
- Consider implementing conversation encryption for production

## Troubleshooting

### Common Issues:
1. **API Key Issues**: Verify your OpenAI API key and organization ID
2. **Rate Limiting**: The app includes automatic fallback responses
3. **Network Issues**: Fallback responses ensure the app remains functional
4. **Model Access**: Ensure your API key has access to the specified model

### Error Handling:
The app includes comprehensive error handling:
- API quota exceeded
- Rate limiting
- Network connectivity issues
- Invalid responses

## Next Steps

1. Test the chatbot thoroughly
2. Customize the system prompt for your specific use case
3. Add conversation persistence if needed
4. Implement user feedback collection
5. Monitor usage and improve based on user interactions

## Support

For technical issues:
- Check the console logs for detailed error messages
- Verify environment variables are properly set
- Ensure OpenAI API key has sufficient credits
- Test with a simple message first
