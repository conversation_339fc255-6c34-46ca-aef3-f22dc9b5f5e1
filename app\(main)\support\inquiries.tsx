import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  TextInput,
  Platform,
  Keyboard,
  TouchableWithoutFeedback,
  Alert,
  Animated,
  Dimensions
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import BottomNavigation from '../../components/BottomNavigation';
import KeyboardAwareScrollView from '../../components/KeyboardAwareScrollView';

// Type definitions
interface Category {
  id: number;
  title: string;
  icon: string;
}

interface Inquiry {
  id: number;
  subject: string;
  date: string;
  status: 'answered' | 'pending';
  category: string;
  message: string;
  response: string | null;
}

const Inquiries = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'new' | 'past'>('new');
  const [subject, setSubject] = useState<string>('');
  const [message, setMessage] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);

  // UX Enhancement states
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});
  const [showSuccess, setShowSuccess] = useState<boolean>(false);
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));

  // Auto-save functionality (Tesler's Law)
  useEffect(() => {
    const timer = setTimeout(() => {
      // Auto-save form data to local storage or state
      if (subject || message || selectedCategory) {
        // In a real app, this would save to AsyncStorage
        console.log('Auto-saving form data...');
      }
    }, 2000);

    return () => clearTimeout(timer);
  }, [subject, message, selectedCategory]);

  // Entrance animation (Aesthetic-Usability Effect)
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Miller's Law: Organize in logical groups (5±2 items)
  const categories: Category[] = [
    { id: 1, title: 'Medical Services', icon: 'medical-outline' },
    { id: 2, title: 'Appointments', icon: 'calendar-outline' },
    { id: 3, title: 'Assessments', icon: 'clipboard-outline' },
    { id: 4, title: 'Billing & Payments', icon: 'card-outline' },
    { id: 5, title: 'General Support', icon: 'help-circle-outline' },
  ];

  const pastInquiries: Inquiry[] = [
    {
      id: 1,
      subject: 'Question about assessment results',
      date: '2023-05-10',
      status: 'answered',
      category: 'Assessments',
      message: 'I have a question about interpreting my DASS assessment results. Could you provide more information?',
      response: 'Thank you for your inquiry. The DASS assessment measures depression, anxiety, and stress levels. Your results indicate mild levels of anxiety. This is not a clinical diagnosis but can help guide your mental health journey. We recommend discussing these results with a professional for personalized advice.'
    },
    {
      id: 2,
      subject: 'Appointment rescheduling',
      date: '2023-04-25',
      status: 'answered',
      category: 'Appointments',
      message: 'I need to reschedule my appointment on April 30th. What are the available slots for the following week?',
      response: 'We have rescheduled your appointment to May 5th at 2:00 PM as requested. You will receive a confirmation email shortly. Please let us know if you need to make any further changes.'
    },
    {
      id: 3,
      subject: 'Payment method update',
      date: '2023-04-15',
      status: 'pending',
      category: 'Payments',
      message: 'I would like to update my payment method from credit card to debit card. How can I do this?',
      response: null
    },
  ];

  const [expandedInquiry, setExpandedInquiry] = useState<number | null>(null);

  // Dismiss keyboard when tapping outside input fields
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  // Enhanced form validation (Tesler's Law - move complexity behind scenes)
  const validateForm = () => {
    const errors: {[key: string]: string} = {};

    if (!selectedCategory) {
      errors.category = 'Please select a category for your inquiry';
    }

    if (!subject.trim()) {
      errors.subject = 'Subject is required';
    } else if (subject.trim().length < 5) {
      errors.subject = 'Subject must be at least 5 characters';
    }

    if (!message.trim()) {
      errors.message = 'Please describe your inquiry';
    } else if (message.trim().length < 10) {
      errors.message = 'Please provide more details (at least 10 characters)';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      // Shake animation for invalid form
      Animated.sequence([
        Animated.timing(slideAnim, { toValue: -10, duration: 100, useNativeDriver: true }),
        Animated.timing(slideAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(slideAnim, { toValue: 0, duration: 100, useNativeDriver: true }),
      ]).start();
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Peak-End Rule: Create memorable success moment
      setShowSuccess(true);

      // Success animation
      Animated.sequence([
        Animated.timing(fadeAnim, { toValue: 0, duration: 200, useNativeDriver: true }),
        Animated.timing(fadeAnim, { toValue: 1, duration: 400, useNativeDriver: true }),
      ]).start();

      // Clear form after success
      setTimeout(() => {
        setSelectedCategory(null);
        setSubject('');
        setMessage('');
        setFormErrors({});
        setShowSuccess(false);
        setIsSubmitting(false);
      }, 2000);

    } catch (error) {
      setIsSubmitting(false);
      Alert.alert('Error', 'Failed to submit inquiry. Please try again.');
    }
  };

  const toggleInquiry = (id: number) => {
    if (expandedInquiry === id) {
      setExpandedInquiry(null);
    } else {
      setExpandedInquiry(id);
    }
  };

  const renderNewInquiry = () => (
    <Animated.View
      style={[
        styles.tabContent,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}
    >
      {showSuccess ? (
        // Peak-End Rule: Memorable success state
        <View style={styles.successContainer}>
          <Animated.View style={[styles.successIcon, { opacity: fadeAnim }]}>
            <Ionicons name="checkmark-circle" size={80} color="#4CAF50" />
          </Animated.View>
          <Text style={styles.successTitle}>Inquiry Submitted!</Text>
          <Text style={styles.successMessage}>
            Thank you for reaching out. We'll respond within 24-48 hours.
          </Text>
        </View>
      ) : (
        <>
          {/* Law of Proximity: Group related elements */}
          <View style={styles.formSection}>
            <Text style={styles.sectionTitle}>Submit a New Inquiry</Text>
            <Text style={styles.sectionSubtitle}>
              We're here to help! Choose a category and describe your inquiry.
            </Text>
          </View>

          {/* Category Selection - Improved for Fitts's Law */}
          <View style={styles.formSection}>
            <Text style={styles.formLabel}>
              What can we help you with? *
            </Text>
            {formErrors.category && (
              <Text style={styles.errorText}>{formErrors.category}</Text>
            )}
            <View style={styles.categoriesGrid}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.categoryCard,
                    selectedCategory === category.id && styles.selectedCategoryCard
                  ]}
                  onPress={() => {
                    setSelectedCategory(category.id);
                    setFormErrors(prev => ({ ...prev, category: '' }));
                  }}
                  activeOpacity={0.7}
                >
                  <Ionicons
                    name={category.icon as any}
                    size={28}
                    color={selectedCategory === category.id ? '#FFFFFF' : '#4CAF50'}
                    style={styles.categoryCardIcon}
                  />
                  <Text
                    style={[
                      styles.categoryCardText,
                      selectedCategory === category.id && styles.selectedCategoryCardText
                    ]}
                  >
                    {category.title}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Subject Input */}
          <View style={styles.formSection}>
            <Text style={styles.formLabel}>Subject *</Text>
            {formErrors.subject && (
              <Text style={styles.errorText}>{formErrors.subject}</Text>
            )}
            <TextInput
              style={[
                styles.input,
                formErrors.subject && styles.inputError
              ]}
              value={subject}
              onChangeText={(text) => {
                setSubject(text);
                setFormErrors(prev => ({ ...prev, subject: '' }));
              }}
              placeholder="Brief description of your inquiry"
              placeholderTextColor="#999999"
              maxLength={100}
            />
            <Text style={styles.characterCount}>{subject.length}/100</Text>
          </View>

          {/* Message Input */}
          <View style={styles.formSection}>
            <Text style={styles.formLabel}>Detailed Message *</Text>
            {formErrors.message && (
              <Text style={styles.errorText}>{formErrors.message}</Text>
            )}
            <TextInput
              style={[
                styles.messageInput,
                formErrors.message && styles.inputError
              ]}
              value={message}
              onChangeText={(text) => {
                setMessage(text);
                setFormErrors(prev => ({ ...prev, message: '' }));
              }}
              placeholder="Please provide detailed information about your inquiry..."
              placeholderTextColor="#999999"
              multiline
              textAlignVertical="top"
              maxLength={500}
            />
            <Text style={styles.characterCount}>{message.length}/500</Text>
          </View>

          {/* Submit Button - Enhanced for Fitts's Law */}
          <TouchableOpacity
            style={[
              styles.submitButton,
              isSubmitting && styles.submitButtonDisabled
            ]}
            onPress={handleSubmit}
            disabled={isSubmitting}
            activeOpacity={0.8}
          >
            {isSubmitting ? (
              <View style={styles.submitButtonContent}>
                <Animated.View style={styles.loadingSpinner}>
                  <Ionicons name="hourglass-outline" size={20} color="#FFFFFF" />
                </Animated.View>
                <Text style={styles.submitButtonText}>Submitting...</Text>
              </View>
            ) : (
              <View style={styles.submitButtonContent}>
                <Ionicons name="send" size={20} color="#FFFFFF" style={styles.submitButtonIcon} />
                <Text style={styles.submitButtonText}>Submit Inquiry</Text>
              </View>
            )}
          </TouchableOpacity>
        </>
      )}
    </Animated.View>
  );

  const renderPastInquiries = () => (
    <Animated.View
      style={[
        styles.tabContent,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}
    >
      <View style={styles.formSection}>
        <Text style={styles.sectionTitle}>Past Inquiries</Text>
        <Text style={styles.sectionSubtitle}>
          View your previous inquiries and our responses.
        </Text>
      </View>

      {pastInquiries.length === 0 ? (
        <View style={styles.emptyState}>
          <Ionicons name="mail-open-outline" size={64} color="#CCCCCC" />
          <Text style={styles.emptyStateTitle}>No inquiries yet</Text>
          <Text style={styles.emptyStateText}>
            You haven't submitted any inquiries yet. Use the "New Inquiry" tab to get started.
          </Text>
        </View>
      ) : (
        pastInquiries.map((inquiry) => (
          <TouchableOpacity
            key={inquiry.id}
            style={[
              styles.inquiryItem,
              expandedInquiry === inquiry.id && styles.inquiryItemExpanded
            ]}
            onPress={() => toggleInquiry(inquiry.id)}
            activeOpacity={0.7}
          >
            <View style={styles.inquiryHeader}>
              <View style={styles.inquiryHeaderLeft}>
                <Text style={styles.inquirySubject}>{inquiry.subject}</Text>
                <Text style={styles.inquiryMeta}>
                  {new Date(inquiry.date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                  })} • {inquiry.category}
                </Text>
              </View>
              <View style={styles.inquiryHeaderRight}>
                <View style={[
                  styles.statusBadge,
                  { backgroundColor: inquiry.status === 'answered' ? '#E8F5E8' : '#FFF3E0' }
                ]}>
                  <View style={[
                    styles.statusIndicator,
                    { backgroundColor: inquiry.status === 'answered' ? '#4CAF50' : '#FF9800' }
                  ]} />
                  <Text style={[
                    styles.statusText,
                    { color: inquiry.status === 'answered' ? '#4CAF50' : '#FF9800' }
                  ]}>
                    {inquiry.status === 'answered' ? 'Answered' : 'Pending'}
                  </Text>
                </View>
                <Ionicons
                  name={expandedInquiry === inquiry.id ? "chevron-up" : "chevron-down"}
                  size={20}
                  color="#999999"
                  style={styles.expandIcon}
                />
              </View>
            </View>

            {expandedInquiry === inquiry.id && (
              <Animated.View style={styles.inquiryDetails}>
                <View style={styles.messageContainer}>
                  <Text style={styles.messageLabel}>Your Message:</Text>
                  <Text style={styles.messageText}>{inquiry.message}</Text>
                </View>

                {inquiry.response && (
                  <View style={styles.responseContainer}>
                    <Text style={styles.responseLabel}>Our Response:</Text>
                    <Text style={styles.responseText}>{inquiry.response}</Text>
                  </View>
                )}
              </Animated.View>
            )}
          </TouchableOpacity>
        ))
      )}
    </Animated.View>
  );

  return (
    <TouchableWithoutFeedback onPress={dismissKeyboard}>
      <SafeAreaView style={styles.container}>
        <StatusBar
          translucent={true}
          backgroundColor="#66948a"
          barStyle="light-content"
        />
        
        {/* Header */}
        <LinearGradient
          colors={['#66948a', '#66948a']}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Inquiries</Text>
            <View style={styles.placeholderButton} />
          </View>
        </LinearGradient>

        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'new' && styles.activeTab]}
            onPress={() => setActiveTab('new')}
          >
            <Text style={[styles.tabText, activeTab === 'new' && styles.activeTabText]}>
              New Inquiry
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'past' && styles.activeTab]}
            onPress={() => setActiveTab('past')}
          >
            <Text style={[styles.tabText, activeTab === 'past' && styles.activeTabText]}>
              Past Inquiries
            </Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <KeyboardAwareScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.tabContent}
          extraScrollHeight={50}
          enableOnAndroid={true}
        >
          {activeTab === 'new' && renderNewInquiry()}
          {activeTab === 'past' && renderPastInquiries()}
        </KeyboardAwareScrollView>

        {/* Bottom Navigation */}
        <BottomNavigation />
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    // Extend fully into status bar area (User preference)
    paddingTop: Platform.OS === 'ios' ? 60 : (StatusBar.currentHeight || 0) + 20,
    paddingBottom: 20,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
    // Enhanced shadow for better visual hierarchy
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    minHeight: 44,
  },
  backButton: {
    width: 44, // Fitts's Law: Larger touch target
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    // Enhanced visual feedback
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  placeholderButton: {
    width: 44,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingVertical: 6,
    paddingHorizontal: 6,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tab: {
    flex: 1,
    paddingVertical: 14, // Fitts's Law: Larger touch target
    paddingHorizontal: 16,
    alignItems: 'center',
    borderRadius: 12,
    minHeight: 48, // Fitts's Law: Minimum touch target
  },
  activeTab: {
    backgroundColor: '#4CAF50',
    shadowColor: '#4CAF50',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 2,
  },
  tabText: {
    fontSize: 15,
    color: '#666666',
    fontWeight: '600',
  },
  activeTabText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  tabContent: {
    padding: 16,
  },

  // Success State Styles (Peak-End Rule)
  successContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    minHeight: 300,
  },
  successIcon: {
    marginBottom: 20,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 12,
    textAlign: 'center',
  },
  successMessage: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
  },

  // Form Section Styles (Law of Proximity)
  formSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 15,
    color: '#666666',
    lineHeight: 22,
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },

  // Error Handling Styles (Tesler's Law)
  errorText: {
    fontSize: 13,
    color: '#FF5252',
    marginBottom: 8,
    marginTop: 4,
  },

  // Enhanced Category Styles (Fitts's Law + Aesthetic-Usability Effect + Miller's Law)
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
    gap: 14,
    justifyContent: 'space-between',
  },
  categoryCard: {
    flexDirection: 'column',
    alignItems: 'center',
    backgroundColor: 'rgba(76, 175, 80, 0.08)',
    borderRadius: 18,
    paddingVertical: 18,
    paddingHorizontal: 14,
    // Fitts's Law: Optimal touch target size
    minWidth: 95,
    minHeight: 85,
    // Miller's Law: Consistent spacing for visual grouping
    flex: 0.48,
    maxWidth: '48%',
    borderWidth: 2,
    borderColor: 'transparent',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedCategoryCard: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
    transform: [{ scale: 1.02 }],
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  categoryCardIcon: {
    marginBottom: 8,
  },
  categoryCardText: {
    fontSize: 12,
    color: '#4CAF50',
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: 16,
  },
  selectedCategoryCardText: {
    color: '#FFFFFF',
  },

  // Legacy category styles (keeping for compatibility)
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedCategoryButton: {
    backgroundColor: '#4CAF50',
  },
  categoryIcon: {
    marginRight: 6,
  },
  categoryText: {
    fontSize: 13,
    color: '#4CAF50',
    fontWeight: '500',
  },
  selectedCategoryText: {
    color: '#FFFFFF',
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 15,
    color: '#333333',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  inputError: {
    borderColor: '#FF5252',
    borderWidth: 2,
  },
  characterCount: {
    fontSize: 12,
    color: '#999999',
    textAlign: 'right',
    marginTop: 4,
  },
  messageInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 15,
    color: '#333333',
    height: 120,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  submitButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 28,
    paddingVertical: 18, // Fitts's Law: Larger touch target
    paddingHorizontal: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 32,
    marginBottom: 16,
    minHeight: 56, // Fitts's Law: Minimum touch target size
    shadowColor: '#4CAF50',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  submitButtonDisabled: {
    backgroundColor: '#CCCCCC',
    shadowOpacity: 0.1,
    elevation: 2,
  },
  submitButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  submitButtonIcon: {
    marginRight: 8,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingSpinner: {
    marginRight: 8,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 48,
    marginTop: 32,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666666',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 15,
    color: '#999999',
    textAlign: 'center',
    lineHeight: 22,
    maxWidth: 280,
  },
  inquiryItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#F0F0F0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  inquiryItemExpanded: {
    borderColor: '#4CAF50',
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
  },
  inquiryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  inquiryHeaderLeft: {
    flex: 1,
    marginRight: 16,
  },
  inquiryHeaderRight: {
    alignItems: 'flex-end',
  },
  inquirySubject: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  inquiryMeta: {
    fontSize: 13,
    color: '#999999',
    marginTop: 4,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 12,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  expandIcon: {
    marginLeft: 8,
  },
  inquiryDetails: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  messageContainer: {
    marginBottom: 16,
  },
  messageLabel: {
    fontSize: 13,
    fontWeight: '600',
    color: '#666666',
    marginBottom: 4,
  },
  messageText: {
    fontSize: 14,
    color: '#333333',
    lineHeight: 20,
  },
  responseContainer: {
    backgroundColor: 'rgba(76, 175, 80, 0.05)',
    borderRadius: 8,
    padding: 12,
  },
  responseLabel: {
    fontSize: 13,
    fontWeight: '600',
    color: '#4CAF50',
    marginBottom: 4,
  },
  responseText: {
    fontSize: 14,
    color: '#333333',
    lineHeight: 20,
  },
});

export default Inquiries;
