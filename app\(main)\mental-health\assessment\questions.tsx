import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  StatusBar,
  Animated,
  ActivityIndicator,
  Dimensions,
  ScrollView,
  Platform
} from 'react-native';
import { useState, useEffect, useRef } from 'react';
import { useRouter, useLocalSearchParams } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import StandardHeader from '../../../components/StandardHeader';
import { useUser } from '../../../context/UserContext';

const AssessmentQuestions = () => {
  const router = useRouter();
  const { id, fresh } = useLocalSearchParams();
  const { userData } = useUser();
  const assessmentId = Array.isArray(id) ? id[0] : id; // Ensure we have a string
  const isFreshStart = fresh === 'true'; // Check if this is a fresh start
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState([]);
  const [assessment, setAssessment] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  
  // Complete DASS-21 and PSS-10 assessment data
  const assessmentData = {
    'dass': {
      title: 'DASS-21 (Depression Anxiety Stress Scales)',
      description: 'Please read each statement and select the option that indicates how much the statement applied to you over the past week. There are no right or wrong answers.',
      options: [
        { value: 0, label: 'Did not apply to me at all' },
        { value: 1, label: 'Applied to me to some degree, or some of the time' },
        { value: 2, label: 'Applied to me to a considerable degree or a good part of time' },
        { value: 3, label: 'Applied to me very much or most of the time' }
      ],
      questions: [
        'I found it hard to wind down',
        'I was aware of dryness of my mouth',
        'I couldn\'t seem to experience any positive feeling at all',
        'I experienced breathing difficulty (e.g. excessively rapid breathing, breathlessness in the absence of physical exertion)',
        'I found it difficult to work up the initiative to do things',
        'I tended to over-react to situations',
        'I experienced trembling (e.g. in the hands)',
        'I felt that I was using a lot of nervous energy',
        'I was worried about situations in which I might panic and make a fool of myself',
        'I felt that I had nothing to look forward to',
        'I found myself getting agitated',
        'I found it difficult to relax',
        'I felt down-hearted and blue',
        'I was intolerant of anything that kept me from getting on with what I was doing',
        'I felt I was close to panic',
        'I was unable to become enthusiastic about anything',
        'I felt I wasn\'t worth much as a person',
        'I felt that I was rather touchy',
        'I was aware of the action of my heart in the absence of physical exertion (e.g. sense of heart rate increase, heart missing a beat)',
        'I felt scared without any good reason',
        'I felt that life was meaningless'
      ],
      // DASS-21 subscale mapping: Depression (3,5,10,13,16,17,21), Anxiety (2,4,7,9,15,19,20), Stress (1,6,8,11,12,14,18)
      subscales: {
        depression: [2, 4, 9, 12, 15, 16, 20], // 0-indexed
        anxiety: [1, 3, 6, 8, 14, 18, 19],
        stress: [0, 5, 7, 10, 11, 13, 17]
      }
    },
    'pss': {
      title: 'PSS-10 (Perceived Stress Scale)',
      description: 'The questions in this scale ask you about your feelings and thoughts during the last month. In each case, you will be asked to indicate how often you felt or thought a certain way.',
      options: [
        { value: 0, label: 'Never' },
        { value: 1, label: 'Almost Never' },
        { value: 2, label: 'Sometimes' },
        { value: 3, label: 'Fairly Often' },
        { value: 4, label: 'Very Often' }
      ],
      questions: [
        'In the last month, how often have you been upset because of something that happened unexpectedly?',
        'In the last month, how often have you felt that you were unable to control the important things in your life?',
        'In the last month, how often have you felt nervous and "stressed"?',
        'In the last month, how often have you felt confident about your ability to handle your personal problems?',
        'In the last month, how often have you felt that things were going your way?',
        'In the last month, how often have you found that you could not cope with all the things that you had to do?',
        'In the last month, how often have you been able to control irritations in your life?',
        'In the last month, how often have you felt that you were on top of things?',
        'In the last month, how often have you been angered because of things that were outside of your control?',
        'In the last month, how often have you felt difficulties were piling up so high that you could not overcome them?'
      ],
      // PSS-10 reverse scored items: 4, 5, 7, 8 (0-indexed: 3, 4, 6, 7)
      reverseScored: [3, 4, 6, 7]
    }
  };

  useEffect(() => {
    const initializeAssessment = async () => {
      setIsLoading(true);

      // Simulate loading time for better UX (Peak-End Rule)
      await new Promise(resolve => setTimeout(resolve, 800));

      // Set the assessment based on the ID and load saved answers
      if (assessmentId && assessmentData[assessmentId]) {
        setAssessment(assessmentData[assessmentId]);
        // Load saved answers if they exist
        await loadSavedAnswers();
      } else {
        // Handle invalid assessment ID
        Alert.alert('Error', 'Invalid assessment selected');
        router.back();
        return;
      }

      setIsLoading(false);
    };

    initializeAssessment();
  }, [assessmentId]);

  // Load saved answers from AsyncStorage (or start fresh if requested)
  const loadSavedAnswers = async () => {
    try {
      // If this is a fresh start, clear any previous data and start clean
      if (isFreshStart) {
        console.log('🆕 Starting fresh assessment - clearing previous data');
        await AsyncStorage.removeItem(`${assessmentId}_answers`);
        await AsyncStorage.removeItem(`${assessmentId}_currentQuestion`);

        // Initialize with fresh answers array
        if (assessmentId) {
          setAnswers(new Array(assessmentData[assessmentId].questions.length).fill(null));
          setCurrentQuestion(0);
        }
        return;
      }

      // Otherwise, try to load saved progress
      const savedAnswers = await AsyncStorage.getItem(`${assessmentId}_answers`);
      const savedQuestion = await AsyncStorage.getItem(`${assessmentId}_currentQuestion`);

      if (savedAnswers) {
        const parsedAnswers = JSON.parse(savedAnswers);
        setAnswers(parsedAnswers);

        // If we have a saved question position, use it
        if (savedQuestion) {
          const questionIndex = parseInt(savedQuestion, 10);
          setCurrentQuestion(questionIndex);

          // If all questions are answered, just reset to start fresh assessment
          if (assessmentId && questionIndex >= assessmentData[assessmentId].questions.length - 1 &&
              !parsedAnswers.includes(null)) {
            // User has already completed this assessment, reset to start fresh
            resetAssessment();
          }
        }
        console.log('📋 Loaded saved progress');
      } else {
        // Initialize answers array with nulls
        if (assessmentId) {
          setAnswers(new Array(assessmentData[assessmentId].questions.length).fill(null));
        }
        console.log('🆕 No saved progress, starting fresh');
      }
    } catch (error) {
      console.error('Error loading saved answers:', error);
      // Initialize answers array with nulls as fallback
      if (assessmentId) {
        setAnswers(new Array(assessmentData[assessmentId].questions.length).fill(null));
      }
    }
  };

  // Save current answers and question position
  const saveProgress = async (newAnswers: any[], questionIndex: number) => {
    try {
      await AsyncStorage.setItem(`${assessmentId}_answers`, JSON.stringify(newAnswers));
      await AsyncStorage.setItem(`${assessmentId}_currentQuestion`, questionIndex.toString());
    } catch (error) {
      console.error('Error saving progress:', error);
      Alert.alert('Error', 'Failed to save your progress');
    }
  };

  // Reset the assessment
  const resetAssessment = async () => {
    try {
      await AsyncStorage.removeItem(`${assessmentId}_answers`);
      await AsyncStorage.removeItem(`${assessmentId}_currentQuestion`);
      if (assessmentId) {
        setAnswers(new Array(assessmentData[assessmentId].questions.length).fill(null));
      }
      setCurrentQuestion(0);
    } catch (error) {
      console.error('Error resetting assessment:', error);
    }
  };

  // Smooth transition animation between questions
  const animateTransition = () => {
    setIsTransitioning(true);

    // Fade out current question
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      // Slide in new question
      slideAnim.setValue(-50);
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => {
        setIsTransitioning(false);
      });
    });
  };

  const handleAnswer = (value: any) => {
    console.log(`🎯 Question ${currentQuestion + 1} answered with value:`, value);

    const newAnswers = [...answers];
    newAnswers[currentQuestion] = value;
    setAnswers(newAnswers);

    console.log('📝 Updated answers array:', newAnswers);
    console.log('📊 Current progress:', `${currentQuestion + 1}/${assessment.questions.length}`);

    // Save progress
    const nextQuestion = currentQuestion + 1;
    saveProgress(newAnswers, nextQuestion < assessment.questions.length ? nextQuestion : currentQuestion);

    // Animate transition and move to next question or show results
    if (currentQuestion < assessment.questions.length - 1) {
      animateTransition();
      setTimeout(() => {
        setCurrentQuestion(nextQuestion);
      }, 200);
    } else {
      // Show completion animation before navigating
      console.log('🎉 Assessment completed! Final answers:', newAnswers);
      setIsTransitioning(true);
      setTimeout(async () => {
        const results = calculateResults(newAnswers);

        // Automatically save results to database for retakes
        await saveResultsToDatabase(results);

        router.push({
          pathname: '/(main)/mental-health/assessment/results',
          params: {
            id: assessmentId,
            results: JSON.stringify(results),
            refresh: Date.now() // Force refresh to show latest results
          }
        });
      }, 500);
    }
  };

  // Add a function to handle navigation between questions
  const navigateToQuestion = (index: number) => {
    if (index >= 0 && index < assessment.questions.length) {
      setCurrentQuestion(index);
      saveProgress(answers, index);
    }
  };

  const calculateResults = (answers: any[]) => {
    console.log('🧮 Calculating results for:', assessmentId);
    console.log('📊 Answers array:', answers);
    console.log('📏 Answers length:', answers.length);

    if (assessmentId === 'dass') {
      // DASS-21 proper scoring algorithm
      const subscales = assessmentData[assessmentId].subscales;
      console.log('📋 DASS subscales:', subscales);

      // Calculate subscale scores
      const depressionScore = subscales.depression.reduce((sum: number, index: number) => {
        const value = answers[index] || 0;
        console.log(`Depression Q${index + 1}: ${value}`);
        return sum + value;
      }, 0) * 2;

      const anxietyScore = subscales.anxiety.reduce((sum: number, index: number) => {
        const value = answers[index] || 0;
        console.log(`Anxiety Q${index + 1}: ${value}`);
        return sum + value;
      }, 0) * 2;

      const stressScore = subscales.stress.reduce((sum: number, index: number) => {
        const value = answers[index] || 0;
        console.log(`Stress Q${index + 1}: ${value}`);
        return sum + value;
      }, 0) * 2;

      const results = {
        depression: {
          score: depressionScore,
          level: getDassLevel('depression', depressionScore)
        },
        anxiety: {
          score: anxietyScore,
          level: getDassLevel('anxiety', anxietyScore)
        },
        stress: {
          score: stressScore,
          level: getDassLevel('stress', stressScore)
        }
      };

      console.log('✅ DASS Results calculated:', results);
      return results;

    } else if (assessmentId === 'pss') {
      // PSS-10 proper scoring with reverse scoring
      const reverseScored = assessmentData[assessmentId].reverseScored;
      console.log('🔄 PSS reverse scored items:', reverseScored);
      let totalScore = 0;

      answers.forEach((answer: number, index: number) => {
        const rawAnswer = answer || 0;
        if (reverseScored.includes(index)) {
          // Reverse score: 0=4, 1=3, 2=2, 3=1, 4=0
          const reversedScore = 4 - rawAnswer;
          totalScore += reversedScore;
          console.log(`PSS Q${index + 1} (REVERSED): ${rawAnswer} → ${reversedScore}`);
        } else {
          totalScore += rawAnswer;
          console.log(`PSS Q${index + 1}: ${rawAnswer}`);
        }
      });

      const results = {
        stress: {
          score: totalScore,
          level: getPssLevel(totalScore)
        }
      };

      console.log('✅ PSS Results calculated:', results);
      return results;
    }

    console.log('❌ Unknown assessment type:', assessmentId);
    return {};
  };

  // DASS-21 severity level calculation based on official scoring
  const getDassLevel = (subscale: string, score: number) => {
    const thresholds = {
      depression: { mild: 10, moderate: 14, severe: 21, extremelySevere: 28 },
      anxiety: { mild: 8, moderate: 10, severe: 15, extremelySevere: 20 },
      stress: { mild: 15, moderate: 19, severe: 26, extremelySevere: 34 }
    };

    const threshold = thresholds[subscale as keyof typeof thresholds];
    if (score < threshold.mild) return 'Normal';
    if (score < threshold.moderate) return 'Mild';
    if (score < threshold.severe) return 'Moderate';
    if (score < threshold.extremelySevere) return 'Severe';
    return 'Extremely Severe';
  };

  // PSS-10 severity level calculation
  const getPssLevel = (score: number) => {
    if (score <= 13) return 'Low Stress';
    if (score <= 26) return 'Moderate Stress';
    return 'High Stress';
  };

  // Save results to database automatically
  const saveResultsToDatabase = async (results: any) => {
    try {
      console.log('🔍 Checking user data:', userData);

      if (!userData || !userData.email) {
        console.log('❌ No user email found, skipping database save');
        console.log('User data:', userData);
        return;
      }

      // Use the correct Supabase configuration from environment variables
      const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://ifeqwtegvozyvrhlrffn.supabase.co';
      const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlmZXF3dGVndm96eXZyaGxyZmZuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0MTczNDgsImV4cCI6MjA2Mzk5MzM0OH0.LtyqtPn-h5zxQq2YBLvzU2Km2f3yx4duv2jJMe91WM8';

      console.log('💾 Saving assessment results to database...');
      console.log('📧 User email:', userData.email);
      console.log('📋 Assessment type:', assessmentId);
      console.log('📊 Results to save:', results);

      const payload = {
        user_email: userData.email,
        assessment_type: assessmentId,
        results: JSON.stringify(results),
        completed_at: new Date().toISOString(),
        scores: JSON.stringify({
          // Extract main scores for easy querying
          depression: results.depression?.score || null,
          anxiety: results.anxiety?.score || null,
          stress: results.stress?.score || null,
          total_score: results.stress?.score || results.total_score || null
        })
      };

      console.log('📦 Payload:', payload);

      const response = await fetch(`${supabaseUrl}/rest/v1/assessment_results`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
          'Authorization': `Bearer ${supabaseAnonKey}`,
          'Prefer': 'return=representation'
        },
        body: JSON.stringify(payload)
      });

      console.log('📡 Response status:', response.status);
      console.log('📡 Response headers:', response.headers);

      if (response.ok) {
        const savedResult = await response.json();
        console.log('✅ Assessment results saved to database:', savedResult);
      } else {
        const errorText = await response.text();
        console.error('❌ Failed to save to database. Status:', response.status);
        console.error('❌ Error response:', errorText);
      }
    } catch (error) {
      console.error('❌ Database save error:', error);
      console.error('❌ Error details:', error.message);
    }
  };

  // Handle exit with confirmation
  const handleGoBack = () => {
    // Check if user has started answering
    const hasAnswers = answers.some(answer => answer !== null && answer !== undefined);

    if (hasAnswers) {
      Alert.alert(
        'Exit Assessment',
        'Your progress has been saved. You can continue this assessment later from where you left off.',
        [
          {
            text: 'Continue Assessment',
            style: 'cancel'
          },
          {
            text: 'Exit',
            style: 'destructive',
            onPress: () => router.back()
          }
        ]
      );
    } else {
      router.back();
    }
  };

  // Loading screen with beautiful animation
  if (isLoading || !assessment) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar backgroundColor="#66948a" barStyle="light-content" translucent />
        <View style={styles.loadingContainer}>
          <View style={styles.loadingContent}>
            <ActivityIndicator size="large" color="#6B9142" />
            <Text style={styles.loadingTitle}>Preparing Your Assessment</Text>
            <Text style={styles.loadingSubtitle}>
              {assessmentId === 'dass' ? 'DASS-21 Depression, Anxiety & Stress Scale' : 'PSS-10 Perceived Stress Scale'}
            </Text>
            <View style={styles.loadingTips}>
              <Text style={styles.tipText}>💡 Answer honestly for accurate results</Text>
              <Text style={styles.tipText}>🔒 Your responses are private and secure</Text>
              <Text style={styles.tipText}>⏱️ Takes about 5-10 minutes to complete</Text>
            </View>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <View style={styles.container}>
      <StandardHeader
        title={assessment.title}
        onBackPress={handleGoBack}
      />

      {/* Enhanced Progress Section */}
      <View style={styles.progressSection}>
        <View style={styles.progressHeader}>
          <Text style={styles.progressTitle}>Assessment Progress</Text>
          <Text style={styles.progressCounter}>
            {currentQuestion + 1} of {assessment.questions.length}
          </Text>
        </View>

        <View style={styles.progressBarContainer}>
          <View style={styles.progressBar}>
            <Animated.View
              style={[
                styles.progressFill,
                { width: `${((currentQuestion + 1) / assessment.questions.length) * 100}%` }
              ]}
            />
          </View>
          <Text style={styles.progressPercentage}>
            {Math.round(((currentQuestion + 1) / assessment.questions.length) * 100)}%
          </Text>
        </View>
      </View>

      {/* Scrollable Content */}
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Formal Question Display */}
        <View style={styles.questionSection}>
          <Animated.View
            style={[
              styles.questionCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
              }
            ]}
          >
            <View style={styles.questionHeader}>
              <Text style={styles.questionLabel}>Question {currentQuestion + 1}</Text>
              <View style={styles.questionBadge}>
                <Text style={styles.questionBadgeText}>
                  {assessmentId === 'dass' ? 'DASS-21' : 'PSS-10'}
                </Text>
              </View>
            </View>

            <Text style={styles.questionText}>
              {assessment.questions[currentQuestion]}
            </Text>

            <Text style={styles.questionInstruction}>
              {assessmentId === 'dass'
                ? 'How much did this apply to you over the past week?'
                : 'How often have you felt this way in the last month?'
              }
            </Text>
          </Animated.View>
        </View>

        {/* Formal Options Container */}
        <View style={styles.optionsSection}>
          <Text style={styles.optionsTitle}>Select your response:</Text>

          <View style={styles.optionsContainer}>
            {assessment.options.map((option: any, index: number) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.optionButton,
                  answers[currentQuestion] === option.value && styles.selectedOption,
                  isTransitioning && styles.disabledOption
                ]}
                onPress={() => !isTransitioning && handleAnswer(option.value)}
                disabled={isTransitioning}
                activeOpacity={0.8}
              >
                <View style={styles.optionContent}>
                  <View style={[
                    styles.optionIndicator,
                    answers[currentQuestion] === option.value && styles.selectedIndicator
                  ]}>
                    <Text style={[
                      styles.optionNumber,
                      answers[currentQuestion] === option.value && styles.selectedNumber
                    ]}>
                      {String.fromCharCode(65 + index)}
                    </Text>
                  </View>
                  <Text style={[
                    styles.optionText,
                    answers[currentQuestion] === option.value && styles.selectedOptionText
                  ]}>
                    {option.label}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Professional Footer */}
        <View style={styles.footerSection}>
          <Text style={styles.confidentialityText}>
            🔒 Your responses are confidential and stored securely
          </Text>
          <Text style={styles.progressText}>
            Progress is automatically saved • {assessment.questions.length - currentQuestion - 1} questions remaining
          </Text>
        </View>
      </ScrollView>

    </View>
  );
};

export default AssessmentQuestions;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },

  // Scroll Container Styles
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },

  // Enhanced Progress Section Styles
  progressSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 15,
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 4,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2D5016',
  },
  progressCounter: {
    fontSize: 14,
    fontWeight: '600',
    color: '#66948a',
  },
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
  },
  progressBar: {
    flex: 1,
    height: 8,
    backgroundColor: '#E8F3D8',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#66948a',
    borderRadius: 4,
  },
  progressPercentage: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#66948a',
    minWidth: 40,
    textAlign: 'right',
  },
  // Question Section Styles
  questionSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  questionCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
    borderLeftWidth: 4,
    borderLeftColor: '#66948a',
  },
  questionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  questionLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2D5016',
  },
  questionBadge: {
    backgroundColor: '#E8F3D8',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#66948a',
  },
  questionBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#66948a',
  },
  questionText: {
    fontSize: 16,
    color: '#2D5016',
    lineHeight: 24,
    marginBottom: 15,
    fontWeight: '500',
  },
  questionInstruction: {
    fontSize: 14,
    color: '#666666',
    fontStyle: 'italic',
    textAlign: 'center',
    backgroundColor: '#F8F9FA',
    padding: 12,
    borderRadius: 8,
    marginTop: 10,
  },
  // Options Section Styles
  optionsSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  optionsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2D5016',
    marginBottom: 15,
    textAlign: 'center',
  },
  optionsContainer: {
    gap: 12,
  },
  optionButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 18,
    borderWidth: 2,
    borderColor: '#E8F3D8',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedOption: {
    backgroundColor: '#E8F3D8',
    borderColor: '#66948a',
    shadowColor: '#66948a',
    shadowOpacity: 0.2,
    elevation: 4,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionIndicator: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F8F9FA',
    borderWidth: 2,
    borderColor: '#E0E0E0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  selectedIndicator: {
    backgroundColor: '#66948a',
    borderColor: '#66948a',
  },
  optionNumber: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666666',
  },
  selectedNumber: {
    color: '#FFFFFF',
  },
  optionText: {
    flex: 1,
    fontSize: 16,
    color: '#444444',
    fontWeight: '500',
    lineHeight: 22,
  },
  selectedOptionText: {
    color: '#2D5016',
    fontWeight: '600',
  },
  disabledOption: {
    opacity: 0.6,
  },
  // Footer Section Styles
  footerSection: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  confidentialityText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: '500',
  },
  progressText: {
    fontSize: 12,
    color: '#999999',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  // Loading screen styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    paddingHorizontal: 30,
  },
  loadingContent: {
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    padding: 40,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
    width: '100%',
    maxWidth: 350,
  },
  loadingTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6B9142',
    marginTop: 20,
    marginBottom: 8,
    textAlign: 'center',
  },
  loadingSubtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 25,
    lineHeight: 22,
  },
  loadingTips: {
    width: '100%',
  },
  tipText: {
    fontSize: 14,
    color: '#888888',
    marginBottom: 12,
    textAlign: 'left',
    lineHeight: 20,
  },
  // Legacy styles for compatibility
  questionNumber: {
    fontSize: 14,
    color: '#6B9142',
    fontWeight: '600',
    marginBottom: 15,
    textAlign: 'center',
  },
});