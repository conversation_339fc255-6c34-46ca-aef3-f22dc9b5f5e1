// User database operations for Supabase
import { supabaseUrl, supabaseAnonKey } from './supabase';

/**
 * Create a new user record in the custom user table
 * @param {Object} userData - User data to store
 * @param {string} authToken - Authentication token from Supabase Auth
 * @returns {Promise<Object>} - Response with user data or error
 */
export const createUserRecord = async (userData, authToken) => {
  try {
    console.log('Creating user record in database:', userData);

    const response = await fetch(`${supabaseUrl}/rest/v1/user`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${authToken}`,
        'Prefer': 'return=representation'
      },
      body: JSON.stringify(userData),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to create user record');
    }

    console.log('User record created successfully:', data);
    return { data: data[0], error: null };
  } catch (error) {
    console.error('Create user record error:', error);
    return { data: null, error };
  }
};

/**
 * Update user record in the custom user table
 * @param {string} userId - User ID
 * @param {Object} userData - User data to update
 * @param {string} authToken - Authentication token from Supabase Auth
 * @returns {Promise<Object>} - Response with updated user data or error
 */
export const updateUserRecord = async (userId, userData, authToken) => {
  try {
    console.log('Updating user record in database:', userId, userData);

    const response = await fetch(`${supabaseUrl}/rest/v1/user?id=eq.${userId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${authToken}`,
        'Prefer': 'return=representation'
      },
      body: JSON.stringify(userData),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to update user record');
    }

    console.log('User record updated successfully:', data);
    return { data: data[0], error: null };
  } catch (error) {
    console.error('Update user record error:', error);
    return { data: null, error };
  }
};

/**
 * Get user record from the custom user table
 * @param {string} userId - User ID
 * @param {string} authToken - Authentication token from Supabase Auth
 * @returns {Promise<Object>} - Response with user data or error
 */
export const getUserRecord = async (userId, authToken) => {
  try {
    console.log('Fetching user record from database:', userId);

    const response = await fetch(`${supabaseUrl}/rest/v1/user?id=eq.${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${authToken}`,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to fetch user record');
    }

    console.log('User record fetched successfully:', data);
    return { data: data[0] || null, error: null };
  } catch (error) {
    console.error('Fetch user record error:', error);
    return { data: null, error };
  }
};

export default { createUserRecord, updateUserRecord, getUserRecord };
