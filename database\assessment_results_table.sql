-- Create assessment_results table for storing mental health assessment results
-- Run this in your Supabase SQL Editor

CREATE TABLE IF NOT EXISTS assessment_results (
    id SERIAL PRIMARY KEY,
    user_email VARCHAR(255) NOT NULL,
    assessment_type VARCHAR(50) NOT NULL,
    results JSONB NOT NULL,
    scores JSONB,
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_assessment_results_user_email ON assessment_results(user_email);
CREATE INDEX IF NOT EXISTS idx_assessment_results_type ON assessment_results(assessment_type);
CREATE INDEX IF NOT EXISTS idx_assessment_results_user_type ON assessment_results(user_email, assessment_type);
CREATE INDEX IF NOT EXISTS idx_assessment_results_completed_at ON assessment_results(completed_at DESC);

-- Create a composite index for the most common query (latest result per user per assessment)
CREATE INDEX IF NOT EXISTS idx_assessment_results_user_type_completed ON assessment_results(user_email, assessment_type, completed_at DESC);

-- Add comments for documentation
COMMENT ON TABLE assessment_results IS 'Stores mental health assessment results for users';
COMMENT ON COLUMN assessment_results.user_email IS 'Email of the user who took the assessment';
COMMENT ON COLUMN assessment_results.assessment_type IS 'Type of assessment (dass, pss, gad7, phq9)';
COMMENT ON COLUMN assessment_results.results IS 'Complete assessment results as JSON';
COMMENT ON COLUMN assessment_results.scores IS 'Extracted scores for easy querying';
COMMENT ON COLUMN assessment_results.completed_at IS 'When the assessment was completed';

-- Enable Row Level Security (RLS)
ALTER TABLE assessment_results ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only see their own assessment results
CREATE POLICY "Users can view own assessment results" ON assessment_results
    FOR SELECT USING (user_email = auth.jwt() ->> 'email');

-- Users can only insert their own assessment results
CREATE POLICY "Users can insert own assessment results" ON assessment_results
    FOR INSERT WITH CHECK (user_email = auth.jwt() ->> 'email');

-- Users can only update their own assessment results
CREATE POLICY "Users can update own assessment results" ON assessment_results
    FOR UPDATE USING (user_email = auth.jwt() ->> 'email');

-- Users can only delete their own assessment results
CREATE POLICY "Users can delete own assessment results" ON assessment_results
    FOR DELETE USING (user_email = auth.jwt() ->> 'email');

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_assessment_results_updated_at 
    BEFORE UPDATE ON assessment_results 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT ALL ON assessment_results TO authenticated;
GRANT USAGE ON SEQUENCE assessment_results_id_seq TO authenticated;

-- Example queries for testing:
-- 
-- -- Get latest DASS result for a user
-- SELECT * FROM assessment_results 
-- WHERE user_email = '<EMAIL>' 
-- AND assessment_type = 'dass' 
-- ORDER BY completed_at DESC 
-- LIMIT 1;
--
-- -- Get all attempts for a user
-- SELECT assessment_type, COUNT(*) as attempts, MAX(completed_at) as latest
-- FROM assessment_results 
-- WHERE user_email = '<EMAIL>' 
-- GROUP BY assessment_type;
--
-- -- Get score trends over time
-- SELECT completed_at, scores->>'depression' as depression_score
-- FROM assessment_results 
-- WHERE user_email = '<EMAIL>' 
-- AND assessment_type = 'dass'
-- ORDER BY completed_at;
