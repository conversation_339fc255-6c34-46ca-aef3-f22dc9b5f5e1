import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  FlatList,
  ActivityIndicator,
  Alert,
  StatusBar,
  Platform
} from 'react-native';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BottomNavigation from '../../components/BottomNavigation';
import StandardHeader from '../../components/StandardHeader';
import { useUser } from '../../context/UserContext';
import { supabaseUrl, supabaseAnonKey } from '../../lib/supabase';

const MentalAssessment = () => {
  const router = useRouter();
  const { userData } = useUser();
  const [isLoading, setIsLoading] = useState(true);
  const [assessmentStatus, setAssessmentStatus] = useState({});
  
  // Base assessment data
  const assessments = [
    {
      id: 'dass',
      title: 'DASS (Depression Anxiety Stress Scales)',
      description: 'A comprehensive assessment that measures three related negative emotional states of depression, anxiety, and stress. This test helps identify the severity of these core symptoms.',
      questions: 21,
      icon: 'D',
      color: '#6B9142'
    },
    {
      id: 'pss',
      title: 'PSS (Perceived Stress Scale)',
      description: 'A widely used psychological instrument that measures your perception of stress. It assesses how unpredictable, uncontrollable, and overloaded you find your life.',
      questions: 10,
      icon: 'P',
      color: '#6B9142'
    }
  ];

  // Check for saved results when component mounts and when user data changes
  useEffect(() => {
    console.log('🚨 CRITICAL DEBUG: Current user email in mental health screen:', userData.email);
    console.log('🚨 CRITICAL DEBUG: Full user data:', JSON.stringify(userData, null, 2));
    checkAssessmentStatus();
  }, [userData.email]);

  // Function to check assessment status from database and AsyncStorage
  const checkAssessmentStatus = async () => {
    setIsLoading(true);
    try {
      const status = {};

      // Check each assessment
      for (const assessment of assessments) {
        let hasResults = false;
        let timestamp = null;
        let attemptCount = 0;

        // First check database for latest results
        if (userData.email) {
          console.log(`🔍 Checking assessment results for user: ${userData.email}, assessment: ${assessment.id}`);
          try {
            const response = await fetch(
              `${supabaseUrl}/rest/v1/assessment_results?user_email=eq.${userData.email}&assessment_type=eq.${assessment.id}&order=completed_at.desc&limit=1`,
              {
                method: 'GET',
                headers: {
                  'Content-Type': 'application/json',
                  'apikey': supabaseAnonKey,
                  'Authorization': `Bearer ${supabaseAnonKey}`
                }
              }
            );

            if (response.ok) {
              const results = await response.json();
              console.log(`🚨 DATABASE RESULTS for ${assessment.id}:`, results);
              if (results && results.length > 0) {
                console.log(`🚨 FOUND RESULTS for ${userData.email}:`, results[0]);
                hasResults = true;
                timestamp = new Date(results[0].completed_at);

                // Get total attempt count
                const countResponse = await fetch(
                  `${supabaseUrl}/rest/v1/assessment_results?user_email=eq.${userData.email}&assessment_type=eq.${assessment.id}&select=id`,
                  {
                    method: 'GET',
                    headers: {
                      'Content-Type': 'application/json',
                      'apikey': supabaseAnonKey,
                      'Authorization': `Bearer ${supabaseAnonKey}`
                    }
                  }
                );

                if (countResponse.ok) {
                  const allResults = await countResponse.json();
                  attemptCount = allResults.length;
                }
              }
            }
          } catch (dbError) {
            console.error(`Database error for ${assessment.id}:`, dbError);
          }
        }

        // Fallback to AsyncStorage if no database results
        if (!hasResults) {
          const savedResults = await AsyncStorage.getItem(`${assessment.id}_saved_results`);
          const savedTimestamp = await AsyncStorage.getItem(`${assessment.id}_result_timestamp`);

          if (savedResults) {
            hasResults = true;
            timestamp = savedTimestamp ? new Date(savedTimestamp) : null;
          }
        }

        // Check for saved progress
        const savedProgress = await AsyncStorage.getItem(`${assessment.id}_progress`);
        const progressData = savedProgress ? JSON.parse(savedProgress) : null;

        status[assessment.id] = {
          hasResults,
          timestamp,
          attemptCount,
          hasProgress: savedProgress !== null,
          progressData: progressData,
          progressPercentage: progressData ? Math.round(((progressData.currentQuestion + 1) / progressData.totalQuestions) * 100) : 0
        };

        console.log(`${assessment.id} status:`, status[assessment.id]);
      }

      setAssessmentStatus(status);
    } catch (error) {
      console.error('Error checking assessment status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAssessmentSelect = (assessment) => {
    router.push({
      pathname: '/(main)/mental-health/assessment/information',
      params: { id: assessment.id }
    });
  };

  const handleViewResults = async (assessmentId) => {
    try {
      console.log(`Attempting to view results for ${assessmentId}`);

      let resultsToShow = null;

      // First try to get latest results from database
      if (userData.email) {
        console.log(`🔍 Viewing results for user: ${userData.email}, assessment: ${assessmentId}`);
        try {
          const response = await fetch(
            `${supabaseUrl}/rest/v1/assessment_results?user_email=eq.${userData.email}&assessment_type=eq.${assessmentId}&order=completed_at.desc&limit=1`,
            {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'apikey': supabaseAnonKey,
                'Authorization': `Bearer ${supabaseAnonKey}`
              }
            }
          );

          if (response.ok) {
            const results = await response.json();
            console.log(`Database response for ${assessmentId}:`, results);
            if (results && results.length > 0) {
              resultsToShow = results[0].results; // This is the JSON string of results
              console.log(`✅ Found latest ${assessmentId} results in database:`, results[0]);
              console.log(`Results data:`, resultsToShow);
            } else {
              console.log(`❌ No database results found for ${assessmentId}`);
            }
          } else {
            console.log(`❌ Database request failed for ${assessmentId}:`, response.status);
          }
        } catch (dbError) {
          console.error('Database error:', dbError);
        }
      }

      // Fallback to AsyncStorage if no database results
      if (!resultsToShow) {
        const savedResults = await AsyncStorage.getItem(`${assessmentId}_saved_results`);
        if (savedResults) {
          resultsToShow = savedResults;
          console.log(`✅ Found ${assessmentId} results in AsyncStorage`);
        }
      }

      console.log(`${assessmentId} results found:`, resultsToShow ? 'Yes' : 'No');

      if (resultsToShow) {
        // Add a unique timestamp parameter to force fresh data loading
        const timestamp = Date.now();

        router.push({
          pathname: '/(main)/mental-health/assessment/results',
          params: {
            id: assessmentId,
            results: resultsToShow,
            refresh: timestamp // Force the results screen to treat this as new data
          }
        });
      } else {
        Alert.alert(
          "No Results Found",
          "You haven't completed this assessment yet. Would you like to take it now?",
          [
            {
              text: "Yes",
              onPress: () => handleAssessmentSelect({id: assessmentId})
            },
            {
              text: "No",
              style: "cancel"
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error viewing results:', error);
      Alert.alert("Error", "Could not load assessment results.");
    }
  };

  const renderAssessmentItem = ({ item }) => {
    const hasResults = assessmentStatus[item.id]?.hasResults || false;
    const hasProgress = assessmentStatus[item.id]?.hasProgress || false;
    const progressPercentage = assessmentStatus[item.id]?.progressPercentage || 0;
    const timestamp = assessmentStatus[item.id]?.timestamp;

    return (
      <View style={styles.assessmentCard}>
        {/* Assessment Header with Badge */}
        <View style={styles.assessmentHeader}>
          <Text style={styles.assessmentTitle}>{item.title}</Text>
          {hasResults && (
            <View style={styles.completedBadge}>
              <Text style={styles.completedBadgeText}>✓ Completed</Text>
            </View>
          )}
          {!hasResults && hasProgress && (
            <View style={styles.progressBadge}>
              <Text style={styles.progressBadgeText}>{progressPercentage}% Complete</Text>
            </View>
          )}
        </View>

        <View style={styles.assessmentContent}>
          <Text style={styles.assessmentDescription}>{item.description}</Text>

          {/* Bottom Row: Stats and Button */}
          <View style={styles.bottomRow}>
            <View style={styles.assessmentStats}>
              <Text style={styles.statText}>
                {item.questions} Questions (5-10 minutes)
              </Text>
            </View>

            <TouchableOpacity
              style={[
                styles.takeTestButton,
                hasResults && styles.retakeButton,
                hasProgress && !hasResults && styles.continueButton
              ]}
              onPress={() => handleAssessmentSelect(item)}
              activeOpacity={0.8}
            >
              <Text style={[
                styles.takeTestButtonText,
                hasResults && styles.retakeButtonText,
                hasProgress && !hasResults && styles.continueButtonText
              ]}>
                {hasResults ? 'Retake' :
                 hasProgress ? 'Continue' :
                 'Take test'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* View Results Button (if has results) */}
          {hasResults && (
            <TouchableOpacity
              style={styles.viewResultsButton}
              onPress={() => handleViewResults(item.id)}
              activeOpacity={0.8}
            >
              <Text style={styles.viewResultsButtonText}>View Results</Text>
            </TouchableOpacity>
          )}

          {/* Last Completed Date */}
          {hasResults && timestamp && (
            <Text style={styles.lastCompletedText}>
              Last completed: {new Date(timestamp).toLocaleDateString()}
            </Text>
          )}
        </View>
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <StandardHeader title="Mental Health Assessment" />
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#6B9142" />
            <Text style={styles.loadingText}>Loading assessments...</Text>
          </View>
        </SafeAreaView>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StandardHeader title="Mental Health Assessment" />

      <SafeAreaView style={styles.safeArea}>
      <View style={styles.content}>
        <Text style={styles.subtitle}>
          Please select which assessment you would like to take today
        </Text>
        <FlatList
          data={assessments}
          renderItem={renderAssessmentItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.assessmentList}
          showsVerticalScrollIndicator={false}
        />
      </View>
      <BottomNavigation />
    </SafeAreaView>
    </View>
  );
};

export default MentalAssessment;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    color: '#7F8C8D',
    fontSize: 16,
    fontWeight: '500',
  },
  safeArea: {
    flex: 1,
  },
  content: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'left',
    marginTop: 20,
    marginBottom: 15,
    paddingHorizontal: 20,
    lineHeight: 18,
  },
  assessmentList: {
    paddingTop: 20,
    paddingBottom: 100, // Space for bottom navigation
  },
  assessmentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    marginHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  assessmentContent: {
    flex: 1,
  },

  assessmentInfo: {
    flex: 1,
  },
  assessmentTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C3E50',
    flex: 1,
    marginRight: 10,
  },
  assessmentDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
    lineHeight: 20,
  },
  assessmentQuestions: {
    fontSize: 13,
    color: '#95A5A6',
    marginBottom: 14,
    marginTop: 4,
    fontWeight: '500',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  takeTestButton: {
    backgroundColor: '#66948a',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 32,
    alignItems: 'center',
    alignSelf: 'flex-end',
    minWidth: 100,
    shadowColor: '#66948a',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  takeTestButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  viewResultsButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#66948a',
    marginTop: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  viewResultsButtonText: {
    color: '#66948a',
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  // New enhanced styles
  assessmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  completedBadge: {
    backgroundColor: '#E8F2D9',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    borderWidth: 1,
    borderColor: '#6B9142',
  },
  completedBadgeText: {
    color: '#6B9142',
    fontSize: 12,
    fontWeight: 'bold',
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
  },
  assessmentStats: {
    flex: 1,
  },
  statText: {
    fontSize: 13,
    color: '#666666',
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 10,
    marginBottom: 15,
  },
  retakeButton: {
    backgroundColor: '#66948a',
    shadowColor: '#66948a',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 6,
    elevation: 4,
  },
  retakeButtonText: {
    color: '#FFFFFF',
  },
  lastCompletedText: {
    fontSize: 12,
    color: '#999999',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  // Progress Badge Styles
  progressBadge: {
    backgroundColor: '#FFF3CD',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    borderWidth: 1,
    borderColor: '#FFC107',
  },
  progressBadgeText: {
    color: '#856404',
    fontSize: 12,
    fontWeight: 'bold',
  },
  // Continue Button Styles
  continueButton: {
    backgroundColor: '#2D5016',
    shadowColor: '#2D5016',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 6,
    elevation: 4,
  },
  continueButtonText: {
    color: '#FFFFFF',
    fontWeight: '700',
    letterSpacing: 0.5,
  },
});