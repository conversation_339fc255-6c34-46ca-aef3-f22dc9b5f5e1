// Simple user table operations with Supabase OTP email verification
import { supabaseUrl, supabase<PERSON>nonKey } from './supabase';
import { getEmailServerUrls, findWorkingEmailServer, EMAIL_CONFIG } from '../config/email-config';

// Email sending function using EmailJS (React Native compatible)
const sendVerificationEmail = async (email, verificationCode) => {
  try {
    console.log('📧 Attempting to send email via EmailJS...');

    // Use EmailJS service - works in React Native and uses your Gmail SMTP
    const response = await fetch('https://api.emailjs.com/api/v1.0/email/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        service_id: 'service_gmail_mentalease', // We'll set this up
        template_id: 'template_verification_code', // We'll set this up
        user_id: 'mentalease_public_key', // We'll set this up
        template_params: {
          to_email: email,
          verification_code: verificationCode,
          subject: 'MentalEase - Email Verification Code',
          from_name: 'MentalEase',
          message: `Your verification code is: ${verificationCode}`,
          html_content: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #6B9142; margin: 0;">MentalEase</h1>
                <p style="color: #666; margin: 5px 0;">Your Mental Health Companion</p>
              </div>

              <h2 style="color: #333;">Welcome to MentalEase!</h2>
              <p style="color: #555; line-height: 1.6;">
                Thank you for signing up for MentalEase. To complete your registration and secure your account,
                please verify your email address using the code below.
              </p>

              <div style="background: linear-gradient(135deg, #6B9142, #8BC34A); padding: 25px; border-radius: 12px; text-align: center; margin: 25px 0;">
                <p style="color: white; margin: 0 0 10px 0; font-size: 16px;">Your Verification Code:</p>
                <h1 style="color: white; font-size: 36px; letter-spacing: 6px; margin: 0; font-weight: bold;">${verificationCode}</h1>
              </div>

              <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #333; margin: 0 0 10px 0;">How to verify:</h3>
                <ol style="color: #555; margin: 0; padding-left: 20px;">
                  <li>Open the MentalEase app</li>
                  <li>Enter the 6-digit code above</li>
                  <li>Tap "Verify Email"</li>
                </ol>
              </div>

              <p style="color: #666; font-size: 14px;">This code will expire in 10 minutes for security reasons.</p>

              <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
              <p style="color: #999; font-size: 12px;">
                If you didn't sign up for MentalEase, please ignore this email.
              </p>
            </div>
          `
        }
      }),
    });

    if (response.ok) {
      console.log('✅ Email sent successfully via EmailJS');
      return true;
    } else {
      const errorData = await response.text();
      console.log('❌ EmailJS error:', errorData);
      return false;
    }
  } catch (error) {
    console.log('❌ Email sending failed:', error);
    return false;
  }
};

export const signUp = async (email, username, password) => {
  try {
    console.log('Creating user in user table:', email, username);

    // Check if user already exists (email or username)
    const checkResponse = await fetch(`${supabaseUrl}/rest/v1/users?or=(email.eq.${email},username.eq.${username})`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
      },
    });

    const existingUsers = await checkResponse.json();

    if (existingUsers && existingUsers.length > 0) {
      const existingUser = existingUsers[0];
      if (existingUser.email === email) {
        throw new Error('User with this email already exists');
      }
      if (existingUser.username === username) {
        throw new Error('Username is already taken');
      }
    }

    // Create new user in users table
    const signUpResponse = await fetch(`${supabaseUrl}/rest/v1/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
        'Prefer': 'return=representation'
      },
      body: JSON.stringify({
        email,
        username,                         // Add username field
        password,
        name: email.split('@')[0],        // Use email username as temporary name
        status: false,                    // Web uses 'status' instead of 'email_verified'
        role: 'patient',                  // Default role for mobile users
        has_completed_profile: false
      }),
    });

    const data = await signUpResponse.json();

    if (!signUpResponse.ok) {
      throw new Error(data.message || 'Failed to create user');
    }

    console.log('User created in table:', data);

    // Generate custom 6-digit verification code for testing
    try {
      console.log('🔄 Generating custom verification code...');

      // Generate custom 6-digit code and store it
      const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
      const expirationTime = Date.now() + (10 * 60 * 1000); // 10 minutes

      // Store verification code in database
      try {
        const updateCodeResponse = await fetch(`${supabaseUrl}/rest/v1/users?email=eq.${email}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabaseAnonKey,
            'Prefer': 'return=representation'
          },
          body: JSON.stringify({
            activationcode: verificationCode,                    // Web uses 'activationcode'
            activation_code_expires: new Date(expirationTime).toISOString()
          }),
        });

        if (updateCodeResponse.ok) {
          console.log('✅ Verification code stored in database successfully');
        } else {
          console.log('❌ Failed to store verification code in DB');
        }
      } catch (dbError) {
        console.log('❌ DB error storing verification code:', dbError);
      }

      // Display the code prominently for testing
      console.log('');
      console.log('🎯 ================================');
      console.log('🔢 VERIFICATION CODE:', verificationCode);
      console.log('📧 For email:', email);
      console.log('⏰ Valid for 10 minutes');
      console.log('🎯 ================================');
      console.log('');

      // Send email using dynamic email server discovery
      try {
        console.log('📧 Sending custom verification code via email...');

        // Get all possible email server URLs with priority order
        const priorityUrls = [
          'http://************:3001/send-verification',  // Your working Wi-Fi IP
          'http://************:3001/send-verification',  // Your working Ethernet IP
          'http://********:3001/send-verification',      // Android emulator bridge
          'http://localhost:3001/send-verification',     // Localhost
        ];

        const allUrls = [...priorityUrls, ...getEmailServerUrls()];
        let emailSent = false;

        // Try to find and use working email server
        for (const url of allUrls) {
          try {
            console.log(`🔄 Trying to connect to: ${url}`);

            // Create a promise with timeout
            const timeoutPromise = new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Timeout')), 3000)
            );

            const fetchPromise = fetch(url, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                email: email,
                verificationCode: verificationCode
              }),
            });

            const emailResponse = await Promise.race([fetchPromise, timeoutPromise]);

            console.log(`📊 Response status for ${url}:`, emailResponse.status);

            if (emailResponse.ok) {
              try {
                const result = await emailResponse.json();
                console.log('✅ Email sent successfully via:', url);
                console.log('📧 Response:', result);
                emailSent = true;
                break;
              } catch (jsonError) {
                console.log('⚠️ Response OK but JSON parse failed:', jsonError.message);
                // Still consider it successful if status is OK
                console.log('✅ Email likely sent successfully via:', url);
                emailSent = true;
                break;
              }
            } else {
              console.log(`❌ Failed with ${url}:`, emailResponse.status);
              const errorText = await emailResponse.text().catch(() => 'No error text');
              console.log(`❌ Error details:`, errorText);
            }
          } catch (urlError) {
            console.log(`❌ Connection failed to ${url}:`, urlError.message);
            continue;
          }
        }

        if (emailSent) {
          console.log('✅ Custom verification email sent successfully to:', email);
          console.log('📧 User should receive the 6-digit code:', verificationCode);
        } else {
          console.log('❌ All email server connections failed');
          console.log('🔢 Verification code available in console:', verificationCode);
          console.log('💡 Make sure your email server is running: node email-server.js');
          console.log('💡 The app will automatically find the server when it\'s available');
        }
      } catch (emailError) {
        console.log('❌ Email sending error:', emailError);
        console.log('🔢 Verification code available in console:', verificationCode);
      }

    } catch (emailError) {
      console.log('❌ Email sending error:', emailError);
    }

    return { data: data[0], error: null };
  } catch (error) {
    console.error('Create user error:', error);
    return { data: null, error };
  }
};

export const signIn = async (username, password) => {
  try {
    console.log('Finding user in user table:', username);

    // Find user in users table using username
    const response = await fetch(`${supabaseUrl}/rest/v1/users?username=eq.${username}&password=eq.${password}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error('Failed to find user');
    }

    if (!data || data.length === 0) {
      throw new Error('Invalid username or password');
    }

    const user = data[0];
    console.log('User found in table:', user);

    return { data: { user }, error: null };
  } catch (error) {
    console.error('Find user error:', error);
    return { data: null, error };
  }
};

export const signOut = async () => {
  try {
    console.log('Signing out user');

    // Since we're using simple table-based auth,
    // signOut just clears local data (handled by the app)
    return { error: null };
  } catch (error) {
    console.error('Sign out error:', error);
    return { error };
  }
};

export default { signUp, signIn, signOut };

