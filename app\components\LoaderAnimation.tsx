import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  ActivityIndicator
} from 'react-native';

const { width } = Dimensions.get('window');

interface LoaderAnimationProps {
  title?: string;
  subtitle?: string;
  tips?: string[];
  color?: string;
  backgroundColor?: string;
}

const LoaderAnimation: React.FC<LoaderAnimationProps> = ({
  title = 'Loading...',
  subtitle = '',
  tips = [],
  color = '#6B9142',
  backgroundColor = '#F8F9FA'
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const tipAnimations = useRef(tips.map(() => new Animated.Value(0))).current;

  useEffect(() => {
    // Main container animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      })
    ]).start();

    // Staggered tip animations
    if (tips.length > 0) {
      const tipAnimationSequence = tips.map((_, index) =>
        Animated.timing(tipAnimations[index], {
          toValue: 1,
          duration: 400,
          delay: 800 + (index * 200),
          useNativeDriver: true,
        })
      );
      
      Animated.stagger(200, tipAnimationSequence).start();
    }
  }, [fadeAnim, scaleAnim, tipAnimations, tips]);

  return (
    <View style={[styles.container, { backgroundColor }]}>
      <Animated.View 
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }]
          }
        ]}
      >
        {/* Animated Loader */}
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={color} />
          <View style={[styles.loaderRing, { borderColor: `${color}20` }]} />
        </View>

        {/* Title */}
        <Text style={[styles.title, { color }]}>{title}</Text>
        
        {/* Subtitle */}
        {subtitle ? (
          <Text style={styles.subtitle}>{subtitle}</Text>
        ) : null}

        {/* Tips */}
        {tips.length > 0 && (
          <View style={styles.tipsContainer}>
            {tips.map((tip, index) => (
              <Animated.View
                key={index}
                style={[
                  styles.tipItem,
                  {
                    opacity: tipAnimations[index],
                    transform: [{
                      translateY: tipAnimations[index].interpolate({
                        inputRange: [0, 1],
                        outputRange: [20, 0]
                      })
                    }]
                  }
                ]}
              >
                <Text style={styles.tipText}>{tip}</Text>
              </Animated.View>
            ))}
          </View>
        )}
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  content: {
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    padding: 40,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
    width: '100%',
    maxWidth: 350,
  },
  loaderContainer: {
    position: 'relative',
    marginBottom: 20,
  },
  loaderRing: {
    position: 'absolute',
    top: -10,
    left: -10,
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 2,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 25,
    lineHeight: 22,
  },
  tipsContainer: {
    width: '100%',
  },
  tipItem: {
    marginBottom: 12,
  },
  tipText: {
    fontSize: 14,
    color: '#888888',
    textAlign: 'left',
    lineHeight: 20,
  },
});

export default LoaderAnimation;
