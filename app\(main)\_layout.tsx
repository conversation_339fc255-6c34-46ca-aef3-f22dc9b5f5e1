import { Stack } from 'expo-router';
import ActivityTracker from '../components/ActivityTracker';

export default function MainLayout() {
  return (
    <ActivityTracker>
      <Stack
        screenOptions={{
          headerShown: false,
          contentStyle: {
            backgroundColor: '#ffffff',
          },
          // Smooth main app transitions (<PERSON>'s Law - familiar navigation patterns)
          animation: 'slide_from_right',
          animationDuration: 300,
          gestureEnabled: true,
          presentation: 'card',
        }}
      >
      <Stack.Screen
        name="dashboard"
        options={{
          gestureEnabled: false, // Prevent swiping back from dashboard
          gestureDirection: 'horizontal', // Explicitly set gesture direction
          fullScreenGestureEnabled: false, // Disable full screen gesture
        }}
      />

      {/* Profile Section */}
      <Stack.Screen name="profile" />

      {/* Mental Health Section */}
      <Stack.Screen name="mental-health" />

      {/* Consultation Section */}
      <Stack.Screen name="consultation" />

      {/* Appointments Section */}
      <Stack.Screen name="appointments" />

      {/* Support Section */}
      <Stack.Screen name="support" />
    </Stack>
    </ActivityTracker>
  );
}
