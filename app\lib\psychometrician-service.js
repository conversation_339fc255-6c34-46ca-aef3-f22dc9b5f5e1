import { supabase } from './supabase';

/**
 * Sync users with 'psychometrician' role to psychometricians table
 * This ensures all psychometrician users are available for appointment booking
 */
export const syncPsychometricianUsers = async () => {
  try {
    console.log('🔄 Starting psychometrician sync...');

    // Step 1: Fetch all users with psychometrician role
    const { data: psychometricianUsers, error: usersError } = await supabase
      .from('users')
      .select('id, name, email, created_at, updated_at')
      .eq('role', 'psychometrician');

    if (usersError) {
      console.error('❌ Error fetching psychometrician users:', usersError);
      throw usersError;
    }

    if (!psychometricianUsers || psychometricianUsers.length === 0) {
      console.log('⚠️ No users with psychometrician role found');
      return { success: true, synced: 0, message: 'No psychometrician users to sync' };
    }

    console.log(`📋 Found ${psychometricianUsers.length} psychometrician users`);

    // Step 2: For each psychometrician user, ensure they exist in psychometricians table
    let syncedCount = 0;
    const errors = [];

    for (const user of psychometricianUsers) {
      try {
        // Check if psychometrician record already exists
        const { data: existingPsychometrician, error: checkError } = await supabase
          .from('psychometricians')
          .select('id, user_id')
          .eq('user_id', user.id)
          .single();

        if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
          console.error(`❌ Error checking psychometrician for user ${user.id}:`, checkError);
          errors.push({ user_id: user.id, error: checkError.message });
          continue;
        }

        if (existingPsychometrician) {
          // Update existing record
          const { error: updateError } = await supabase
            .from('psychometricians')
            .update({
              name: user.name || user.email,
              email: user.email,
              available: true,
              updated_at: new Date().toISOString()
            })
            .eq('user_id', user.id);

          if (updateError) {
            console.error(`❌ Error updating psychometrician for user ${user.id}:`, updateError);
            errors.push({ user_id: user.id, error: updateError.message });
          } else {
            console.log(`✅ Updated psychometrician record for user ${user.id} (${user.name || user.email})`);
            syncedCount++;
          }
        } else {
          // Create new psychometrician record
          const { error: insertError } = await supabase
            .from('psychometricians')
            .insert({
              user_id: user.id,
              name: user.name || user.email,
              email: user.email,
              specialty: 'Psychometrician',
              experience: '5+ years',
              rating: 4.8,
              image: '👨‍⚕️',
              available: true,
              created_at: user.created_at || new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (insertError) {
            console.error(`❌ Error creating psychometrician for user ${user.id}:`, insertError);
            errors.push({ user_id: user.id, error: insertError.message });
          } else {
            console.log(`✅ Created psychometrician record for user ${user.id} (${user.name || user.email})`);
            syncedCount++;
          }
        }
      } catch (error) {
        console.error(`❌ Unexpected error processing user ${user.id}:`, error);
        errors.push({ user_id: user.id, error: error.message });
      }
    }

    const result = {
      success: errors.length === 0,
      synced: syncedCount,
      total: psychometricianUsers.length,
      errors: errors,
      message: `Synced ${syncedCount}/${psychometricianUsers.length} psychometrician users`
    };

    console.log('🎉 Psychometrician sync completed:', result);
    return result;

  } catch (error) {
    console.error('❌ Fatal error in psychometrician sync:', error);
    return {
      success: false,
      synced: 0,
      total: 0,
      errors: [{ error: error.message }],
      message: 'Sync failed: ' + error.message
    };
  }
};

/**
 * Get all available psychometricians for appointment booking
 */
export const getAvailablePsychometricians = async () => {
  try {
    const { data, error } = await supabase
      .from('psychometricians')
      .select(`
        id,
        user_id,
        name,
        email,
        specialty,
        experience,
        rating,
        image,
        available,
        bio,
        qualifications
      `)
      .eq('available', true)
      .order('name');

    if (error) {
      console.error('Error fetching available psychometricians:', error);
      throw error;
    }

    return { success: true, data: data || [] };
  } catch (error) {
    console.error('Error in getAvailablePsychometricians:', error);
    return { success: false, data: [], error: error.message };
  }
};

/**
 * Update psychometrician profile information
 */
export const updatePsychometricianProfile = async (userId, profileData) => {
  try {
    const { data, error } = await supabase
      .from('psychometricians')
      .update({
        ...profileData,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .select();

    if (error) {
      console.error('Error updating psychometrician profile:', error);
      throw error;
    }

    return { success: true, data: data[0] };
  } catch (error) {
    console.error('Error in updatePsychometricianProfile:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get psychometrician by user ID
 */
export const getPsychometricianByUserId = async (userId) => {
  try {
    const { data, error } = await supabase
      .from('psychometricians')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching psychometrician by user ID:', error);
      throw error;
    }

    return { success: true, data: data || null };
  } catch (error) {
    console.error('Error in getPsychometricianByUserId:', error);
    return { success: false, data: null, error: error.message };
  }
};
