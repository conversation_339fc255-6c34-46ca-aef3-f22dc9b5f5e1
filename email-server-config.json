{"timestamp": "2025-06-08T19:56:06.621Z", "hostname": "LAPTOP-O8I4IQAB", "platform": "win32", "networkInterfaces": [{"name": "Ethernet 2", "address": "************", "netmask": "*************"}, {"name": "Wi-Fi", "address": "************", "netmask": "*************"}], "emailServerPort": 3001, "instructions": {"setup": ["Install dependencies: npm install express nodemailer cors", "Configure Gmail credentials in email-server.js", "Start email server: node email-server.js", "Run React Native app: npm start"], "troubleshooting": ["Make sure email server is running on port 3001", "Check firewall settings", "Verify Gmail app password is correct", "Ensure devices are on same network"]}}