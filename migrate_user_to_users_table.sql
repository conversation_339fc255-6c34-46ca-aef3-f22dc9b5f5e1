-- Migration script to sync mobile 'user' table with web 'users' table
-- Run this in your Supabase SQL editor
-- Note: Web 'name' column = Mobile 'first_name' column

-- Step 1: Create the new 'users' table with unified schema
CREATE TABLE IF NOT EXISTS "users" (
  id BIGSERIAL PRIMARY KEY,                    -- int8 type like web
  created_at TIMESTAMPTZ DEFAULT NOW(),       -- Web column
  updated_at TIMESTAMPTZ DEFAULT NOW(),       -- Web column
  name VARCHAR,                                -- Web column (= first_name from mobile)
  email VARCHAR UNIQUE NOT NULL,               -- Same in both
  username VARCHAR,                            -- Web only column
  password VARCHAR NOT NULL,                   -- Same in both
  status BOOLEAN DEFAULT FALSE,                -- Web column (= email_verified from mobile)
  role VARCHAR DEFAULT 'patient',              -- Web only column
  activationcode VARCHAR,                      -- Web column (= verification_code from mobile)
  resetcode VARCHAR,                           -- Web only column
  contactnumber VARCHAR,                       -- Web column (= phone from mobile)
  disable BOOLEAN DEFAULT FALSE,               -- Web only column

  -- Additional mobile columns to preserve data
  middle_name <PERSON><PERSON><PERSON><PERSON>,                         -- Mobile only
  last_name VARCHA<PERSON>,                           -- Mobile only
  age INTEGER,                                 -- Mobile only
  gender VARCHAR,                              -- Mobile only
  civil_status VARCHAR,                        -- Mobile only
  birthdate VARCHAR,                           -- Mobile only
  birthplace VARCHAR,                          -- Mobile only
  religion VARCHAR,                            -- Mobile only
  address VARCHAR,                             -- Mobile only
  control_number VARCHAR UNIQUE,               -- Mobile only (unique identifier)
  has_completed_profile BOOLEAN DEFAULT FALSE, -- Mobile only
  verification_code_expires TIMESTAMPTZ        -- Mobile only
);

-- Step 2: Migrate data from 'user' to 'users' table
INSERT INTO "users" (
  name,                    -- first_name → name (web convention)
  email,
  password,
  status,                  -- email_verified → status (web convention)
  activationcode,          -- verification_code → activationcode (web convention)
  contactnumber,           -- phone → contactnumber (web convention)
  role,                    -- Default to 'patient' for mobile users

  -- Mobile-specific data preservation
  middle_name,
  last_name,
  age,
  gender,
  civil_status,
  birthdate,
  birthplace,
  religion,
  address,
  control_number,
  has_completed_profile,
  verification_code_expires,

  -- Timestamps
  created_at,
  updated_at
)
SELECT
  first_name,              -- Mobile first_name → Web name
  email,
  password,
  email_verified,          -- Mobile email_verified → Web status
  verification_code,       -- Mobile verification_code → Web activationcode
  phone,                   -- Mobile phone → Web contactnumber
  'patient',               -- Default role for mobile users

  -- Mobile-specific data
  middle_name,
  last_name,
  age,
  gender,
  civil_status,
  birthdate,
  birthplace,
  religion,
  address,
  control_number,
  has_completed_profile,
  verification_code_expires,

  -- Set current timestamps
  NOW(),
  NOW()
FROM "user"
WHERE EXISTS (SELECT 1 FROM "user");

-- Step 3: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_email ON "users"(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON "users"(username);
CREATE INDEX IF NOT EXISTS idx_users_control_number ON "users"(control_number);
CREATE INDEX IF NOT EXISTS idx_users_role ON "users"(role);

-- Step 4: Add comments for documentation
COMMENT ON TABLE "users" IS 'Unified users table for both web and mobile applications';
COMMENT ON COLUMN "users".name IS 'User display name (mapped from mobile first_name)';
COMMENT ON COLUMN "users".status IS 'Email verification status (mapped from mobile email_verified)';
COMMENT ON COLUMN "users".activationcode IS 'Email verification code (mapped from mobile verification_code)';
COMMENT ON COLUMN "users".contactnumber IS 'Phone number (mapped from mobile phone)';
COMMENT ON COLUMN "users".role IS 'User role: patient, psychometrician, admin';
COMMENT ON COLUMN "users".control_number IS 'Mobile app unique identifier (e.g., MH-2023-45678)';
COMMENT ON COLUMN "users".has_completed_profile IS 'Mobile app profile completion flag';

-- Step 5: Create a function to auto-update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Step 6: Create trigger for auto-updating updated_at
DROP TRIGGER IF EXISTS update_users_updated_at ON "users";
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON "users"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Step 7: Backup the old table (optional - comment out if not needed)
-- ALTER TABLE "user" RENAME TO "user_backup";

-- Step 8: Grant necessary permissions (adjust as needed)
-- GRANT ALL ON "users" TO authenticated;
-- GRANT ALL ON "users" TO service_role;

-- Verification queries (run these to check migration)
-- SELECT COUNT(*) as old_count FROM "user";
-- SELECT COUNT(*) as new_count FROM "users";
-- SELECT * FROM "users" LIMIT 5;
