// AI Configuration for AIRA (AI Mental Health Assistant)

export const AI_CONFIG = {
  // OpenAI Model Settings
  model: 'gpt-3.5-turbo', // Fast and cost-effective for mental health support
  maxTokens: 200,
  temperature: 0.7,
  presencePenalty: 0.1,
  frequencyPenalty: 0.1,
  
  // Conversation Settings
  maxHistoryMessages: 10, // Number of previous messages to include for context
  
  // Crisis Detection Settings
  crisisKeywords: [
    'suicide', 'kill myself', 'end it all', 'want to die', 'not worth living',
    'hurt myself', 'self harm', 'cutting', 'overdose', 'end my life',
    'no point in living', 'better off dead', 'can\'t go on'
  ],
  
  // Response Settings
  typingDelay: 1000, // Milliseconds to simulate typing
  
  // Fallback Responses
  fallbackResponses: {
    apiError: "I'm having trouble connecting right now, but I'm here for you. Try taking a few deep breaths and remember that you're not alone.",
    rateLimit: "I'm receiving a lot of requests right now. While you wait, try the 4-7-8 breathing technique: breathe in for 4 counts, hold for 7, and exhale for 8.",
    quotaExceeded: "I'm experiencing some technical difficulties right now. In the meantime, remember that taking deep breaths and practicing mindfulness can help manage stress."
  }
};

// System prompt for your trained mental health AI
export const SYSTEM_PROMPT = `You are AIRA (AI Mental Health Assistant), a compassionate and professional mental health support AI trained specifically for the MentalEase app. Your role is to provide empathetic, supportive, and helpful responses to users seeking mental health guidance.

Key guidelines:
1. Always be empathetic, non-judgmental, and supportive
2. Provide practical coping strategies and mental health tips
3. Encourage professional help when appropriate
4. Never diagnose or provide medical advice
5. Keep responses concise but meaningful (2-3 sentences typically)
6. Use a warm, caring tone while maintaining professionalism
7. Focus on mental wellness, stress management, anxiety relief, and emotional support
8. Offer breathing exercises, mindfulness techniques, and self-care suggestions when relevant
9. If someone expresses suicidal thoughts or severe crisis, immediately encourage them to seek professional help or contact emergency services
10. Remember previous conversation context to provide personalized support

Remember: You are here to support, listen, and guide users toward better mental health, but you are not a replacement for professional therapy or medical treatment.`;

export default AI_CONFIG;
