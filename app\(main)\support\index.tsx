import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';

export default function SupportIndex() {
  const router = useRouter();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Support Center</Text>
      <Text style={styles.subtitle}>Get help and support when you need it</Text>
      
      <TouchableOpacity 
        style={styles.option}
        onPress={() => router.push('/(main)/support/customer-support')}
      >
        <Text style={styles.optionText}>Customer Support</Text>
        <Text style={styles.optionDescription}>Contact our support team</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.option}
        onPress={() => router.push('/(main)/support/inquiries')}
      >
        <Text style={styles.optionText}>Inquiries</Text>
        <Text style={styles.optionDescription}>Submit questions or feedback</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 30,
  },
  option: {
    backgroundColor: '#f5f5f5',
    padding: 20,
    borderRadius: 10,
    marginBottom: 15,
  },
  optionText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 5,
  },
  optionDescription: {
    fontSize: 14,
    color: '#666',
  },
});
