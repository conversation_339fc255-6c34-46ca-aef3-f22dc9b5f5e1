// User data mapping between mobile app and web database schema
// Handles column name differences between mobile and web

/**
 * Maps mobile app user data to web database schema
 * @param {Object} mobileUserData - User data from mobile app
 * @returns {Object} - User data formatted for web database
 */
export const mapMobileToWeb = (mobileUserData) => {
  return {
    // Web column names
    name: mobileUserData.firstName || mobileUserData.first_name,           // first_name → name
    email: mobileUserData.email,
    password: mobileUserData.password,
    status: mobileUserData.emailVerified || mobileUserData.email_verified, // email_verified → status
    role: mobileUserData.role || 'patient',
    activationcode: mobileUserData.verificationCode || mobileUserData.verification_code, // verification_code → activationcode
    contactnumber: mobileUserData.phone,                                   // phone → contactnumber
    username: mobileUserData.username || null,
    resetcode: mobileUserData.resetcode || null,
    disable: mobileUserData.disable || false,
    
    // Mobile-specific fields (preserved)
    middle_name: mobileUserData.middleName || mobileUserData.middle_name,
    last_name: mobileUserData.lastName || mobileUserData.last_name,
    age: mobileUserData.age,
    gender: mobileUserData.gender,
    civil_status: mobileUserData.civilStatus || mobileUserData.civil_status,
    birthdate: mobileUserData.birthdate,
    birthplace: mobileUserData.birthplace,
    religion: mobileUserData.religion,
    address: mobileUserData.address,
    control_number: mobileUserData.controlNumber || mobileUserData.control_number,
    has_completed_profile: mobileUserData.hasCompletedProfile || mobileUserData.has_completed_profile,
    verification_code_expires: mobileUserData.verification_code_expires,
    
    // Timestamps
    created_at: mobileUserData.created_at || new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
};

/**
 * Maps web database user data to mobile app format
 * @param {Object} webUserData - User data from web database
 * @returns {Object} - User data formatted for mobile app
 */
export const mapWebToMobile = (webUserData) => {
  return {
    // Mobile app format (camelCase)
    id: webUserData.id,
    firstName: webUserData.name,                                    // name → firstName
    middleName: webUserData.middle_name || "",
    lastName: webUserData.last_name || "",
    email: webUserData.email,
    emailVerified: webUserData.status || false,                     // status → emailVerified
    phone: webUserData.contactnumber || "",                         // contactnumber → phone
    controlNumber: webUserData.control_number || "",
    
    // Additional personal info
    age: webUserData.age || 0,
    gender: webUserData.gender || "",
    civilStatus: webUserData.civil_status || "",
    birthdate: webUserData.birthdate || "",
    birthplace: webUserData.birthplace || "",
    religion: webUserData.religion || "",
    address: webUserData.address || "",
    
    // App-specific
    hasCompletedProfile: webUserData.has_completed_profile || false,
    role: webUserData.role || 'patient',
    
    // Web-specific fields (for reference)
    username: webUserData.username,
    disable: webUserData.disable,
    createdAt: webUserData.created_at,
    updatedAt: webUserData.updated_at
  };
};

/**
 * Column mapping reference for developers
 */
export const COLUMN_MAPPING = {
  // Mobile → Web
  mobileToWeb: {
    'first_name': 'name',
    'email_verified': 'status', 
    'verification_code': 'activationcode',
    'phone': 'contactnumber'
  },
  
  // Web → Mobile
  webToMobile: {
    'name': 'first_name',
    'status': 'email_verified',
    'activationcode': 'verification_code', 
    'contactnumber': 'phone'
  }
};

/**
 * Get user data for database update (web format)
 * @param {Object} userData - User data from mobile app
 * @returns {Object} - Clean data for database update
 */
export const getUpdateData = (userData) => {
  const mapped = mapMobileToWeb(userData);
  
  // Remove null/undefined values and system fields
  const cleanData = {};
  Object.keys(mapped).forEach(key => {
    if (mapped[key] !== null && mapped[key] !== undefined && key !== 'id' && key !== 'created_at') {
      cleanData[key] = mapped[key];
    }
  });
  
  // Always update timestamp
  cleanData.updated_at = new Date().toISOString();
  
  return cleanData;
};

/**
 * Validate required fields for user creation
 * @param {Object} userData - User data to validate
 * @returns {Object} - Validation result
 */
export const validateUserData = (userData) => {
  const errors = [];
  
  if (!userData.email) errors.push('Email is required');
  if (!userData.password) errors.push('Password is required');
  if (!userData.firstName && !userData.name) errors.push('First name is required');
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

export default {
  mapMobileToWeb,
  mapWebToMobile,
  COLUMN_MAPPING,
  getUpdateData,
  validateUserData
};
