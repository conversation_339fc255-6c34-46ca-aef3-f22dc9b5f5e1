# 📧 MentalEase Email System - Portable Setup Guide

This guide explains how to use the MentalEase email verification system on any computer.

## 🚀 Quick Start (New Computer)

### 1. **Copy the Project**
```bash
# Copy the entire MentalEase folder to the new computer
# Make sure all files are included, especially:
# - email-server.js
# - app/config/email-config.js
# - setup-email-server.js
```

### 2. **Run Setup Script**
```bash
cd "path/to/MENTALEASE-MOBILE-APP"
node setup-email-server.js
```

This will:
- ✅ Detect your computer's network configuration
- ✅ Check email server setup
- ✅ Generate setup instructions
- ✅ Create a configuration file

### 3. **Install Dependencies**
```bash
npm install express nodemailer cors
```

### 4. **Configure Email Credentials**
Edit `email-server.js` and update:
```javascript
auth: {
  user: '<EMAIL>',        // Your Gmail address
  pass: 'your-app-password'            // Your Gmail App Password
}
```

### 5. **Start Email Server**
```bash
node email-server.js
```

You should see:
```
📧 Email server running on http://localhost:3001
📧 Ready to send verification emails via Gmail SMTP
✅ Email server ready to send emails
```

### 6. **Run React Native App**
```bash
npm start
# or
npx expo start
```

## 🔧 How It Works

### **Automatic Network Discovery**
The app automatically tries to connect to your email server using:

1. **Common IP ranges** (192.168.1.x, 192.168.0.x, etc.)
2. **Localhost variants** (127.0.0.1, localhost)
3. **Emulator addresses** (******** for Android)
4. **Corporate networks** (192.168.56.x, 10.0.0.x)

### **Smart Connection Logic**
```javascript
// The app tries each URL until one works:
const possibleUrls = [
  'http://************:3001/send-verification',  // Your Wi-Fi IP
  'http://************:3001/send-verification',  // Alternative range
  'http://localhost:3001/send-verification',     // Localhost
  // ... and many more
];
```

### **Fallback System**
If email sending fails:
- ✅ **Verification code shown in console**
- ✅ **User can still verify manually**
- ✅ **App continues to work**

## 📱 Testing the System

### **1. Create Account**
- Open the app
- Sign up with a new email
- Watch console logs

### **2. Check Console Logs**
Look for:
```
🔄 Trying to connect to: http://************:3001/send-verification
✅ Email sent successfully via: http://************:3001/send-verification
📧 User should receive the 6-digit code: 123456
```

### **3. Check Email**
- Check your Gmail inbox
- Look for "MentalEase - Email Verification Code"
- Use the 6-digit code to verify

## 🛠️ Troubleshooting

### **Email Server Not Found**
```
❌ All email server connections failed
```

**Solutions:**
1. Make sure email server is running: `node email-server.js`
2. Check firewall settings
3. Verify you're on the same network
4. Try running setup script: `node setup-email-server.js`

### **Email Not Received**
```
✅ Email sent successfully but no email received
```

**Solutions:**
1. Check spam/junk folder
2. Verify Gmail app password is correct
3. Check Gmail account settings
4. Use the console verification code as backup

### **Network Connection Issues**
```
❌ Connection failed to all URLs
```

**Solutions:**
1. Check if devices are on same Wi-Fi network
2. Disable VPN if active
3. Check router/firewall settings
4. Try different network (mobile hotspot)

## 🔄 Moving to Different Computers

### **What to Copy:**
- ✅ Entire project folder
- ✅ `email-server.js` with your credentials
- ✅ `node_modules` (or run `npm install`)
- ✅ All app files

### **What Changes Automatically:**
- ✅ Network IP detection
- ✅ Email server discovery
- ✅ Connection attempts

### **What You Need to Do:**
1. Run `node setup-email-server.js`
2. Start email server: `node email-server.js`
3. Start React Native app: `npm start`

## 📋 Configuration Files

### **email-config.js**
- Contains network discovery logic
- Automatically tries common IP ranges
- No manual configuration needed

### **email-server.js**
- Contains Gmail SMTP settings
- **Requires your email credentials**
- Runs on port 3001

### **setup-email-server.js**
- Diagnostic and setup tool
- Shows network configuration
- Generates instructions

## 🎯 Key Benefits

✅ **Portable** - Works on any computer  
✅ **Automatic** - Finds email server automatically  
✅ **Reliable** - Multiple fallback methods  
✅ **Secure** - Uses Gmail SMTP with app passwords  
✅ **Simple** - Minimal configuration required  

## 📞 Support

If you encounter issues:
1. Run `node setup-email-server.js` for diagnostics
2. Check console logs for connection attempts
3. Verify email server is running and accessible
4. Use backup verification codes if needed

The system is designed to be robust and work across different network configurations!
