// Test script to verify database connection and mood_entries table
// Run this in your app console or as a standalone test

import { supabaseUrl, supabaseAnonKey } from './app/lib/supabase.js';

const testDatabaseConnection = async () => {
  console.log('🔍 Testing database connection...');
  console.log('Supabase URL:', supabaseUrl);
  console.log('API Key (first 20 chars):', supabaseAnonKey.substring(0, 20) + '...');

  try {
    // Test 1: Check if mood_entries table exists
    console.log('\n📋 Test 1: Checking if mood_entries table exists...');
    const response = await fetch(`${supabaseUrl}/rest/v1/mood_entries?limit=1`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${supabaseAnonKey}`,
      },
    });

    if (response.ok) {
      console.log('✅ mood_entries table exists and is accessible');
      const data = await response.json();
      console.log('📊 Current entries in table:', data.length);
    } else {
      console.error('❌ Table access failed:', response.status, response.statusText);
      const errorText = await response.text();
      console.error('Error details:', errorText);
      return false;
    }

    // Test 2: Try to insert a test mood entry
    console.log('\n📝 Test 2: Trying to insert a test mood entry...');
    const testEntry = {
      user_email: '<EMAIL>',
      mood_value: 3,
      mood_label: 'Good',
      mood_emoji: '🙂',
      entry_date: new Date().toISOString().split('T')[0], // Today's date
    };

    const insertResponse = await fetch(`${supabaseUrl}/rest/v1/mood_entries`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Prefer': 'return=representation'
      },
      body: JSON.stringify(testEntry),
    });

    if (insertResponse.ok) {
      console.log('✅ Test mood entry inserted successfully');
      const insertedData = await insertResponse.json();
      console.log('📊 Inserted data:', insertedData);
    } else {
      console.error('❌ Insert failed:', insertResponse.status, insertResponse.statusText);
      const errorText = await insertResponse.text();
      console.error('Error details:', errorText);
    }

    // Test 3: Try to fetch the test entry
    console.log('\n🔍 Test 3: Trying to fetch test entries...');
    const fetchResponse = await fetch(
      `${supabaseUrl}/rest/v1/mood_entries?user_email=<EMAIL>`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
          'Authorization': `Bearer ${supabaseAnonKey}`,
        },
      }
    );

    if (fetchResponse.ok) {
      const fetchedData = await fetchResponse.json();
      console.log('✅ Fetch successful');
      console.log('📊 Fetched entries:', fetchedData.length);
      console.log('📋 Data:', fetchedData);
    } else {
      console.error('❌ Fetch failed:', fetchResponse.status, fetchResponse.statusText);
    }

    // Test 4: Clean up test entry
    console.log('\n🧹 Test 4: Cleaning up test entry...');
    const deleteResponse = await fetch(
      `${supabaseUrl}/rest/v1/mood_entries?user_email=<EMAIL>`,
      {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
          'Authorization': `Bearer ${supabaseAnonKey}`,
        },
      }
    );

    if (deleteResponse.ok) {
      console.log('✅ Test entry cleaned up successfully');
    } else {
      console.log('⚠️ Cleanup warning (not critical):', deleteResponse.status);
    }

    console.log('\n🎉 Database connection test completed!');
    return true;

  } catch (error) {
    console.error('❌ Database connection test failed:', error);
    return false;
  }
};

// Export for use in other files
export { testDatabaseConnection };

// If running directly, execute the test
if (typeof window !== 'undefined') {
  // Browser environment
  console.log('Run testDatabaseConnection() in your browser console');
} else {
  // Node environment
  testDatabaseConnection();
}
