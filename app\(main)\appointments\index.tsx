import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Image,
  Modal,
  Platform,
  Alert,
  TextInput,
  StatusBar
} from 'react-native';
import { useState, useEffect } from 'react';
import { useRouter, useFocusEffect } from 'expo-router';
import { useCallback } from 'react';
import { LinearGradient } from 'expo-linear-gradient';
import { useAppointment } from '../../context/AppointmentContext';
import BottomNavigation from '../../components/BottomNavigation';
import StandardHeader from '../../components/StandardHeader';

const Appointments = () => {
  const router = useRouter();
  const {
    getUpcomingAppointments,
    getPastAppointments,
    cancelAppointment,
    fetchAppointments,
    fetchAllAppointments,
    appointments,
    upcomingAppointments,
    pastAppointments,
    psychometricians
  } = useAppointment();

  const [activeTab, setActiveTab] = useState('upcoming');
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [selectedDoctor, setSelectedDoctor] = useState(null);
  const [selectedDate, setSelectedDate] = useState('Friday\n6\nJune');
  const [selectedTime, setSelectedTime] = useState('8:00 am');
  const [appointmentNotes, setAppointmentNotes] = useState('');
  const [currentMonth, setCurrentMonth] = useState('June');

  // Get available specialists from context (real data from database)

  // Transform psychometricians data to match the expected format
  const availableSpecialists = psychometricians.map(psychometrician => ({
    id: psychometrician.id,
    name: psychometrician.name,
    specialty: psychometrician.specialty || 'Psychometrician',
    avatar: psychometrician.name?.charAt(0)?.toUpperCase() || 'P',
    available: psychometrician.available !== false
  }));

  // Fetch appointments when component mounts and when tab changes
  useEffect(() => {
    const loadAppointments = async () => {
      try {
        await fetchAppointments();
        await fetchAllAppointments();
      } catch (error) {
        console.error('Error loading appointments:', error);
      }
    };

    loadAppointments();
  }, [activeTab]); // Refetch when tab changes

  // Refresh appointments when screen comes into focus (e.g., after booking)
  useFocusEffect(
    useCallback(() => {
      let isMounted = true;

      const refreshAppointments = async () => {
        if (!isMounted) return;

        try {
          console.log('🔄 Refreshing appointments data...');
          await fetchAppointments();
          await fetchAllAppointments();
          console.log('✅ Appointments refreshed successfully');
        } catch (error) {
          console.error('❌ Error refreshing appointments:', error);
        }
      };

      // Add a small delay to ensure any database operations have completed
      const timeoutId = setTimeout(refreshAppointments, 100);

      return () => {
        isMounted = false;
        clearTimeout(timeoutId);
      };
    }, [])
  );

  // Use the appointments from context
  const currentUpcomingAppointments = getUpcomingAppointments();
  const currentPastAppointments = getPastAppointments();

  const handleAppointmentPress = (appointment) => {
    setSelectedAppointment(appointment);
    setShowDetailsModal(true);
  };

  const handleBookAppointment = (doctor) => {
    setSelectedDoctor(doctor);
    setShowBookingModal(true);
  };

  const handleDateSelect = (date) => {
    setSelectedDate(date);
  };

  const handleTimeChange = (direction) => {
    const times = ['8:00 am', '9:00 am', '10:00 am', '11:00 am', '12:00 pm', '1:00 pm', '2:00 pm', '3:00 pm', '4:00 pm', '5:00 pm'];
    const currentIndex = times.indexOf(selectedTime);
    if (direction === 'up' && currentIndex > 0) {
      setSelectedTime(times[currentIndex - 1]);
    } else if (direction === 'down' && currentIndex < times.length - 1) {
      setSelectedTime(times[currentIndex + 1]);
    }
  };

  const handleMonthChange = (direction) => {
    const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
    const currentIndex = months.indexOf(currentMonth);
    if (direction === 'up' && currentIndex > 0) {
      setCurrentMonth(months[currentIndex - 1]);
    } else if (direction === 'down' && currentIndex < months.length - 1) {
      setCurrentMonth(months[currentIndex + 1]);
    }
  };

  const handleCancelAppointment = () => {
    setShowCancelModal(false);
    setShowDetailsModal(false);

    try {
      cancelAppointment(selectedAppointment.id);
      Alert.alert(
        "Appointment Cancelled",
        "Your appointment has been successfully cancelled."
      );
    } catch (error) {
      console.error('Error cancelling appointment:', error);
      Alert.alert(
        "Error",
        "There was an error cancelling your appointment. Please try again."
      );
    }
  };

  const formatAppointmentDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const renderUpcomingContent = () => {
    const upcomingAppts = currentUpcomingAppointments; // Show all upcoming appointments

    return (
      <View>
        {/* Upcoming Appointment Section */}
        {upcomingAppts.length > 0 ? (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Upcoming Appointments</Text>
            {upcomingAppts.map((appointment) => {
              const psychometrician = appointment.psychometrician;
              const appointmentDate = new Date(appointment.date);
              const formattedDate = appointmentDate.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric'
              });

              // Format time from 24-hour to 12-hour format
              const formatTime = (timeStr) => {
                const [hours, minutes] = timeStr.split(':');
                const hour = parseInt(hours);
                const ampm = hour >= 12 ? 'pm' : 'am';
                const displayHour = hour > 12 ? hour - 12 : hour === 0 ? 12 : hour;
                return `${displayHour}:${minutes} ${ampm}`;
              };

              return (
                <View key={appointment.id} style={styles.appointmentCard}>
                  <View style={styles.doctorInfo}>
                    <View style={styles.doctorAvatar}>
                      <Text style={styles.doctorAvatarText}>
                        {psychometrician?.name?.charAt(0)?.toUpperCase() || 'P'}
                      </Text>
                    </View>
                    <View style={styles.doctorDetails}>
                      <Text style={styles.doctorName}>
                        {psychometrician?.name || 'Psychometrician'}
                      </Text>
                      <Text style={styles.doctorSpecialty}>
                        {psychometrician?.specialty || 'Psychometrician'}
                      </Text>
                    </View>
                    <View style={styles.appointmentTime}>
                      <Text style={styles.appointmentDate}>{formattedDate}</Text>
                      <Text style={styles.appointmentTimeText}>
                        {formatTime(appointment.start_time)}
                      </Text>
                    </View>
                  </View>
                  <TouchableOpacity
                    style={styles.rescheduleButton}
                    onPress={() => {
                      router.push(`/(main)/appointments/reschedule?id=${appointment.id}`);
                    }}
                  >
                    <Text style={styles.rescheduleButtonText}>Reschedule</Text>
                  </TouchableOpacity>
                </View>
              );
            })}
          </View>
        ) : (
          // Show message when no appointments exist
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Upcoming Appointment</Text>
            <View style={styles.noAppointmentsContainer}>
              <Text style={styles.noAppointmentsText}>No upcoming appointments</Text>
              <TouchableOpacity
                style={styles.bookButton}
                onPress={() => router.push('/(main)/appointments/schedule')}
              >
                <Text style={styles.bookButtonText}>Book Appointment</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    );
  };

  const renderPastContent = () => {
    const pastAppts = currentPastAppointments;

    if (pastAppts.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateTitle}>No Past Appointments</Text>
          <Text style={styles.emptyStateText}>
            Your completed appointments will appear here.
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.pastAppointmentsList}>
        {pastAppts.map((appointment) => {
          const psychometrician = appointment.psychometrician;
          const appointmentDate = new Date(appointment.date);
          const formattedDate = appointmentDate.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
          });

          // Format time from 24-hour to 12-hour format
          const formatTime = (timeStr) => {
            const [hours, minutes] = timeStr.split(':');
            const hour = parseInt(hours);
            const ampm = hour >= 12 ? 'pm' : 'am';
            const displayHour = hour > 12 ? hour - 12 : hour === 0 ? 12 : hour;
            return `${displayHour}:${minutes} ${ampm}`;
          };

          return (
            <View key={appointment.id} style={styles.pastAppointmentCard}>
              <View style={styles.pastAppointmentHeader}>
                <View style={styles.doctorInfo}>
                  <View style={styles.doctorAvatar}>
                    <Text style={styles.doctorAvatarText}>
                      {psychometrician?.name?.charAt(0)?.toUpperCase() || 'P'}
                    </Text>
                  </View>
                  <View style={styles.doctorDetails}>
                    <Text style={styles.doctorName}>
                      {psychometrician?.name || 'Psychometrician'}
                    </Text>
                    <Text style={styles.doctorSpecialty}>
                      {psychometrician?.specialty || 'Psychometrician'}
                    </Text>
                  </View>
                </View>
                <View style={styles.completedBadge}>
                  <Text style={styles.completedBadgeText}>
                    {appointment.status === 'completed' ? 'Completed' :
                     appointment.status === 'cancelled' ? 'Cancelled' :
                     'Past'}
                  </Text>
                </View>
              </View>
              <View style={styles.pastAppointmentDetails}>
                <Text style={styles.pastAppointmentDate}>{formattedDate}</Text>
                <Text style={styles.pastAppointmentTime}>{formatTime(appointment.start_time)}</Text>
                <Text style={styles.pastAppointmentNotes}>{appointment.notes}</Text>
              </View>
            </View>
          );
        })}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <StandardHeader title="Appointment" />

      <SafeAreaView style={styles.safeArea}>
        {/* Tab Container */}
        <View style={styles.tabContainer}>
          <View style={styles.tabButtons}>
            <TouchableOpacity
              style={[styles.tabButton, activeTab === 'upcoming' && styles.activeTabButton]}
              onPress={() => setActiveTab('upcoming')}
            >
              <Text style={[styles.tabText, activeTab === 'upcoming' && styles.activeTabText]}>
                Upcoming
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tabButton, activeTab === 'past' && styles.activeTabButton]}
              onPress={() => setActiveTab('past')}
            >
              <Text style={[styles.tabText, activeTab === 'past' && styles.activeTabText]}>
                Past
              </Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            style={styles.newButton}
            onPress={() => router.push('/(main)/appointments/schedule')}
          >
            <Text style={styles.newButtonText}>+ New</Text>
          </TouchableOpacity>
        </View>

        <ScrollView contentContainerStyle={styles.content}>
          {activeTab === 'upcoming' ? renderUpcomingContent() : renderPastContent()}
        </ScrollView>
      </SafeAreaView>

      {/* Appointment Details Modal */}
      <Modal
        visible={showDetailsModal}
        transparent={true}
        animationType="none"
      >
        {selectedAppointment && (
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Appointment Details</Text>
                <TouchableOpacity
                  onPress={() => setShowDetailsModal(false)}
                  style={styles.closeButton}
                >
                  <Text style={styles.closeButtonText}>✕</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.modalTherapistInfo}>
                <Text style={styles.modalTherapistImage}>{selectedAppointment.psychometrician?.image || '👨‍⚕️'}</Text>
                <View>
                  <Text style={styles.modalTherapistName}>{selectedAppointment.psychometrician?.name || 'Psychometrician'}</Text>
                  <Text style={styles.modalTherapistSpecialty}>{selectedAppointment.psychometrician?.specialty || 'Mental Health Professional'}</Text>
                  <Text style={styles.modalTherapistExperience}>{selectedAppointment.psychometrician?.experience || '5+ years'} • ⭐ {selectedAppointment.psychometrician?.rating || '4.8'}</Text>
                </View>
              </View>

              <View style={styles.modalAppointmentDetails}>
                <View style={styles.modalDetailItem}>
                  <Text style={styles.modalDetailLabel}>Date:</Text>
                  <Text style={styles.modalDetailValue}>{formatAppointmentDate(selectedAppointment.date)}</Text>
                </View>
                <View style={styles.modalDetailItem}>
                  <Text style={styles.modalDetailLabel}>Time:</Text>
                  <Text style={styles.modalDetailValue}>{selectedAppointment.start_time} - {selectedAppointment.end_time}</Text>
                </View>
                <View style={styles.modalDetailItem}>
                  <Text style={styles.modalDetailLabel}>Status:</Text>
                  <View style={[
                    styles.modalStatusBadge,
                    selectedAppointment.status === 'scheduled' ? styles.scheduledBadge :
                    selectedAppointment.status === 'completed' ? styles.completedBadge :
                    styles.cancelledBadge
                  ]}>
                    <Text style={styles.modalStatusText}>
                      {selectedAppointment.status.charAt(0).toUpperCase() + selectedAppointment.status.slice(1)}
                    </Text>
                  </View>
                </View>
                {selectedAppointment.notes && (
                  <View style={styles.modalDetailItem}>
                    <Text style={styles.modalDetailLabel}>Notes:</Text>
                    <Text style={styles.modalDetailValue}>{selectedAppointment.notes}</Text>
                  </View>
                )}
              </View>

              {/* Previous Assessment Section */}
              {selectedAppointment.status === 'completed' && (
                <View style={styles.assessmentSection}>
                  <Text style={styles.assessmentSectionTitle}>Previous Assessment</Text>
                  <View style={styles.assessmentCard}>
                    <View style={styles.assessmentHeader}>
                      <Text style={styles.assessmentDate}>
                        Completed on {formatAppointmentDate(selectedAppointment.date)}
                      </Text>
                      <View style={styles.assessmentScoreBadge}>
                        <Text style={styles.assessmentScoreText}>Score: 72/100</Text>
                      </View>
                    </View>

                    <View style={styles.assessmentResultsContainer}>
                      <View style={styles.assessmentResultItem}>
                        <Text style={styles.assessmentResultLabel}>Anxiety:</Text>
                        <View style={styles.assessmentResultBar}>
                          <View style={[styles.assessmentResultFill, { width: '45%', backgroundColor: '#FFC107' }]} />
                        </View>
                        <Text style={styles.assessmentResultValue}>Moderate</Text>
                      </View>

                      <View style={styles.assessmentResultItem}>
                        <Text style={styles.assessmentResultLabel}>Depression:</Text>
                        <View style={styles.assessmentResultBar}>
                          <View style={[styles.assessmentResultFill, { width: '30%', backgroundColor: '#4CAF50' }]} />
                        </View>
                        <Text style={styles.assessmentResultValue}>Mild</Text>
                      </View>

                      <View style={styles.assessmentResultItem}>
                        <Text style={styles.assessmentResultLabel}>Stress:</Text>
                        <View style={styles.assessmentResultBar}>
                          <View style={[styles.assessmentResultFill, { width: '60%', backgroundColor: '#FF5722' }]} />
                        </View>
                        <Text style={styles.assessmentResultValue}>High</Text>
                      </View>
                    </View>

                    <TouchableOpacity
                      style={styles.viewFullAssessmentButton}
                      onPress={() => {
                        setShowDetailsModal(false);
                        router.replace('/(main)/mental-health/assessment/results');
                      }}
                    >
                      <Text style={styles.viewFullAssessmentButtonText}>View Full Assessment</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}

              {selectedAppointment.status === 'scheduled' && (
                <View style={styles.modalActions}>
                  <TouchableOpacity
                    style={styles.rescheduleButton}
                    onPress={() => {
                      setShowDetailsModal(false);
                      router.push(`/(main)/appointments/reschedule?id=${selectedAppointment.id}`);
                    }}
                  >
                    <Text style={styles.rescheduleButtonText}>Reschedule</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.cancelAppointmentButton}
                    onPress={() => {
                      setShowCancelModal(true);
                    }}
                  >
                    <Text style={styles.cancelAppointmentButtonText}>Cancel Appointment</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>
        )}
      </Modal>

      {/* Cancel Confirmation Modal */}
      <Modal
        visible={showCancelModal}
        transparent={true}
        animationType="none"
      >
        <View style={styles.modalOverlay}>
          <View style={styles.confirmModalContent}>
            <Text style={styles.confirmModalTitle}>Cancel Appointment?</Text>
            <Text style={styles.confirmModalText}>
              Are you sure you want to cancel this appointment? This action cannot be undone.
            </Text>
            <View style={styles.confirmModalButtons}>
              <TouchableOpacity
                style={styles.confirmModalCancelButton}
                onPress={() => setShowCancelModal(false)}
              >
                <Text style={styles.confirmModalCancelButtonText}>No, Keep It</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.confirmModalConfirmButton}
                onPress={handleCancelAppointment}
              >
                <Text style={styles.confirmModalConfirmButtonText}>Yes, Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Booking Modal */}
      <Modal
        visible={showBookingModal}
        transparent={true}
        animationType="slide"
      >
        <StatusBar backgroundColor="#000000" barStyle="light-content" />
        <View style={styles.bookingModalOverlay}>
          <View style={styles.bookingModalContent}>
            <StandardHeader
              title="Appointment"
              onBackPress={() => {
                setShowBookingModal(false);
                setSelectedDoctor(null);
              }}
            />

            {selectedDoctor ? (
              // Show booking form when doctor is selected
              <ScrollView style={styles.bookingFormContainer}>
                <Text style={styles.bookingFormTitle}>Select Date</Text>

                {/* Date Selection */}
                <View style={styles.dateSelectionContainer}>
                  {[
                    { day: 'Friday', date: '6', month: 'June' },
                    { day: 'Saturday', date: '7', month: 'June' },
                    { day: 'Monday', date: '9', month: 'June' },
                    { day: 'Tuesday', date: '10', month: 'June' }
                  ].map((dateOption, index) => (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.dateOption,
                        selectedDate === `${dateOption.day}\n${dateOption.date}\n${dateOption.month}` && styles.selectedDateOption
                      ]}
                      onPress={() => setSelectedDate(`${dateOption.day}\n${dateOption.date}\n${dateOption.month}`)}
                    >
                      <Text
                        style={[
                          styles.dateOptionText,
                          selectedDate === `${dateOption.day}\n${dateOption.date}\n${dateOption.month}` && styles.selectedDateOptionText
                        ]}
                        numberOfLines={1}
                        adjustsFontSizeToFit={true}
                        minimumFontScale={0.8}
                      >
                        {dateOption.day}
                      </Text>
                      <Text style={[
                        styles.dateOptionNumber,
                        selectedDate === `${dateOption.day}\n${dateOption.date}\n${dateOption.month}` && styles.selectedDateOptionText
                      ]}>
                        {dateOption.date}
                      </Text>
                      <Text
                        style={[
                          styles.dateOptionMonth,
                          selectedDate === `${dateOption.day}\n${dateOption.date}\n${dateOption.month}` && styles.selectedDateOptionText
                        ]}
                        numberOfLines={1}
                        adjustsFontSizeToFit={true}
                        minimumFontScale={0.8}
                      >
                        {dateOption.month}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>

                <Text style={styles.bookingFormTitle}>Select Time</Text>

                {/* Time Selection */}
                <View style={styles.timeSelectionContainer}>
                  {['8:00 am', '10:30 am', '1:30 pm', '3:00 pm'].map((timeOption, index) => (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.timeOption,
                        selectedTime === timeOption && styles.selectedTimeOption
                      ]}
                      onPress={() => setSelectedTime(timeOption)}
                    >
                      <Text style={[
                        styles.timeOptionText,
                        selectedTime === timeOption && styles.selectedTimeOptionText
                      ]}>
                        {timeOption}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>

                <Text style={styles.bookingFormTitle}>Additional Notes</Text>

                {/* Notes Input */}
                <TextInput
                  style={styles.notesInput}
                  placeholder="Add any notes for your psychometrician..."
                  placeholderTextColor="#999"
                  multiline
                  numberOfLines={4}
                  value={appointmentNotes}
                  onChangeText={setAppointmentNotes}
                />

                {/* Book Appointment Button */}
                <TouchableOpacity
                  style={styles.bookAppointmentButton}
                  onPress={() => {
                    // Handle booking logic here
                    setShowBookingModal(false);
                    setSelectedDoctor(null);
                    setAppointmentNotes('');
                  }}
                >
                  <Text style={styles.bookAppointmentButtonText}>Book Appointment</Text>
                </TouchableOpacity>
              </ScrollView>
            ) : (
              // Show available specialists when no doctor is selected
              <View style={styles.specialistListContainer}>
                <Text style={styles.specialistListTitle}>Available Specialist</Text>
                <ScrollView showsVerticalScrollIndicator={false}>
                  {availableSpecialists.map((doctor, index) => (
                    <View key={index} style={styles.specialistModalCard}>
                      <View style={styles.specialistCardContent}>
                        <View style={styles.doctorAvatar}>
                          <Text style={styles.doctorAvatarText}>A</Text>
                        </View>
                        <View style={styles.doctorDetails}>
                          <Text style={styles.doctorName}>{doctor.name}</Text>
                          <Text style={styles.doctorSpecialty}>{doctor.specialty}</Text>
                        </View>
                      </View>
                      <TouchableOpacity
                        style={styles.modalBookButton}
                        onPress={() => handleBookAppointment(doctor)}
                      >
                        <Text style={styles.modalBookButtonText}>Book</Text>
                      </TouchableOpacity>
                    </View>
                  ))}
                </ScrollView>
              </View>
            )}
          </View>
        </View>
      </Modal>



      <BottomNavigation />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  safeArea: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  tabButtons: {
    flexDirection: 'row',
  },
  tabButton: {
    paddingVertical: 8,
    paddingHorizontal: 20,
    marginRight: 20,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTabButton: {
    borderBottomColor: '#6B9B7A',
  },
  tabText: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#2C3E50',
    fontWeight: 'bold',
  },
  newButton: {
    backgroundColor: '#5c7e74',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  newButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    paddingBottom: 100,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 30,
    marginTop: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 20,
  },
  appointmentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  specialistCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  doctorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    flex: 1,
    marginRight: 10,
  },
  doctorAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F0A500',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  doctorAvatarText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
  },
  doctorDetails: {
    flex: 1,
  },
  doctorName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 5,
  },
  doctorSpecialty: {
    fontSize: 14,
    color: '#666',
  },
  appointmentTime: {
    alignItems: 'flex-end',
  },
  appointmentDate: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 5,
  },
  appointmentTimeText: {
    fontSize: 14,
    color: '#666',
  },
  rescheduleButton: {
    backgroundColor: '#5c7e74',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 30,
    alignItems: 'center',
  },
  rescheduleButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  bookButton: {
    backgroundColor: '#5c7e74',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 30,
    alignItems: 'center',
  },
  bookButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  pastAppointmentsList: {
    paddingHorizontal: 20,
    paddingBottom: 100,
    paddingTop: 30,
  },
  pastAppointmentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  pastAppointmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 15,
    minHeight: 50,
  },
  completedBadge: {
    backgroundColor: '#5c7e74',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    alignSelf: 'flex-start',
    marginTop: 5,
    minWidth: 80,
    alignItems: 'center',
  },
  completedBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  pastAppointmentDetails: {
    marginTop: 10,
  },
  pastAppointmentDate: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 5,
  },
  pastAppointmentTime: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  pastAppointmentNotes: {
    fontSize: 14,
    color: '#666',
  },
  // Booking Modal Styles
  bookingModalOverlay: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  bookingModalContent: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },


  specialistListContainer: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    paddingTop: 30,
  },
  specialistListTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 25,
    paddingHorizontal: 20,
  },
  specialistModalCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  specialistCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  modalBookButton: {
    backgroundColor: '#5c7e74',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 30,
    alignItems: 'center',
    minWidth: 120,
  },
  modalBookButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  bookingFormContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  bookingFormTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 15,
    marginTop: 10,
  },
  dateSelectionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  dateOption: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 12,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    minHeight: 80,
    justifyContent: 'center',
  },
  selectedDateOption: {
    backgroundColor: '#5c7e74',
  },
  dateOptionText: {
    fontSize: 10,
    color: '#666',
    marginBottom: 3,
    textAlign: 'center',
    flexShrink: 1,
  },
  dateOptionNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 3,
    textAlign: 'center',
  },
  dateOptionMonth: {
    fontSize: 10,
    color: '#666',
    textAlign: 'center',
    flexShrink: 1,
  },
  selectedDateOptionText: {
    color: '#FFFFFF',
  },
  timeSelectionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  timeOption: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 1,
    marginHorizontal: 5,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedTimeOption: {
    backgroundColor: '#5c7e74',
  },
  timeOptionText: {
    fontSize: 14,
    color: '#2C3E50',
    fontWeight: '500',
  },
  selectedTimeOptionText: {
    color: '#FFFFFF',
  },
  notesInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 15,
    fontSize: 16,
    color: '#2C3E50',
    textAlignVertical: 'top',
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  bookAppointmentButton: {
    backgroundColor: '#5c7e74',
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
    marginBottom: 30,
  },
  bookAppointmentButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 20,
    margin: 20,
    maxHeight: '80%',
    width: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  closeButton: {
    padding: 5,
  },
  closeButtonText: {
    fontSize: 18,
    color: '#666',
    fontWeight: 'bold',
  },
  modalTherapistInfo: {
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTherapistImage: {
    fontSize: 40,
    marginBottom: 10,
  },
  modalTherapistName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 5,
  },
  modalTherapistSpecialty: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  modalTherapistExperience: {
    fontSize: 14,
    color: '#666',
  },
  modalAppointmentDetails: {
    marginBottom: 20,
  },
  modalDetailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  modalDetailLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  modalDetailValue: {
    fontSize: 14,
    color: '#2C3E50',
    fontWeight: '600',
  },
  modalStatusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  scheduledBadge: {
    backgroundColor: '#E8F5E8',
  },
  cancelledBadge: {
    backgroundColor: '#FFE8E8',
  },
  modalStatusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  assessmentSection: {
    marginTop: 20,
    padding: 15,
    backgroundColor: '#F8F9FA',
    borderRadius: 10,
  },
  assessmentSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 10,
  },
  assessmentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 15,
  },
  assessmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  assessmentDate: {
    fontSize: 14,
    color: '#666',
  },
  assessmentScoreBadge: {
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  assessmentScoreText: {
    fontSize: 12,
    color: '#4CAF50',
    fontWeight: '600',
  },
  assessmentResultsContainer: {
    marginBottom: 15,
  },
  assessmentResultItem: {
    marginBottom: 10,
  },
  assessmentResultLabel: {
    fontSize: 14,
    color: '#2C3E50',
    marginBottom: 5,
  },
  assessmentResultBar: {
    height: 8,
    backgroundColor: '#F0F0F0',
    borderRadius: 4,
    marginBottom: 5,
  },
  assessmentResultFill: {
    height: '100%',
    borderRadius: 4,
  },
  assessmentResultValue: {
    fontSize: 12,
    color: '#666',
  },
  viewFullAssessmentButton: {
    backgroundColor: '#66948a',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    alignItems: 'center',
  },
  viewFullAssessmentButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  cancelAppointmentButton: {
    backgroundColor: '#FF6B6B',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 1,
    alignItems: 'center',
  },
  cancelAppointmentButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  confirmModalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 20,
    margin: 20,
    alignItems: 'center',
  },
  confirmModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 10,
  },
  confirmModalText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  confirmModalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  confirmModalCancelButton: {
    backgroundColor: '#E0E0E0',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 1,
    marginRight: 10,
    alignItems: 'center',
  },
  confirmModalCancelButtonText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '600',
  },
  confirmModalConfirmButton: {
    backgroundColor: '#FF6B6B',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 1,
    marginLeft: 10,
    alignItems: 'center',
  },
  confirmModalConfirmButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  noAppointmentsContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 30,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  noAppointmentsText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
    textAlign: 'center',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 10,
    textAlign: 'center',
  },
  emptyStateText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },

});

export default Appointments;
