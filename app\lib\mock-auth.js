// Mock authentication service - no Supabase dependency

/**
 * Sign up a new user with email and password
 * @param {string} email - User's email
 * @param {string} password - User's password
 * @returns {Promise<Object>} - Response with user data or error
 */
export const signUp = async (email, password) => {
  try {
    console.log('Mock sign up with:', email);
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Create mock user data
    const userData = {
      id: 'user_' + Math.random().toString(36).substring(2),
      email: email,
      created_at: new Date().toISOString()
    };
    
    return { data: userData, error: null };
  } catch (error) {
    console.error('Sign up error:', error);
    return { data: null, error };
  }
};

/**
 * Sign in a user with email and password
 * @param {string} email - User's email
 * @param {string} password - User's password
 * @returns {Promise<Object>} - Response with user data or error
 */
export const signIn = async (email, password) => {
  try {
    console.log('Mock sign in with:', email);
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Create mock user data
    const userData = {
      id: 'user_' + Math.random().toString(36).substring(2),
      email: email,
      created_at: new Date().toISOString()
    };
    
    return { data: userData, error: null };
  } catch (error) {
    console.error('Sign in error:', error);
    return { data: null, error };
  }
};

/**
 * Sign out the current user
 * @returns {Promise<Object>} - Response with success or error
 */
export const signOut = async () => {
  try {
    console.log('Mock sign out');
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return { error: null };
  } catch (error) {
    console.error('Sign out error:', error);
    return { error };
  }
};

// Export the auth service
export const authService = {
  signUp,
  signIn,
  signOut
};

export default authService;

