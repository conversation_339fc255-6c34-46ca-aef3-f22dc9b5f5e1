import React, { useState, useEffect } from "react";
import {
  SafeAreaView,
  TouchableOpacity,
  Text,
  TextInput,
  View,
  StyleSheet,
  StatusBar,
  FlatList,
  Alert,
  Platform,
  PermissionsAndroid,
} from "react-native";
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

// Import VideoSDK modules directly
import {
  MeetingProvider,
  useMeeting,
  useParticipant,
  MediaStream,
  RTCView
} from "@videosdk.live/react-native-sdk";

import { createMeeting, token } from "../api";

function JoinScreen(props) {
  const [meetingVal, setMeetingVal] = useState("");
  const router = useRouter();

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#66948a" barStyle="light-content" />
      
      {/* Header */}
      <LinearGradient
        colors={['#66948a', '#66948a']}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Video Consultation</Text>
        <View style={styles.headerSpacer} />
      </LinearGradient>

      <View style={styles.content}>
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeTitle}>Start Your Session</Text>
          <Text style={styles.welcomeSubtitle}>
            Connect with mental health professionals through secure video calls
          </Text>
        </View>

        <TouchableOpacity
          onPress={() => {
            props.getMeetingId();
          }}
          style={styles.createMeetingButton}
        >
          <LinearGradient
            colors={['#4A90E2', '#357ABD']}
            style={styles.buttonGradient}
          >
            <Text style={styles.buttonText}>Create New Meeting</Text>
          </LinearGradient>
        </TouchableOpacity>

        <View style={styles.divider}>
          <View style={styles.dividerLine} />
          <Text style={styles.dividerText}>OR</Text>
          <View style={styles.dividerLine} />
        </View>

        <View style={styles.joinSection}>
          <Text style={styles.joinTitle}>Join Existing Meeting</Text>
          <TextInput
            value={meetingVal}
            onChangeText={setMeetingVal}
            placeholder="Enter Meeting ID (XXXX-XXXX-XXXX)"
            placeholderTextColor="#999"
            style={styles.meetingInput}
          />
          <TouchableOpacity
            style={styles.joinMeetingButton}
            onPress={() => {
              props.getMeetingId(meetingVal);
            }}
          >
            <Text style={styles.joinButtonText}>Join Meeting</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const Button = ({ onPress, buttonText, backgroundColor }) => {
  const handlePress = () => {
    console.log(`Button "${buttonText}" pressed`);
    if (onPress) {
      onPress();
    } else {
      console.log(`No onPress function provided for "${buttonText}"`);
    }
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      style={[styles.controlButton, { backgroundColor }]}
    >
      <Text style={styles.controlButtonText}>{buttonText}</Text>
    </TouchableOpacity>
  );
};

function ControlsContainer({ join, leave, toggleWebcam, toggleMic, participantsArrId, localParticipant, setPreventAutoLeave, hasJoined }) {
  const handleJoin = () => {
    console.log('🔗 Manual join button pressed');
    console.log('🔗 Attempting to join existing meeting...');
    try {
      join();
      console.log('🔗 Join function called successfully');
    } catch (error) {
      console.log('❌ Error joining meeting:', error);
    }
  };

  const handleToggleWebcam = () => {
    console.log('Camera button pressed');
    console.log('toggleWebcam function type:', typeof toggleWebcam);
    console.log('toggleWebcam function:', toggleWebcam);
    try {
      const result = toggleWebcam();
      console.log('toggleWebcam result:', result);
    } catch (error) {
      console.log('Error toggling webcam:', error);
    }
  };

  const handleToggleMic = () => {
    console.log('Mic button pressed');
    console.log('toggleMic function type:', typeof toggleMic);
    console.log('toggleMic function:', toggleMic);
    try {
      const result = toggleMic();
      console.log('toggleMic result:', result);
    } catch (error) {
      console.log('Error toggling mic:', error);
    }
  };

  const handleLeave = () => {
    console.log('Leave button pressed');
    console.log('leave function type:', typeof leave);
    console.log('leave function:', leave);

    // Mark this as an intentional leave BEFORE calling leave()
    if (setPreventAutoLeave) {
      console.log('🔴 Setting intentional leave flag');
      setPreventAutoLeave(true);
    }

    try {
      const result = leave();
      console.log('leave result:', result);
    } catch (error) {
      console.log('Error leaving meeting:', error);
    }
  };

  return (
    <View style={styles.controlsContainer}>
      {!hasJoined && (
        <Button
          onPress={handleJoin}
          buttonText="Join Meeting"
          backgroundColor="#28a745"
        />
      )}
      {hasJoined && (
        <>
          <Button
            onPress={handleToggleWebcam}
            buttonText="Camera"
            backgroundColor="#4A90E2"
          />
          <Button
            onPress={handleToggleMic}
            buttonText="Mic"
            backgroundColor="#4A90E2"
          />
        </>
      )}
      <Button
        onPress={handleLeave}
        buttonText="Leave"
        backgroundColor="#FF4757"
      />
    </View>
  );
}

function ParticipantView({ participantId }) {
  const { webcamStream, webcamOn } = useParticipant(participantId);

  return webcamOn && webcamStream ? (
    <RTCView
      streamURL={new MediaStream([webcamStream.track]).toURL()}
      objectFit="cover"
      style={styles.participantVideo}
    />
  ) : (
    <View style={styles.noVideoContainer}>
      <Text style={styles.noVideoText}>Camera Off</Text>
    </View>
  );
}

function ParticipantList({ participants }) {
  console.log('ParticipantList participants:', participants, 'length:', participants?.length);

  return participants && participants.length > 0 ? (
    <FlatList
      data={participants}
      renderItem={({ item }) => (
        <ParticipantView participantId={item} />
      )}
      style={styles.participantsList}
    />
  ) : (
    <View style={styles.waitingContainer}>
      <Text style={styles.waitingText}>Ready to join meeting</Text>
      <Text style={styles.waitingSubtext}>Meeting ID: {meetingId}</Text>
      <Text style={styles.waitingSubtext}>Press "Join Meeting" to connect</Text>
    </View>
  );
}

function MeetingView() {
  const [hasJoined, setHasJoined] = useState(false);
  const [preventAutoLeave, setPreventAutoLeave] = useState(false);
  const [connectionTimeout, setConnectionTimeout] = useState(false);

  // Add connection timeout
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!hasJoined) {
        console.log('⏰ Connection timeout - meeting taking too long to join');
        setConnectionTimeout(true);
      }
    }, 10000); // 10 second timeout

    return () => clearTimeout(timer);
  }, [hasJoined]);

  const { join, leave, toggleWebcam, toggleMic, participants, localParticipant } = useMeeting({
    onError: (error) => {
      console.log('❌ Meeting error:', error);
      console.log('❌ Error details:', JSON.stringify(error, null, 2));
    },
    onMeetingJoined: () => {
      console.log('✅ Meeting joined successfully!');
      console.log('🎉 You are now in the meeting! Camera should be enabled automatically.');
      console.log('📱 You can use Camera/Mic buttons to toggle them');
      setHasJoined(true);
    },
    onParticipantJoined: (participant) => {
      console.log('👥 NEW PARTICIPANT JOINED:', participant.displayName || participant.id);
      console.log('👥 Participant details:', JSON.stringify(participant, null, 2));
    },
    onParticipantLeft: (participant) => {
      console.log('👋 PARTICIPANT LEFT:', participant.displayName || participant.id);
    },
    onMeetingLeft: () => {
      console.log('✅ Meeting left successfully!');
      console.log('🚨 WARNING: Meeting was left - this might be unexpected!');
      console.log('🕐 Time of leave:', new Date().toISOString());
      console.log('🔍 Was this intentional?', preventAutoLeave);

      // Don't auto-rejoin - let's see what's causing the auto-leave first
      setHasJoined(false);
    },
  });
  const participantsArrId = [...participants.keys()];

  // Debug the meeting functions and state
  console.log('Meeting functions available:', {
    join: typeof join,
    leave: typeof leave,
    toggleWebcam: typeof toggleWebcam,
    toggleMic: typeof toggleMic,
    participantsCount: participantsArrId.length,
    localParticipant: localParticipant ? localParticipant.id : 'none'
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#66948a" barStyle="light-content" />
      
      {/* Header */}
      <LinearGradient
        colors={['#66948a', '#66948a']}
        style={styles.header}
      >
        <Text style={styles.headerTitleCentered}>Video Session</Text>
      </LinearGradient>

      <View style={styles.meetingContent}>
        <ParticipantList participants={participantsArrId} />
        <ControlsContainer
          join={join}
          leave={leave}
          toggleWebcam={toggleWebcam}
          toggleMic={toggleMic}
          participantsArrId={participantsArrId}
          localParticipant={localParticipant}
          setPreventAutoLeave={setPreventAutoLeave}
          hasJoined={hasJoined}
        />
      </View>
    </SafeAreaView>
  );
}

export default function VideoMeeting() {
  const [meetingId, setMeetingId] = useState(null);
  const [isVideoSDKAvailable, setIsVideoSDKAvailable] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // Check if VideoSDK is available
    setIsVideoSDKAvailable(!!MeetingProvider && !!createMeeting && !!token);
    console.log("VideoSDK availability check:", {
      MeetingProvider: !!MeetingProvider,
      createMeeting: !!createMeeting,
      token: !!token,
      tokenValue: token
    });

    // Request permissions when component mounts
    requestPermissions();
  }, []);

  const requestPermissions = async () => {
    try {
      // Request camera and microphone permissions explicitly
      console.log('🔐 Requesting camera and microphone permissions...');

      // For React Native, we need to request permissions through the platform
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.CAMERA,
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        ]);

        console.log('📱 Android permissions granted:', granted);

        if (granted['android.permission.CAMERA'] === PermissionsAndroid.RESULTS.GRANTED &&
            granted['android.permission.RECORD_AUDIO'] === PermissionsAndroid.RESULTS.GRANTED) {
          console.log('✅ All permissions granted');
        } else {
          console.log('❌ Some permissions denied');
        }
      } else {
        console.log('📱 iOS permissions will be requested by VideoSDK when needed');
      }
    } catch (error) {
      console.log('❌ Permission request error:', error);
    }
  };

  const getMeetingId = async (id: string | null = null) => {
    console.log("getMeetingId called with:", { id, createMeeting: !!createMeeting, token: !!token });

    if (!createMeeting || !token) {
      console.log("VideoSDK not available - showing alert");
      Alert.alert(
        "VideoSDK Not Available",
        "Video calling requires a development build. Please build the app with native modules enabled.",
        [
          { text: "Go Back", onPress: () => router.back() },
          { text: "Learn More", onPress: () => showVideoSDKInfo() }
        ]
      );
      return;
    }

    try {
      if (id == null) {
        console.log("🔧 Creating new meeting...");
        const meetingId = await createMeeting({ token });
        console.log("✅ Meeting created successfully:", meetingId);
        setMeetingId(meetingId);
      } else {
        console.log("🔧 Using provided meeting ID:", id);
        console.log("🔍 Meeting ID format check:", {
          id,
          length: id.length,
          format: /^[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{4}$/.test(id)
        });
        setMeetingId(id);
      }
    } catch (error) {
      console.log("❌ Meeting creation/join error:", error);
      console.log("❌ Error details:", JSON.stringify(error, null, 2));
      Alert.alert("Error", "Failed to create/join meeting: " + error.message);
    }
  };

  const showVideoSDKInfo = () => {
    Alert.alert(
      "Development Build Required",
      "To use video calling features:\n\n1. Install Expo Dev Client\n2. Build a development build\n3. Install the custom build on your device\n\nThis enables native WebRTC modules required for video calls.",
      [{ text: "OK" }]
    );
  };

  if (!isVideoSDKAvailable) {
    return <VideoSDKNotAvailable />;
  }

  console.log('🔍 VideoMeeting render state:', {
    meetingId,
    hasToken: !!token,
    isVideoSDKAvailable
  });

  return meetingId ? (
    <>
      {console.log('🚀 Creating MeetingProvider with:', { meetingId, token: !!token })}
      <MeetingProvider
        config={{
          meetingId,
          micEnabled: true,  // Try with mic enabled
          webcamEnabled: true, // Try with camera enabled to match web app
          name: "MentalEase Mobile User",
        }}
        token={token}
      >
        <MeetingView />
      </MeetingProvider>
    </>
  ) : (
    <JoinScreen getMeetingId={getMeetingId} />
  );
}

// Component to show when VideoSDK is not available
function VideoSDKNotAvailable() {
  const router = useRouter();

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#66948a" barStyle="light-content" />

      {/* Header */}
      <LinearGradient
        colors={['#66948a', '#66948a']}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Video Consultation</Text>
        <View style={styles.headerSpacer} />
      </LinearGradient>

      <View style={styles.content}>
        <View style={styles.notAvailableContainer}>
          <Text style={styles.notAvailableTitle}>🚧 Development Build Required</Text>
          <Text style={styles.notAvailableText}>
            Video calling features require native WebRTC modules that are not available in Expo Go.
          </Text>
          <Text style={styles.notAvailableSubtext}>
            To enable video calls:
            {'\n'}• Build a development build with EAS
            {'\n'}• Install the custom build on your device
            {'\n'}• This enables native video calling capabilities
          </Text>

          <TouchableOpacity
            style={styles.backToDashboardButton}
            onPress={() => router.back()}
          >
            <Text style={styles.backToDashboardButtonText}>Back to Dashboard</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    paddingTop: 45,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerTitleCentered: {
    textAlign: 'center',
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    width: '100%',
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  welcomeSection: {
    alignItems: 'center',
    marginBottom: 40,
    marginTop: 20,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 10,
    textAlign: 'center',
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: '#7F8C8D',
    textAlign: 'center',
    lineHeight: 24,
  },
  createMeetingButton: {
    borderRadius: 25,
    marginBottom: 30,
    shadowColor: '#4A90E2',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  buttonGradient: {
    paddingVertical: 16,
    borderRadius: 25,
    alignItems: 'center',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 30,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#E0E0E0',
  },
  dividerText: {
    marginHorizontal: 20,
    color: '#999',
    fontSize: 14,
    fontWeight: '500',
  },
  joinSection: {
    alignItems: 'center',
  },
  joinTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: 20,
  },
  meetingInput: {
    width: '100%',
    padding: 16,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    borderRadius: 15,
    fontSize: 16,
    color: '#2C3E50',
    backgroundColor: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 20,
  },
  joinMeetingButton: {
    backgroundColor: '#66948a',
    paddingVertical: 14,
    paddingHorizontal: 40,
    borderRadius: 25,
  },
  joinButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  meetingContent: {
    flex: 1,
  },
  participantsList: {
    flex: 1,
  },
  participantVideo: {
    height: 300,
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 15,
  },
  noVideoContainer: {
    backgroundColor: '#E8E8E8',
    height: 300,
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noVideoText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  waitingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  waitingText: {
    fontSize: 18,
    color: '#7F8C8D',
    textAlign: 'center',
    lineHeight: 26,
  },
  waitingSubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 10,
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  controlButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 20,
    minWidth: 70,
    alignItems: 'center',
  },
  controlButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  notAvailableContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  notAvailableTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C3E50',
    textAlign: 'center',
    marginBottom: 20,
  },
  notAvailableText: {
    fontSize: 16,
    color: '#7F8C8D',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 20,
  },
  notAvailableSubtext: {
    fontSize: 14,
    color: '#7F8C8D',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 40,
  },
  backToDashboardButton: {
    backgroundColor: '#66948a',
    paddingVertical: 14,
    paddingHorizontal: 30,
    borderRadius: 25,
  },
  backToDashboardButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
