import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { to, subject, html } = await req.json()

    // SMTP configuration using your Gmail settings
    const smtpConfig = {
      hostname: "smtp.gmail.com",
      port: 587,
      username: Deno.env.get('SMTP_USERNAME') || '<EMAIL>',
      password: Deno.env.get('SMTP_PASSWORD'), // Your app password
    }

    // Create email content
    const emailContent = `From: MentalEase <${smtpConfig.username}>
To: ${to}
Subject: ${subject}
Content-Type: text/html; charset=utf-8

${html}`

    // Send email using SMTP
    const conn = await Deno.connect({
      hostname: smtpConfig.hostname,
      port: smtpConfig.port,
    })

    const encoder = new TextEncoder()
    const decoder = new TextDecoder()

    // SMTP conversation
    await conn.write(encoder.encode(`EHLO localhost\r\n`))
    await conn.write(encoder.encode(`STARTTLS\r\n`))
    await conn.write(encoder.encode(`AUTH LOGIN\r\n`))
    
    // Base64 encode credentials
    const usernameB64 = btoa(smtpConfig.username)
    const passwordB64 = btoa(smtpConfig.password || '')
    
    await conn.write(encoder.encode(`${usernameB64}\r\n`))
    await conn.write(encoder.encode(`${passwordB64}\r\n`))
    
    await conn.write(encoder.encode(`MAIL FROM:<${smtpConfig.username}>\r\n`))
    await conn.write(encoder.encode(`RCPT TO:<${to}>\r\n`))
    await conn.write(encoder.encode(`DATA\r\n`))
    await conn.write(encoder.encode(`${emailContent}\r\n.\r\n`))
    await conn.write(encoder.encode(`QUIT\r\n`))

    conn.close()

    return new Response(
      JSON.stringify({ success: true, message: 'Email sent successfully' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    console.error('Error sending email:', error)
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      },
    )
  }
})
