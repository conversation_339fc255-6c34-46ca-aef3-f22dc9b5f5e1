import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';

export default function AssessmentIndex() {
  const router = useRouter();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Mental Health Assessment</Text>
      <Text style={styles.subtitle}>Take a comprehensive assessment to understand your mental health</Text>
      
      <TouchableOpacity 
        style={styles.startButton}
        onPress={() => router.push('/(main)/mental-health/assessment/questions')}
      >
        <Text style={styles.startButtonText}>Start Assessment</Text>
      </TouchableOpacity>

      <TouchableOpacity 
        style={styles.resultsButton}
        onPress={() => router.push('/(main)/mental-health/assessment/results')}
      >
        <Text style={styles.resultsButtonText}>View Previous Results</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 30,
  },
  startButton: {
    backgroundColor: '#66948a',
    padding: 15,
    borderRadius: 10,
    marginBottom: 15,
    alignItems: 'center',
  },
  startButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  resultsButton: {
    backgroundColor: '#f5f5f5',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  resultsButtonText: {
    color: '#333',
    fontSize: 16,
    fontWeight: '500',
  },
});
